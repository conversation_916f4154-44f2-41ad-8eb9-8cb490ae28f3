const passport = require('passport');
const GoogleStrategy = require('passport-google-oauth20').Strategy;
const mysql = require('mysql2');
require('dotenv').config();
const keys = require('./keys'); // Ensure this file contains your Google Client ID and Secret

const pool = mysql.createPool({
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  database: process.env.DB_NAME || 'arab_cbt',
  password: process.env.DB_PASSWORD || 'Mn773738',
});

passport.use(new GoogleStrategy({
  clientID: keys.googleClientID,
  clientSecret: keys.googleClientSecret,
  callbackURL: '/auth/google/callback',
},
async (accessToken, refreshToken, profile, done) => {
  // Check if user already exists in our db
  pool.query('SELECT * FROM users WHERE google_id = ?', [profile.id], (err, results) => {
    if (err) throw err;
    if (results.length > 0) {
      // User exists
      done(null, results[0]);
    } else {
      // User does not exist, create a new one
      const newUser = {
        google_id: profile.id,
        username: profile.displayName,
        profile_pic: profile._json.picture,
      };
      pool.query('INSERT INTO users SET ?', newUser, (err, results) => {
        if (err) throw err;
        done(null, newUser);
      });
    }
  });
}));

passport.serializeUser((user, done) => {
  done(null, user.google_id);
});

passport.deserializeUser((id, done) => {
  pool.query('SELECT * FROM users WHERE google_id = ?', [id], (err, results) => {
    if (err) throw err;
    done(null, results[0]);
  });
});
