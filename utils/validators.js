const { body, validationResult } = require('express-validator');

// Validation middleware for user registration
const registerUserValidators = [
    body('name')
        .notEmpty().withMessage('Name is required')
        .isString().withMessage('Name must be a string')
        .isLength({ min: 2, max: 100 }).withMessage('Name must be between 2 and 100 characters'),

    body('nickname')
        .optional()
        .isString().withMessage('Nickname must be a string')
        .isLength({ max: 100 }).withMessage('Nickname can be up to 100 characters'),

    body('email')
        .isEmail().withMessage('Invalid email format')
        .normalizeEmail()
        .isLength({ max: 100 }).withMessage('Email can be up to 100 characters'),

    body('password')
        .notEmpty().withMessage('Password is required')
        .isString().withMessage('Password must be a string')
        .isLength({ min: 6 }).withMessage('Password must be at least 6 characters'),

    body('role')
        .optional()
        .isInt({ min: 1 }).withMessage('Role must be a positive integer')
        .toInt(),
];

const loginUserValidators = [
    body('email').isEmail().withMessage('Email is invalid'),
    body('password').notEmpty().withMessage('Password is required'),
];


// Middleware to check validation results
const validate = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
    }
    next(); // Pass control to the next middleware
};


module.exports = {
    registerUserValidators,
    validate,
    loginUserValidators,
};
