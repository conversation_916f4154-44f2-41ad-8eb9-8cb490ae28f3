const pool = require('../config/db');

async function checkTableExist(tableName, createTableQuery) {
    try {
        const query = `SHOW TABLES LIKE ?`;
        const [rows] = await pool.query(query, [tableName]);
        
        if (rows.length === 0) {
            // If the table doesn't exist, create it
            await pool.query(createTableQuery);
            console.log(`Table '${tableName}' created successfully.`);
        } else {
            console.log(`Table '${tableName}' already exists.`);
        }
    } catch (error) {
        console.error(`Error checking or creating table '${tableName}':`, error.message);
        throw error;
    }
}

module.exports = { checkTableExist };
