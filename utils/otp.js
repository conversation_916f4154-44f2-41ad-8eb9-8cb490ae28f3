const nodemailer = require('nodemailer');
const dotenv = require('dotenv');

dotenv.config();

/*────────── generate 6-digit code ──────────*/
function generateOtp(length = 6) {
  return Math.random().toString().slice(2, 2 + length);
}

/*────────── send via e-mail ──────────*/
async function sendOtpEmail(to, otp) {
  const transporter = nodemailer.createTransport({
    host: process.env.SMTP_HOST,
    port: process.env.SMTP_PORT,
    secure: true,
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS,
    },
  });

  await transporter.sendMail({
    from: process.env.FROM_EMAIL,
    to,
    subject: 'Your one-time login code',
    html: `<p>Your OTP: <b>${otp}</b> (valid for 10 minutes)</p>`,
  });
}

module.exports = { generateOtp, sendOtpEmail };