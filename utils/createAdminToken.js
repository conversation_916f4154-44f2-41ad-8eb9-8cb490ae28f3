const jwt = require('jsonwebtoken');
require('dotenv').config();

// Function to create a JWT token for an admin
const createAdminToken = (admin) => {
  const token = jwt.sign(
    {
      id: admin.id,
      role: 'admin',  // Assign 'admin' role in the payload
    },
    process.env.JWT_SECRET,  // Secret key from environment variables
    { expiresIn: '24h' }  // Token expiration
  );
  return token;
};

module.exports = createAdminToken;
