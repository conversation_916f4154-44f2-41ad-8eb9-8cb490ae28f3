const express = require("express");
const router = express.Router();
const videosController = require("../controllers/videosController");
const { SingleImage, MultiFiles } = require("../middleware/uploadMiddleware"); // ✅ Correct import
const { authenticateUser, videoPurchasesAuth } = require("../middleware/authMiddleware");
const purchaseVideoController = require("../controllers/videosPruchaseController");
// Routes
router.get("/", videosController.getAllVideos);
router.post("/create", MultiFiles("treatment", "image"), videosController.createVideo);
router.get("/:id", videosController.getVideoById);
router.put("/update/:id", SingleImage('treatment', 'image'), videosController.updateVideo);
router.put("/is-active/:id", videosController.updateVideoStatus);
router.delete("/delete/:id", videosController.deleteTreatmentVideo);

router.get("/treatment-video/:id", videosController.getSubVideosByVideoId);
router.post("/treatment-video/:id", videosController.createSubVideo);
router.put("/update-sub/:id", videosController.updateSubVideo);
router.delete("/treatment-video/:id", videosController.deleteSubCategory);

router.get("/treatment-item/item/:id", videosController.getItemsBySubCategoryId);
router.post("/treatment-item/item", MultiFiles("treatment", [{ name: "file", maxCount: 1 }, { name: "image", maxCount: 1 }]), videosController.createItem);
router.put("/treatment-item/item/:id", MultiFiles("treatment", [{ name: "file", maxCount: 1 }, { name: "image", maxCount: 1 }]), videosController.updateItem);
router.delete("/treatment-item/item/:id", videosController.deleteItem);

// Purchases
router.get('/video-purchase', videoPurchasesAuth, purchaseVideoController.checkVideoPurchase);
router.post('/purchases', authenticateUser, purchaseVideoController.purchaseVideo);

module.exports = router;
