const express = require("express");
const router = express.Router();
const lecturesController = require("../controllers/lecturesController");
const { SingleVideo, SingleImage, MultiFiles } = require("../middleware/uploadMiddleware");

// ✅ Setup multer instances
const uploadImage = SingleImage("lectures", "image");
// const uploadVideo = SingleVideo("lectures", "video");
const uploadVideoFile = SingleImage("lectures", "video");

// ✅ Main Lectures (Categories)
router.get("/", lecturesController.getAllLectures); // Get all main lectures (with sub-lectures)
router.post("/create", uploadImage, lecturesController.createLecture); // Create main lecture (convert to .webm)
router.get("/:id", lecturesController.getLectureById); // Get lecture by ID
router.put("/update/:id", uploadImage, lecturesController.updateLecture); // Update lecture with optional new video
router.put("/is-active/:id", lecturesController.updateLectureStatus); // Toggle active status
router.delete("/delete/:id", lecturesController.deleteLecture); // Delete main lecture

// ✅ Sub-Lectures (Videos under a lecture)
router.get("/lecture-sub-video/:id", lecturesController.getSubLecturesByLectureId); // Get all sub-lectures under lecture
router.post("/create-sub-video/:id", uploadVideoFile, lecturesController.createSubLecture); // Create sub-lecture (convert to .webm)
router.put("/update-sub/:id", uploadVideoFile, lecturesController.updateSubLecture); // Update sub-lecture
router.delete("/delete-sub/:id", lecturesController.deleteSubLecture); // Delete sub-lecture

// ✅ Optional: Get sub-lectures by lecture text (URL-encoded)
router.get("/sub-by-text/:text", lecturesController.getSubLecturesByLectureText);

module.exports = router;
