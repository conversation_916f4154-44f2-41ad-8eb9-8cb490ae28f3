const express = require("express");
const router = express.Router();
const FeatureController = require("../controllers/featuresController");
const { adminAuthAccess } = require("../middleware/authMiddleware");

router.get("/", FeatureController.getAllFeatures);
router.post("/", adminAuthAccess, FeatureController.createFeature);
router.put("/:id", adminAuthAccess, FeatureController.updateFeature);
router.put("/:id/status", adminAuthAccess, FeatureController.toggleFeatureStatus);
router.delete("/:id", adminAuthAccess, FeatureController.deleteFeature);

module.exports = router;
