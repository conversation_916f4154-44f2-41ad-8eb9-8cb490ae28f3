const express = require("express");
const router = express.Router();
const thoughtController = require("../controllers/thoughtController");
const { SingleFile } = require("../middleware/uploadMiddleware"); // ✅ Correct import

// ✅ Initialize Multer for single audio uploads in 'thoughts'
const uploadSingleAudio = SingleFile("thoughts", "audio");

// Create a new thought
router.post("/", uploadSingleAudio, thoughtController.createThought);

// Get a thought by ID
router.get("/:id", thoughtController.getById);

// get audio by thought id
router.get("/audio/:id", thoughtController.getAudioByThoughtId);

// get terments by thought belongs to
router.get("/treatments/by-belongs-to/:belongs_to", thoughtController.getTermentByBelongsTo);

// Get all thoughts
router.get("/", thoughtController.getAllThoughts);

// Update a thought by ID
router.put("/:id", uploadSingleAudio, thoughtController.updateThought);

// Delete a thought by ID
router.delete("/:id", thoughtController.deleteThought);

module.exports = router;
