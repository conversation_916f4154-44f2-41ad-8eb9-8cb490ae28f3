const express = require('express');
const router = express.Router();  // Create a router instance
const roleController = require('../controllers/roleController');

router.get('/', roleController.getAllRoles);
// create a new role
router.post('/create', roleController.createRole);
// update a role
router.put('/update/:id', roleController.updateRole);
// delete a role
router.delete('/delete/:id', roleController.deleteRole);





module.exports = router