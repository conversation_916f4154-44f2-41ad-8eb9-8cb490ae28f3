const express = require('express');
const router = express.Router();
const audioClipController = require('../controllers/audioClipController');
const { SingleFile } = require('../middleware/uploadMiddleware'); // ✅ Correct import

// ✅ Initialize Multer for single audio uploads in 'audio_clips'
const uploadSingleAudio = SingleFile("audio_clips", "audio");

router.post('/', uploadSingleAudio, audioClipController.createAudioClip);
router.get('/:id', audioClipController.getAudioClipById);
router.get('/', audioClipController.getAllAudioClips);
router.put('/:id', uploadSingleAudio, audioClipController.updateAudioClip); // allowing optional audio file update
router.delete('/:id', audioClipController.deleteAudioClip);

module.exports = router;
