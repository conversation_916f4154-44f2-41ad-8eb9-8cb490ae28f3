const express = require('express');
const router = express.Router();
const questionController = require('../controllers/questionController');

// Routes for questions
router.get('/thought/:thoughtId', questionController.getQuestionsByThoughtId); // Fetch all questions for a specific thought
router.post('/', questionController.createQuestions); // Create new questions
router.put('/update/:questionId', questionController.updateQuestion);
router.get('/:questionId', questionController.getQuestionsByThoughtId); // Get a question by ID
router.delete('/:questionId', questionController.deleteQuestion); // Delete a question by ID

module.exports = router;

