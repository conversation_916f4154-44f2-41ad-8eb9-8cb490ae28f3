const express = require("express");
const router = express.Router();
const BookPurchaseController = require("../controllers/BookPurchaseController");
const { authenticateUser } = require("../middleware/authMiddleware");

// ✅ Check if user has purchased a book (expects user_id & book_id in query)
router.get("/check-book-purchase", BookPurchaseController.checkBookPurchase);

// ✅ Make a new purchase
router.post("/purchased", authenticateUser, BookPurchaseController.purchaseBook);
// 
router.get('/admin/purchases', BookPurchaseController.getAdminPurchases); // ✅ This must match
router.get('/summary', BookPurchaseController.getRevenueSummary);

module.exports = router;