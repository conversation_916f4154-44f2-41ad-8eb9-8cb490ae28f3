const express = require('express');
const router = express.Router();
const paymentController = require('../controllers/paymentController');

router.post('/', paymentController.createPaymentMethod);
router.get('/', paymentController.getAllPaymentMethods);
router.get('/:id', paymentController.getPaymentMethodById);
router.put('/:id', paymentController.updatePaymentMethod);
router.delete('/:id', paymentController.deletePaymentMethod);

module.exports = router;