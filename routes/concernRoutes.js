const express = require("express");
const router = express.Router();
const concernController = require("../controllers/concernController");
const { SingleFile } = require("../middleware/uploadMiddleware"); // ✅ Correct import

// ✅ Initialize Multer for single image uploads in 'concerns'
const uploadSingleImage = SingleFile("concerns", "image");

router.post("/", uploadSingleImage, concernController.createConcern);
router.get("/:id", concernController.getConcernById);
router.get("/", concernController.getAllConcerns);
router.put("/is-active/:id", concernController.activateConcern);
router.put("/:id", uploadSingleImage, concernController.updateConcern); // Allow optional image upload
router.delete("/:id", concernController.deleteConcern);

module.exports = router;
