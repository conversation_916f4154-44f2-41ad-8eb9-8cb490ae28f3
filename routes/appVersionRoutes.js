const express = require('express');
const router = express.Router();
const appVersionController = require('../controllers/appVersionController');

// POST - Create new version
router.post('/', appVersionController.createVersion);

// PUT - Update existing version
router.put('/:id', appVersionController.updateVersion);

// DELETE - Delete version by ID
router.delete('/:id', appVersionController.deleteVersion);

// GET - All versions
router.get('/', appVersionController.getAllVersions);

module.exports = router;
