const express = require("express");
const topicController = require("../controllers/topicController");
const { SingleFile } = require("../middleware/uploadMiddleware");

const router = express.Router();

// ✅ Setup for single image upload to /uploads/topics
const uploadSingleImage = SingleFile("topics", "image");

// ✅ Create topic (with optional parent_id for sub-categories)
router.post("/", uploadSingleImage, topicController.createTopic);

// ✅ Choose topics for user
router.post("/userChoose/:id", topicController.userChooseTopics);

// ✅ Create notifications for a topic
router.post("/create-notification/:id", topicController.createTopicNotification);

// ✅ Get all topics
router.get("/", topicController.getAllTopics);

// ✅ Get notifications for a specific topic
router.get('/:id/notifications', topicController.getTopicNotifications);

// ✅ Get a topic by ID
router.get("/:id", topicController.getTopicById);

// ✅ Get topics selected by a specific user
router.get("/userTopics/:id", topicController.getUserTopics);

// ✅ Update topic (name, type, parent_id, image)
router.put("/:id", uploadSingleImage, topicController.updateTopic);

// ✅ Update user's selected topics
router.put("/userTopics/:id", topicController.updateUserTopicById);

// ✅ Update topic notification
router.put('/edit/:id', topicController.editTopicNotification);

// ✅ Activate or deactivate topic
router.put("/:id/toggle", topicController.toggleTopicStatus);

// ✅ Delete topic
router.delete("/:id", topicController.deleteTopic);

module.exports = router;
