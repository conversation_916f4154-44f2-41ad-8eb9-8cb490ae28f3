const { Router } = require("express");
const authRoutes = require("./authRoutes");
const clinicRoutes = require("./clinicRoutes");
const roleRoutes = require("./roleRoutes");
const subscriptionRoutes = require("./ subscription.router");
const paymentRoutes = require("./paymentRoutes");
const FeatureRouter = require("./feature.router");
const supportRoutes = require("./supportRoutes");
const bookRoutes = require("./booksRoutes");
const booksRouter = require("./books.router");
const videosRouter = require("./videosRouter");
const TopicRoutes = require("./topicRoutes");
const ConcernRoutes = require("./concernRoutes");
const ThoughtRoutes = require("./thoughtRoutes");
const questionsRouter = require("./questionRoutes");
const answerRouter = require("./answerRoutes");
const distractionRoutes = require("./distractionRoutes");
const meditationRoutes = require("./meditationRoutes");
const lecturesRouter = require("./lecturesRoutes");
const discountRoutes = require("./discountRoutes");
const chantingRouter = require("./chantingRoutes");
const AppVersionRouter = require("./appVersionRoutes");
const ComparisonHistoryRouter = require("./ComparisonHistory.router");
const db = require("../config/db");

const router = Router();

router.use("/clinics", clinicRoutes);
router.use("/auth", authRoutes);
router.use("/roles", roleRoutes);
router.use("/subscriptions", subscriptionRoutes);
router.use("/features", FeatureRouter);
router.use("/support", supportRoutes);
router.use("/books", bookRoutes);
router.use("/purchase-books", booksRouter);
router.use("/videos", videosRouter);
router.use("/topics", TopicRoutes);
router.use("/concerns", ConcernRoutes);
router.use("/thoughts", ThoughtRoutes);
router.use("/questions", questionsRouter);
router.use("/answers", answerRouter);
router.use("/distraction", distractionRoutes);
router.use("/meditation", meditationRoutes);
router.use("/lectures", lecturesRouter);
router.use("/discount", discountRoutes);
router.use("/chanting", chantingRouter);
router.use("/app-version", AppVersionRouter);
router.use("/features", FeatureRouter);
router.use("/payment-methods", paymentRoutes);
router.use("/comparison-history", ComparisonHistoryRouter);


router.post("/upload-csv-questions", (req, res) => {
    try {
        const { data } = req.body;

        if (!data || data.length === 0) {
            return res.status(400).json({ error: "No data received." });
        }

        // Prepare query
        const values = data.map(row => [
            row.thought_id,
            row.question_text,
            row.is_question,
            row.is_show,
            row.yes_route_id || null,
            row.no_route_id || null,
            row.question_type
        ]);

        const sql = `
            INSERT INTO questions (thought_id, question_text, is_question, is_show, yes_route_id, no_route_id, question_type) 
            VALUES ?
        `;

        db.query(sql, [values], (err, result) => {
            if (err) {
                console.error("Database Insert Error:", err);
                return res.status(500).json({ error: "Database error." });
            }
            res.status(200).json({ message: "CSV uploaded successfully!", inserted: result.affectedRows });
        });

    } catch (error) {
        console.error("Server Error:", error);
        res.status(500).json({ error: "Internal server error." });
    }

});


module.exports = router;