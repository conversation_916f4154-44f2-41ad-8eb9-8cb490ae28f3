const express = require("express");
const router = express.Router();
const distractionController = require("../controllers/distractionController");
const { MultiFiles } = require("../middleware/uploadMiddleware"); // ✅ Correct import

// ✅ Initialize Multer for distractions (image & audio uploads)
const uploadDistractionImage = MultiFiles("distractions", [{ name: "image", maxCount: 1 }]);
const uploadDistractionAudio = MultiFiles("distractions", [{ name: "audio_url", maxCount: 1 }]);

// Validation Middleware for ID
const validateIdParam = (req, res, next) => {
    const { id } = req.params;
    if (!id || isNaN(id)) {
        return res.status(400).json({ message: "Invalid or missing ID parameter." });
    }
    next();
};

// Route to create a distraction
router.post("/create", uploadDistractionImage, distractionController.createDistraction);

// Route to update the audio of a distraction
router.post("/create-audio/:id", validateIdParam, uploadDistractionAudio, distractionController.createSubDistraction);

// Route to fetch all distractions
router.get("/", distractionController.getAll);

// Route to fetch all sub distractions
router.get("/sub/:id", distractionController.getAllSubDistraction);

// Route to fetch a single distraction by ID
router.get("/:id", validateIdParam, distractionController.getDistractionById);

// Route to update a distraction
router.put("/update/:id", validateIdParam, uploadDistractionImage, distractionController.updateDistractionById);

// Route to update the audio of a distraction
router.put("/update-audio/:id", validateIdParam, uploadDistractionAudio, distractionController.updateSubDistractionById);

// Route to delete a distraction
router.delete("/delete/:id", validateIdParam, distractionController.deleteDistractionById);

// Route to delete a sub distraction
router.delete("/delete-audio/:id", validateIdParam, distractionController.deleteSubDistractionById);

module.exports = router;
