const express = require("express");
const discountController = require("../controllers/discountController");

const router = express.Router();

// Route to add a discount option
router.post("/", discountController.addDiscountOption);

// Route to get all discount options
router.get("/", discountController.getDiscountOptions);

// Route to update a discount option
router.put("/update", discountController.updateDiscountOption);

router.delete("/delete/:id", discountController.deleteDiscountOption);

module.exports = router;
