const express = require("express");
const router = express.Router();
const booksController = require("../controllers/booksController");
const { MultiFiles, SingleFile } = require("../middleware/uploadMiddleware");

// ✅ Initialize Multer middleware properly
const uploadBookImages = MultiFiles("books", [
    { name: "coverImage", maxCount: 1 },
    { name: "secondaryImage", maxCount: 1 },
]);

const uploadBookPages = MultiFiles("book_pages", [{ name: "pages", maxCount: 1000 }]);
const uploadSinglePage = SingleFile("book_pages", "page");

// ✅ Apply Multer **ONLY** to routes that require file uploads
router.post("/add", uploadBookImages, booksController.addBook);
router.post("/add-pages/:id", uploadBookPages, booksController.addPages);
router.put("/:id", uploadBookImages, booksController.updateBook);
router.put("/pages/:id", uploadSinglePage, booksController.updatePage);
router.put("/:id/status", booksController.updateBookStatus);

// ❌ No Multer needed for GET requests
router.get("/:id", booksController.getBookById);
router.get("/", booksController.getAllBooks);
router.get("/get-pages/:book_id", booksController.getBookPages);
router.get("/pages/:id", booksController.getPagesById);



module.exports = router;
