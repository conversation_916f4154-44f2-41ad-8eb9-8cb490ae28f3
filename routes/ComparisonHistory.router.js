const express = require('express');
const router = express.Router();
const ComparisonHistoryController = require('../controllers/ComparisonHistoryController');
const { authenticateUser } = require('../middleware/authMiddleware');

// GET for authenticated user
router.get('/:userId', authenticateUser, ComparisonHistoryController.getAllComparisonHistoryByUserId);

// POST create new entry
router.post('/:thoughtId', authenticateUser, ComparisonHistoryController.createComparisonHistoryAudio);

// PUT update after_score
router.put('/:thoughtId/after-score', authenticateUser, ComparisonHistoryController.updateComparisonHistoryById);

// PUT update after_score_audio
router.put('/:thoughtId/after-score-audio', authenticateUser, ComparisonHistoryController.updateComparisonHistoryAudioById);

module.exports = router;