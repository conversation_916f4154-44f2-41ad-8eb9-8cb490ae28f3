const express = require('express');
const router = express.Router();
const chantingController = require('../controllers/chantingController');

// Create a new chanting
router.post('/', chantingController.createChanting);

// Get active chantings by user ID
router.get('/:user_id', chantingController.getUserChantings);


// Delete chanting
router.delete('/:id', chantingController.deleteChanting);

module.exports = router;
