const express = require('express');
const router = express.Router();  
const authController = require('../controllers/authController'); // Ensure this path is correct
const { authenticateUser } = require('../middleware/authMiddleware');
const authAdmin = require('../middleware/authAdmin');
const authSuperAdmin = require('../middleware/authSuperAdmin');

// Example of a correct route definition
router.get('/', authController.getAllUsers);
router.get('/profile/:id', authenticateUser, authController.getUserProfile);
router.get('/:id', authenticateUser, authController.getUserById);
router.post('/register', authController.registerUser);
router.post('/login', authController.loginUser);
router.get('/validate-token', authenticateUser, authController.validateUserToken);

// Check this line for the correct handler function   
router.put('/update/:id', authenticateUser, authController.updateUser); // This should be defined
router.put('/toggle-status/:id', authController.toggleUserStatus);
router.post('/logout', authenticateUser, authController.logoutUser); // This should be defined
router.delete('/delete/:id', authController.deleteUser); // This should be defined

// change password
router.put('/:id/change-password', authenticateUser, authController.changePassword);

// send forgot password email
router.post('/forgot-password', authController.forgotPassword);

// reset password
router.put('/reset-password/:token', authController.resetPassword);

// update the user role
router.put('/update/role/:id', authAdmin, authController.updateUserRoleById);
router.put('/update/admin/:id', authSuperAdmin, authController.updateAdminRoleByUserId);
module.exports = router;
