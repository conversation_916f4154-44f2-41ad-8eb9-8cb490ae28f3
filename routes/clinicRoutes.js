const express = require('express');
const router = express.Router();  // Create a router instance
const clinicController = require('../controllers/clinicController');
const adminAuthAccess = require('../middleware/authAdmin');

router.post('/register', clinicController.createClinic); 
router.get('/', clinicController.getAllClinics); 
router.get('/:id', clinicController.getClinicById); 
router.put('/update/:id', adminAuthAccess, clinicController.updateClinic); // Fixed this line
router.delete('/delete/:id', adminAuthAccess, clinicController.deleteClinic);

module.exports = router;
