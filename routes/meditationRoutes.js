const express = require("express");
const router = express.Router();
const { SingleAudio, MultiFiles } = require("../middleware/uploadMiddleware");
const meditationController = require("../controllers/meditationController");


const uploadMeditationImage = MultiFiles("meditation", [{name: "image", maxCount: 1}, {name: "secondry_image", maxCount: 1}]);
const uploadMeditationAudio = SingleAudio("meditation", "audio_url");

router.post("/", uploadMeditationImage, meditationController.createMeditation);
router.get("/:id", meditationController.getMeditationById);
router.get('/', meditationController.getAllMeditation);
router.put("/update/:id", uploadMeditationImage, meditationController.updateMeditation);
router.delete("/delete/:id", meditationController.deleteMeditation);




router.post("/sub/:meditationId", uploadMeditationAudio, meditationController.createSubMeditation);
router.get("/sub/:meditationId", meditationController.getAllSubMeditation);
router.get("/sub-meditation-by-id/:id", meditationController.getSubByMeditationId);
router.put("/update-sub/:id", uploadMeditationAudio, meditationController.updateSubMeditation);
router.delete("/delete-sub/:id", meditationController.deleteSubMeditation);

module.exports = router;