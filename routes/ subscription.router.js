const express = require("express");
const router = express.Router();
const SubscriptionController = require("../controllers/subscriptionController");

// 📦 إدارة الباقات (Admin only)
router.get("/", SubscriptionController.getAllPackagesWithDetails);
router.get("/plans", SubscriptionController.getAllPackagesPlans);
router.post("/", SubscriptionController.createPackage);
router.put("/:id", SubscriptionController.updatePackage);
router.put("/:id/status", SubscriptionController.togglePackageStatus);

// إدارة الميزات لكل باقة
router.get("/:package_id/features", SubscriptionController.getFeaturesByPackageId);
router.post("/:package_id/features", SubscriptionController.addFeatureToPackage);
router.put('/features/:id', SubscriptionController.updateFeature);
router.put("/features/:id/status", SubscriptionController.toggleFeatureStatus);
router.delete('/features/:id', SubscriptionController.deleteFeature);

router.get("/:id", SubscriptionController.getPackageById);

// 🧑‍💻 اشتراك المستخدم في باقة
router.post("/subscribe", SubscriptionController.subscribeToPackage);


// 🧑‍💻 استعلام حالة الاشتراك للمستخدم
router.post("/checkUserServicePurchase", SubscriptionController.checkUserServicePurchase);
router.get("/check-user-subscription", SubscriptionController.checkUserSubscription);
router.post("/has-purchased", SubscriptionController.hasUserPurchased);

// 🧑‍💻 استعلام الباقة الفعّالة للمستخدم
router.get("/active", SubscriptionController.getActiveSubscription);




module.exports = router;
