const Joi = require("joi");

const create = {
  params: Joi.object().keys({}),
  body: Joi.object()
    .keys({
      userId: Joi.number().required(),
      type: Joi.string().valid("music", "videos").required(),
      favoriteId: Joi.number().required(),
    })
    .options({ abortEarly: false }),
};

const userFavorite = {
  params: Joi.object().keys({ userId: Joi.number().required() }),
};

const update = {
  params: Joi.object().keys({
    id: Joi.number().required(),
  }),
  body: Joi.object()
    .keys({
      userId: Joi.number().required(),
      type: Joi.string().valid("music", "videos").required(),
      favoriteId: Joi.number().required(),
    })
    .min(1),
};

module.exports = {
  create,
  update,
  userFavorite,
};