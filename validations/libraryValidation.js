const Joi = require("joi");

const create = {
  params: Joi.object().keys({}),
  body: Joi.object()
    .keys({
      userId: Joi.number().required(),
      type: Joi.string().valid("books", "sentences", "videos").required(),
      libraryId: Joi.number().required(),
    })
    .options({ abortEarly: false }),
};

const getUserLibrary = {
  params: Joi.object().keys({ userId: Joi.number().required() }),
};

const update = {
  params: Joi.object().keys({
    id: Joi.number().required(),
  }),
  body: Joi.object()
    .keys({
      userId: Joi.number().required(),
      type: Joi.string().valid("books", "sentences", "videos").required(),
      libraryId: Joi.number().required(),
    })
    .min(1),
};

module.exports = {
  create,
  update,
  getUserLibrary,
};