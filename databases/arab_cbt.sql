-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.0
-- https://www.phpmyadmin.net/
--
-- Host: localhost:8889
-- Generation Time: Jan 29, 2025 at 02:03 PM
-- Server version: 5.7.39
-- PHP Version: 8.2.0

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `arab_cbt`
--

-- --------------------------------------------------------

--
-- Table structure for table `admins`
--

CREATE TABLE `admins` (
  `id` int(11) NOT NULL,
  `clinic_name` varchar(255) NOT NULL,
  `address_clinic` varchar(255) NOT NULL,
  `email_clinic` varchar(255) NOT NULL,
  `phone_clinic` varchar(20) NOT NULL,
  `password` varchar(255) NOT NULL,
  `link_whatsapp` varchar(255) DEFAULT NULL,
  `link_telegram` varchar(255) DEFAULT NULL,
  `link_instagram` varchar(255) DEFAULT NULL,
  `link_youtube` varchar(255) DEFAULT NULL,
  `link_facebook` varchar(255) DEFAULT NULL,
  `link_twitter` varchar(255) DEFAULT NULL,
  `link_tiktok` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `admins`
--

INSERT INTO `admins` (`id`, `clinic_name`, `address_clinic`, `email_clinic`, `phone_clinic`, `password`, `link_whatsapp`, `link_telegram`, `link_instagram`, `link_youtube`, `link_facebook`, `link_twitter`, `link_tiktok`, `created_at`, `updated_at`) VALUES
(1, '\"ساليم كيال\"', '\"يركا \"', '<EMAIL>', '972523773738', '$2a$10$QhF3GFjYPA5CT7fG0XS/N.C4ojLowxHUvQ2EmIicPwCEU.GNul6Jy', '', '', '', '', '', '', '', '2024-11-01 20:46:56', '2024-11-01 20:46:56');

-- --------------------------------------------------------

--
-- Table structure for table `answers`
--

CREATE TABLE `answers` (
  `id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `question_id` int(11) DEFAULT NULL,
  `answer_text` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `audio_clips`
--

CREATE TABLE `audio_clips` (
  `id` int(11) NOT NULL,
  `thought_id` int(11) DEFAULT NULL,
  `audio_url` varchar(255) NOT NULL,
  `duration` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `audio_clips`
--

INSERT INTO `audio_clips` (`id`, `thought_id`, `audio_url`, `duration`, `created_at`, `updated_at`) VALUES
(3, 19, '/uploads/audioClips/1733777375361-838987764.m4a', 328, '2024-12-09 20:49:35', '2024-12-09 20:49:35'),
(4, 20, '/uploads/audioClips/1733861475743-957554210.m4a', 328, '2024-12-10 20:11:15', '2024-12-10 20:11:15'),
(5, 21, '/uploads/audioClips/1733861936682-183316263.m4a', 334, '2024-12-10 20:18:56', '2024-12-10 20:18:56'),
(6, 22, '/uploads/audioClips/1733861936678-686120647.m4a', 357, '2024-12-10 20:18:56', '2024-12-10 20:18:56'),
(7, 23, '/uploads/audioClips/1733861936680-770495489.m4a', 379, '2024-12-10 20:18:56', '2024-12-10 20:18:56'),
(8, 24, '/uploads/audioClips/1733861936683-761130710.m4a', 351, '2024-12-10 20:18:56', '2024-12-10 20:18:56'),
(9, 25, '/uploads/audioClips/1734188754846-161691094.m4a', 325, '2024-12-14 15:05:54', '2024-12-14 15:05:54'),
(10, 26, '/uploads/audioClips/1734188776914-775958968.m4a', 334, '2024-12-14 15:06:16', '2024-12-14 15:06:16'),
(11, 27, '/uploads/audioClips/1734189153344-26574571.m4a', 367, '2024-12-14 15:12:33', '2024-12-14 15:12:33'),
(12, 28, '/uploads/audioClips/1734189202699-389602887.m4a', 337, '2024-12-14 15:13:22', '2024-12-14 15:13:22'),
(13, 29, '/uploads/audioClips/1736235108170-606319299.m4a', 328, '2025-01-07 07:31:48', '2025-01-07 07:31:48'),
(14, 30, '/uploads/thoughts/1736235845621-896958951.m4a', 328, '2025-01-07 07:44:05', '2025-01-07 07:44:05');

-- --------------------------------------------------------

--
-- Table structure for table `books`
--

CREATE TABLE `books` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `image` varchar(255) NOT NULL,
  `is_exists` tinyint(1) DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `books`
--

INSERT INTO `books` (`id`, `name`, `price`, `image`, `is_exists`, `created_at`, `updated_at`) VALUES
(2, 'وداعا للأفكار السلبية كتاب', '150.00', '/uploads/books/1735863348342-609827379.webp', 1, '2024-11-02 13:27:15', '2025-01-03 00:15:48');

-- --------------------------------------------------------

--
-- Table structure for table `clinics`
--

CREATE TABLE `clinics` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `clinic_name` varchar(255) NOT NULL,
  `address_clinic` varchar(255) NOT NULL,
  `email_clinic` varchar(255) NOT NULL,
  `phone_clinic` varchar(20) NOT NULL,
  `link_whatsapp` varchar(255) DEFAULT NULL,
  `link_telegram` varchar(255) DEFAULT NULL,
  `link_instagram` varchar(255) DEFAULT NULL,
  `link_youtube` varchar(255) DEFAULT NULL,
  `link_facebook` varchar(255) DEFAULT NULL,
  `link_twitter` varchar(255) DEFAULT NULL,
  `link_tiktok` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `clinics`
--

INSERT INTO `clinics` (`id`, `user_id`, `clinic_name`, `address_clinic`, `email_clinic`, `phone_clinic`, `link_whatsapp`, `link_telegram`, `link_instagram`, `link_youtube`, `link_facebook`, `link_twitter`, `link_tiktok`, `created_at`, `updated_at`) VALUES
(3, 1, 'سليم كيال', 'يركا', '<EMAIL>', '972523773738', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-11-07 11:07:01', '2024-11-09 23:17:16');

-- --------------------------------------------------------

--
-- Table structure for table `concerns`
--

CREATE TABLE `concerns` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `concerns`
--

INSERT INTO `concerns` (`id`, `name`, `image`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'اخاف من نوبة الهلع', '/uploads/concerns/1736189798387-169983145.webp', 1, '2024-11-08 11:55:05', '2025-01-06 18:56:38'),
(5, 'الخوف من الحيوانات', '/uploads/concerns/1736189853410-437974978.webp', 1, '2024-12-10 19:44:16', '2025-01-06 18:57:33'),
(6, 'الخوف من الأماكن العامة والجديدة', '/uploads/concerns/1736189886412-659778516.webp', 1, '2024-12-10 19:44:28', '2025-01-06 18:58:06'),
(7, 'الخوف من الفحوصات والتدخلات الطبية', '/uploads/concerns/1736189898358-658424896.webp', 1, '2024-12-10 19:44:39', '2025-01-06 18:58:18'),
(8, 'الخوف من الأماكن المكتظة', '/uploads/concerns/1736189910660-339235357.webp', 1, '2024-12-10 19:44:50', '2025-01-06 18:58:30'),
(9, 'الخوف من المستقبل', '/uploads/concerns/1736189923561-136686259.webp', 1, '2024-12-10 19:45:07', '2025-01-06 18:58:43'),
(10, 'الخوف من المواقف الاجتماعية', '/uploads/concerns/1736189945149-311183851.webp', 1, '2024-12-10 19:45:24', '2025-01-06 18:59:05'),
(11, 'الخوف من الامراض النفسيه والجنون', '/uploads/concerns/1736189964909-699592912.webp', 1, '2024-12-10 19:45:36', '2025-01-06 18:59:25'),
(12, 'الخوف من الافكار الوسواسية الغير مرغوبة', '/uploads/concerns/1736189974711-248869920.webp', 1, '2024-12-10 19:45:49', '2025-01-06 18:59:34'),
(13, 'الخوف من الامتحانات', '/uploads/concerns/1736190230218-410908526.webp', 1, '2024-12-10 19:45:59', '2025-01-06 19:03:50'),
(14, 'الخوف من الكوارث الطبيعية', '/uploads/concerns/1736190246642-564529186.webp', 1, '2024-12-10 19:46:18', '2025-01-06 19:04:06'),
(15, 'الخوف من الامراض الجسدية', '/uploads/concerns/1736190829841-676608847.webp', 1, '2024-12-10 19:47:47', '2025-01-06 19:13:49'),
(16, 'الخوف من الأماكن المغلقة', '/uploads/concerns/1736190847073-50791020.webp', 1, '2024-12-10 19:47:58', '2025-01-06 19:14:07'),
(17, 'الخوف من المرتفعات', '/uploads/concerns/1736190860377-28373283.webp', 1, '2024-12-10 19:48:12', '2025-01-06 19:14:20'),
(18, 'الخوف من الأماكن البعيدة', '/uploads/concerns/1736190870426-263417165.webp', 1, '2024-12-10 19:48:26', '2025-01-06 19:14:30'),
(19, 'الخوف من الموت', '/uploads/concerns/1736190885359-560737341.webp', 1, '2024-12-10 19:48:43', '2025-01-06 19:14:45'),
(20, 'الخوف من السفر بالطائرة', '/uploads/concerns/1736191763063-785927143.webp', 1, '2024-12-10 19:48:55', '2025-01-06 19:29:23'),
(21, 'الخوف من الحشرات', '/uploads/concerns/1736191775226-378431655.webp', 1, '2024-12-10 19:49:19', '2025-01-06 19:29:35');

-- --------------------------------------------------------

--
-- Table structure for table `distraction`
--

CREATE TABLE `distraction` (
  `id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `distraction_id` int(11) DEFAULT NULL,
  `audio_url` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `distraction`
--

INSERT INTO `distraction` (`id`, `title`, `description`, `image`, `is_active`, `distraction_id`, `audio_url`, `created_at`, `updated_at`) VALUES
(1, 'نشاطات لتشتيت الأفكار', 'نشاطات لتشتيت الأفكار', '/uploads/distractions/1736941259871-1tzfxtgb2.webp', 1, NULL, NULL, '2024-12-27 19:44:34', '2025-01-15 13:40:59'),
(2, '5 رحلات ارتداء', NULL, NULL, 1, 1, '/uploads/distractions/1736941334846-rv63qtinu.webm', '2024-12-28 19:12:11', '2025-01-15 13:42:16');

-- --------------------------------------------------------

--
-- Table structure for table `features`
--

CREATE TABLE `features` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `features`
--

INSERT INTO `features` (`id`, `name`, `created_at`, `updated_at`) VALUES
(1, 'علاج خوف واحد ومحدد', '2024-12-14 13:32:33', '2024-12-14 13:32:33'),
(2, 'جميع المخاوف والأفكار', '2024-12-14 13:32:48', '2024-12-14 13:32:48'),
(3, 'رسائل دعم 3 مرات في اليوم', '2024-12-14 13:33:02', '2024-12-14 13:33:02'),
(4, '33% تخفيض على العلاج النفسي المسجل', '2024-12-14 13:33:19', '2024-12-14 13:33:19'),
(5, '33% تخفيض على الكتب', '2024-12-14 13:33:35', '2024-12-14 13:33:35'),
(6, 'جلسات الاسترخاء العميق المتقدم', '2024-12-14 13:33:50', '2024-12-14 13:33:50'),
(7, 'أداة تشتيت الأفكار', '2024-12-14 13:34:04', '2024-12-14 13:34:04'),
(8, 'دورات ومحاضرات مجانية', '2024-12-14 13:34:19', '2024-12-14 13:34:19'),
(9, 'أدوات جديدة ومتجددة باستمرار', '2024-12-14 13:34:45', '2024-12-14 13:34:45'),
(10, '50% تخفيض على العلاج النفسي المسجل', '2024-12-14 13:34:59', '2024-12-14 13:34:59'),
(11, '50% تخفيض على الكتب', '2024-12-14 13:35:10', '2024-12-14 13:35:10');

-- --------------------------------------------------------

--
-- Table structure for table `meditation`
--

CREATE TABLE `meditation` (
  `id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `meditation_id` int(11) DEFAULT NULL,
  `audio_url` varchar(255) DEFAULT NULL,
  `type` enum('category','sub_category') DEFAULT 'category',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `meditation`
--

INSERT INTO `meditation` (`id`, `title`, `description`, `image`, `is_active`, `meditation_id`, `audio_url`, `type`, `created_at`, `updated_at`) VALUES
(1, 'الى داخل مدينة ', 'الى داخل مدينة ', '/uploads/meditation/1735601217962-458909929.webp', 1, NULL, NULL, 'category', '2024-12-31 01:26:58', '2024-12-31 01:26:58'),
(2, 'سوق', '', NULL, 1, 1, '/uploads/meditation/1735606659540-42525232.webm', 'sub_category', '2024-12-31 02:57:46', '2024-12-31 03:05:44');

-- --------------------------------------------------------

--
-- Table structure for table `next_questions`
--

CREATE TABLE `next_questions` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `question_id` int(11) NOT NULL,
  `next_question_id` int(11) NOT NULL,
  `is_show` tinyint(1) DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `noti_cat`
--

CREATE TABLE `noti_cat` (
  `id` int(11) NOT NULL,
  `text` varchar(255) NOT NULL,
  `image` varchar(255) NOT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `noti_cat`
--

INSERT INTO `noti_cat` (`id`, `text`, `image`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'التخلص من السلبية والتشاؤم', '/uploads/topics/1730763994037-661319766.png', 1, '2024-11-04 23:33:48', '2024-11-04 23:46:34');

-- --------------------------------------------------------

--
-- Table structure for table `pages`
--

CREATE TABLE `pages` (
  `id` int(11) NOT NULL,
  `book_id` int(11) NOT NULL,
  `page_image` varchar(255) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `pages`
--

INSERT INTO `pages` (`id`, `book_id`, `page_image`, `created_at`, `updated_at`) VALUES
(1, 2, '/uploads/books/1736088367821-544000444.webp', '2025-01-05 12:51:39', '2025-01-05 14:46:07'),
(2, 2, '/uploads/books/1736081471421-379612054.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(3, 2, '/uploads/books/1736081471560-850792515.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(4, 2, '/uploads/books/1736081471722-445678209.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(5, 2, '/uploads/books/1736081471896-34662084.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(6, 2, '/uploads/books/1736081472049-23099720.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(7, 2, '/uploads/books/1736081472172-762577630.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(8, 2, '/uploads/books/1736081472339-959918285.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(9, 2, '/uploads/books/1736081472483-875151496.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(10, 2, '/uploads/books/1736081472632-830183834.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(11, 2, '/uploads/books/1736081472760-640017775.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(12, 2, '/uploads/books/1736081472910-432679738.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(13, 2, '/uploads/books/1736081473067-135890953.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(14, 2, '/uploads/books/1736081473207-399427314.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(15, 2, '/uploads/books/1736081473374-831970640.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(16, 2, '/uploads/books/1736081473550-129251310.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(17, 2, '/uploads/books/1736081473734-925530684.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(18, 2, '/uploads/books/1736081473887-771575932.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(19, 2, '/uploads/books/1736081474025-78719287.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(20, 2, '/uploads/books/1736081474164-761531243.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(21, 2, '/uploads/books/1736081474330-843384147.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(22, 2, '/uploads/books/1736081474500-417428553.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(23, 2, '/uploads/books/1736081474669-108185250.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(24, 2, '/uploads/books/1736081474844-289375452.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(25, 2, '/uploads/books/1736081474972-923536389.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(26, 2, '/uploads/books/1736081475107-10593849.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(27, 2, '/uploads/books/1736081475275-176459642.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(28, 2, '/uploads/books/1736081475449-230846057.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(29, 2, '/uploads/books/1736081475577-361397182.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(30, 2, '/uploads/books/1736081475717-574437851.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(31, 2, '/uploads/books/1736081475880-832252515.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(32, 2, '/uploads/books/1736081476054-7362161.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(33, 2, '/uploads/books/1736081476212-510701458.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(34, 2, '/uploads/books/1736081476367-575434573.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(35, 2, '/uploads/books/1736081476492-33162487.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(36, 2, '/uploads/books/1736081476630-767114564.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(37, 2, '/uploads/books/1736081476791-539989385.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(38, 2, '/uploads/books/1736081476939-376970551.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(39, 2, '/uploads/books/1736081477089-187490317.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(40, 2, '/uploads/books/1736081477226-394111415.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(41, 2, '/uploads/books/1736081477390-621452907.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(42, 2, '/uploads/books/1736081477554-744455251.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(43, 2, '/uploads/books/1736081477701-78388001.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(44, 2, '/uploads/books/1736081477848-982880869.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(45, 2, '/uploads/books/1736081477975-402580802.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(46, 2, '/uploads/books/1736081478112-178664171.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(47, 2, '/uploads/books/1736081478279-306606057.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(48, 2, '/uploads/books/1736081478421-149409327.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(49, 2, '/uploads/books/1736081478564-962738588.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(50, 2, '/uploads/books/1736081478705-539712471.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(51, 2, '/uploads/books/1736081478872-988363565.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(52, 2, '/uploads/books/1736081479013-238135254.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(53, 2, '/uploads/books/1736081479141-935605786.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(54, 2, '/uploads/books/1736081479282-138659351.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(55, 2, '/uploads/books/1736081479442-442527859.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(56, 2, '/uploads/books/1736081479616-46790284.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(57, 2, '/uploads/books/1736081479778-665092804.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(58, 2, '/uploads/books/1736081479925-500149862.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(59, 2, '/uploads/books/1736081480052-763347615.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(60, 2, '/uploads/books/1736081480193-955129113.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(61, 2, '/uploads/books/1736081480350-332311796.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(62, 2, '/uploads/books/1736081480515-660423164.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(63, 2, '/uploads/books/1736081480683-911677847.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(64, 2, '/uploads/books/1736081480823-103097838.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(65, 2, '/uploads/books/1736081480952-759142422.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(66, 2, '/uploads/books/1736081481107-147095679.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(67, 2, '/uploads/books/1736081481272-983729137.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(68, 2, '/uploads/books/1736081481421-732588100.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(69, 2, '/uploads/books/1736081481549-31095061.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(70, 2, '/uploads/books/1736081481706-427938440.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(71, 2, '/uploads/books/1736081481869-213864727.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(72, 2, '/uploads/books/1736081482023-64615523.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(73, 2, '/uploads/books/1736081482150-683996026.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(74, 2, '/uploads/books/1736081482315-591702003.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(75, 2, '/uploads/books/1736081482486-409895321.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(76, 2, '/uploads/books/1736081482643-197292237.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(77, 2, '/uploads/books/1736081482810-818203156.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(78, 2, '/uploads/books/1736081482970-878955842.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(79, 2, '/uploads/books/1736081483095-941426129.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(80, 2, '/uploads/books/1736081483253-280091803.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(81, 2, '/uploads/books/1736081483420-68483311.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(82, 2, '/uploads/books/1736081483563-679578660.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(83, 2, '/uploads/books/1736081483690-484886974.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(84, 2, '/uploads/books/1736081483854-809414401.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(85, 2, '/uploads/books/1736081484043-421879157.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(86, 2, '/uploads/books/1736081484198-233577561.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(87, 2, '/uploads/books/1736081484367-921627371.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(88, 2, '/uploads/books/1736081484549-32539826.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(89, 2, '/uploads/books/1736081484718-66442960.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(90, 2, '/uploads/books/1736081484886-871209952.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(91, 2, '/uploads/books/1736081485024-880494855.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(92, 2, '/uploads/books/1736081485190-73800704.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(93, 2, '/uploads/books/1736081485358-838361281.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(94, 2, '/uploads/books/1736081485509-719964920.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(95, 2, '/uploads/books/1736081485664-319345861.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(96, 2, '/uploads/books/1736081485803-640329634.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(97, 2, '/uploads/books/1736081485968-441065134.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(98, 2, '/uploads/books/1736081486139-455149252.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(99, 2, '/uploads/books/1736081486299-302655969.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(100, 2, '/uploads/books/1736081486445-599100903.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(101, 2, '/uploads/books/1736081486614-330682814.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(102, 2, '/uploads/books/1736081486787-973972941.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(103, 2, '/uploads/books/1736081486950-82409230.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(104, 2, '/uploads/books/1736081487111-496234366.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(105, 2, '/uploads/books/1736081487277-837093799.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(106, 2, '/uploads/books/1736081487421-963590667.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(107, 2, '/uploads/books/1736081487585-946394780.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(108, 2, '/uploads/books/1736081487756-166036151.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(109, 2, '/uploads/books/1736081487955-123868678.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(110, 2, '/uploads/books/1736081488095-314653682.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(111, 2, '/uploads/books/1736081488254-74313207.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(112, 2, '/uploads/books/1736081488420-137604401.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(113, 2, '/uploads/books/1736081488565-814459786.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(114, 2, '/uploads/books/1736081488707-200700373.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(115, 2, '/uploads/books/1736081488866-328319754.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(116, 2, '/uploads/books/1736081489007-601469278.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(117, 2, '/uploads/books/1736081489148-129251948.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(118, 2, '/uploads/books/1736081489307-123164255.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(119, 2, '/uploads/books/1736081489477-536022272.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(120, 2, '/uploads/books/1736081489661-457558976.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(121, 2, '/uploads/books/1736081489845-925547608.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(122, 2, '/uploads/books/1736081489989-609901789.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(123, 2, '/uploads/books/1736081490150-462603691.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(124, 2, '/uploads/books/1736081490293-387782757.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(125, 2, '/uploads/books/1736081490421-193044083.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(126, 2, '/uploads/books/1736081490559-850594586.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(127, 2, '/uploads/books/1736081490718-748003881.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(128, 2, '/uploads/books/1736081490871-654061527.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(129, 2, '/uploads/books/1736081491009-668203437.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(130, 2, '/uploads/books/1736081491148-880511037.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(131, 2, '/uploads/books/1736081491304-386862297.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(132, 2, '/uploads/books/1736081491431-214033092.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(133, 2, '/uploads/books/1736081491573-625723476.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(134, 2, '/uploads/books/1736081491716-70034924.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(135, 2, '/uploads/books/1736081491873-179096295.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(136, 2, '/uploads/books/1736081492045-157619247.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(137, 2, '/uploads/books/1736081492238-879996168.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(138, 2, '/uploads/books/1736081492415-480662548.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(139, 2, '/uploads/books/1736081492564-374909121.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(140, 2, '/uploads/books/1736081492708-285995086.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(141, 2, '/uploads/books/1736081492869-533280804.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(142, 2, '/uploads/books/1736081493031-93529563.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(143, 2, '/uploads/books/1736081493197-581050998.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(144, 2, '/uploads/books/1736081493367-603766215.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(145, 2, '/uploads/books/1736081493511-459939299.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(146, 2, '/uploads/books/1736081493656-147206401.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(147, 2, '/uploads/books/1736081493820-77842506.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(148, 2, '/uploads/books/1736081493988-597959242.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(149, 2, '/uploads/books/1736081494158-203930228.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(150, 2, '/uploads/books/1736081494314-366443031.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(151, 2, '/uploads/books/1736081494446-527323119.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(152, 2, '/uploads/books/1736081494587-924779203.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(153, 2, '/uploads/books/1736081494751-71211633.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(154, 2, '/uploads/books/1736081494898-141939980.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(155, 2, '/uploads/books/1736081495028-554741111.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(156, 2, '/uploads/books/1736081495172-284134613.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(157, 2, '/uploads/books/1736081495337-54269647.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(158, 2, '/uploads/books/1736081495515-285158044.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(159, 2, '/uploads/books/1736081495662-835068055.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(160, 2, '/uploads/books/1736081495804-910329874.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(161, 2, '/uploads/books/1736081495933-911382894.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(162, 2, '/uploads/books/1736081496073-638564543.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(163, 2, '/uploads/books/1736081496235-993239844.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(164, 2, '/uploads/books/1736081496426-855821672.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(165, 2, '/uploads/books/1736081496606-626244965.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(166, 2, '/uploads/books/1736081496746-329914473.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(167, 2, '/uploads/books/1736081496875-298019972.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(168, 2, '/uploads/books/1736081497019-716060959.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(169, 2, '/uploads/books/1736081497181-465088671.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(170, 2, '/uploads/books/1736081497327-757855297.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(171, 2, '/uploads/books/1736081497456-347460977.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(172, 2, '/uploads/books/1736081497593-794383445.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(173, 2, '/uploads/books/1736081497759-204663355.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(174, 2, '/uploads/books/1736081497915-556499892.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(175, 2, '/uploads/books/1736081498063-778105794.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(176, 2, '/uploads/books/1736081498226-129072834.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(177, 2, '/uploads/books/1736081498390-132991405.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(178, 2, '/uploads/books/1736081498536-622848107.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(179, 2, '/uploads/books/1736081498664-929820100.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(180, 2, '/uploads/books/1736081498801-320772426.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(181, 2, '/uploads/books/1736081498963-590313574.webp', '2025-01-05 12:51:39', '2025-01-05 12:51:39'),
(184, 2, '/uploads/books/1736088789265-563305179.webp', '2025-01-05 14:53:09', '2025-01-05 14:53:09');

-- --------------------------------------------------------

--
-- Table structure for table `permissions`
--

CREATE TABLE `permissions` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `questions`
--

CREATE TABLE `questions` (
  `id` int(11) NOT NULL,
  `thought_id` int(11) NOT NULL,
  `question_text` text CHARACTER SET utf8mb4,
  `is_question` tinyint(1) DEFAULT '1',
  `is_show` tinyint(1) DEFAULT '1',
  `yes_route_id` int(11) DEFAULT NULL,
  `no_route_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `question_type` enum('question','introduction','sub_question','conclusion','sentence','repeating_sentence','general_sentence','sentence_general') DEFAULT 'question'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `questions`
--

INSERT INTO `questions` (`id`, `thought_id`, `question_text`, `is_question`, `is_show`, `yes_route_id`, `no_route_id`, `created_at`, `updated_at`, `question_type`) VALUES
(1, 19, 'أنا هنا معك الآن، لا تقلق. سأساعدك في تنظيم أفكارك والتخلص من الأفكار السلبية، لنفكر بطريقة واقعية.', 0, 1, 2, 2, '2024-12-18 12:33:05', '2025-01-24 23:01:01', 'introduction'),
(2, 19, 'هل ترغب بأن تكون شخصًا واقعيًا، وتصدق فقط الأفكار الواقعية؟', 0, 1, 4, 3, '2024-12-18 12:58:58', '2025-01-24 23:01:51', 'question'),
(3, 19, 'لكي تتخلص من الخوف والقلق والأفكار السلبية، عليك أن تتخذ قرارًا جادًا بأنك من الآن فصاعدًا، ستكون شخصًا واقعيًا يصدق فقط الأفكار الواقعية', 0, 1, 5, 5, '2024-12-18 12:59:41', '2024-12-18 13:02:22', 'sentence'),
(4, 19, 'هل توافق على أن ليس كل فكرة تخطر في بالك يجب أن تصدقها؟ لأن بعض الأفكار قد تكون غير صحيحة. من الأفضل أن تتحقق أولاً من واقعية الفكرة؛ إذا كانت واقعية تأخذ بها، وإذا لم تكن، تتجاهلها', 1, 1, 6, 7, '2024-12-18 13:01:05', '2024-12-18 13:08:54', 'question'),
(5, 19, 'العلاج الأساسي للتخلص من الأفكار السلبية هو إثبات أنها غير واقعية واستبدالها  بأفكار صحيحة ومنطقية', 0, 1, 4, 4, '2024-12-18 13:02:07', '2024-12-18 13:09:32', 'sentence_general'),
(6, 19, 'للتخلص من الخوف والأفكار السلبية، من المهم أن تدرك أن أي فكرة تخطر في بالك ليست بالضرورة حقيقة مطلقة. بل يجب أن تتحقق منها أولاً.', 0, 1, 8, 8, '2024-12-18 13:08:02', '2024-12-18 13:10:28', 'sentence_general'),
(7, 19, 'فكر قليلاً، هل كل الأفكار التي خطرت على بالك في حياتك تحققت فعلاً على أرض الواقع؟', 0, 1, 10, 9, '2024-12-18 13:08:42', '2024-12-18 13:12:39', 'sub_question'),
(8, 19, 'ردد الآن هذه الجملة ثلاث مرات: “أنا إنسان واقعي، لا أصدق الأفكار إلا إذا كانت واقعية.”', 0, 1, 11, 11, '2024-12-18 13:10:16', '2024-12-18 13:15:28', 'repeating_sentence'),
(9, 19, 'بما أن البشر يمتلكون أفكارًا مختلفة، فهذا دليل على أن بعض الأفكار قد تكون غير صحيحة. ولذلك، قد تكون أفكارك أيضًا غير صحيحة. فلا تثق بجميع أفكارك', 0, 1, 6, 6, '2024-12-18 13:11:58', '2024-12-18 13:13:36', 'sentence'),
(10, 19, 'لو كانت جميع الناس تفكر بالطريقة نفسها، لكان الجميع يعمل في نفس المجال، ويعتنق نفس الدين، ويتبنى نفس الآراء. لكن تنوع البشر واختلاف أفكارهم دليل على أن الأفكار قد تكون صحيحة أو خاطئة.. – وينتقل الى جملة عامة تلخص فقرة الأفكار العامة', 0, 1, 6, 6, '2024-12-18 13:12:23', '2024-12-18 13:13:50', 'sentence'),
(11, 19, 'رائع، دعنا نتحقق الآن إذا كانت هذه الفكرة لديك واقعية أم لا', 1, 1, NULL, NULL, '2024-12-18 13:15:17', '2024-12-18 13:15:17', 'conclusion');

-- --------------------------------------------------------

--
-- Table structure for table `roles`
--

CREATE TABLE `roles` (
  `id` int(11) NOT NULL,
  `name` varchar(50) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `roles`
--

INSERT INTO `roles` (`id`, `name`, `created_at`, `updated_at`) VALUES
(1, 'admin', '2024-10-26 14:44:20', '2024-10-26 14:44:20'),
(2, 'user', '2024-10-26 14:44:20', '2024-10-26 14:44:20'),
(3, 'guest', '2024-10-26 14:44:20', '2024-10-26 14:44:20');

-- --------------------------------------------------------

--
-- Table structure for table `role_permissions`
--

CREATE TABLE `role_permissions` (
  `id` int(11) NOT NULL,
  `role_id` int(11) NOT NULL,
  `permission_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `subscriptions`
--

CREATE TABLE `subscriptions` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `monthly_price` decimal(10,2) NOT NULL,
  `yearly_price` decimal(10,2) NOT NULL,
  `features` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `subscriptions`
--

INSERT INTO `subscriptions` (`id`, `name`, `description`, `monthly_price`, `yearly_price`, `features`, `created_at`, `updated_at`) VALUES
(1, 'محدد', 'هذه الباقة مثالية لمن يعانون من خوف واحد فقط ويريدون التغلب عليه بسهولة', '10.00', '96.00', NULL, '2024-12-14 14:08:25', '2024-12-14 14:08:25'),
(2, 'شامل', 'هذه الباقة مناسبة لمن يرغب في التحرر تمامًا من القلق وجميع مخاوفه وأفكاره السلبية', '20.00', '192.00', NULL, '2024-12-14 14:46:00', '2024-12-14 14:46:00'),
(3, 'متقدم +', 'هذه الباقة مخصصة لمن يسعى لتحقيق السلام الداخلي الكامل والاستمتاع بحياة مليئة بالتوازن والراحة', '25.00', '240.00', NULL, '2024-12-14 14:47:03', '2024-12-14 14:47:03');

-- --------------------------------------------------------

--
-- Table structure for table `subscription_features`
--

CREATE TABLE `subscription_features` (
  `id` int(11) NOT NULL,
  `subscription_id` int(11) NOT NULL,
  `feature_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `subscription_features`
--

INSERT INTO `subscription_features` (`id`, `subscription_id`, `feature_id`, `created_at`, `updated_at`) VALUES
(1, 1, 1, '2024-12-14 14:08:25', '2024-12-14 14:08:25'),
(2, 1, 3, '2024-12-14 14:08:25', '2024-12-14 14:08:25'),
(3, 1, 4, '2024-12-14 14:08:25', '2024-12-14 14:08:25'),
(4, 1, 5, '2024-12-14 14:08:25', '2024-12-14 14:08:25'),
(5, 2, 2, '2024-12-14 14:46:00', '2024-12-14 14:46:00'),
(6, 2, 3, '2024-12-14 14:46:00', '2024-12-14 14:46:00'),
(7, 2, 4, '2024-12-14 14:46:00', '2024-12-14 14:46:00'),
(8, 2, 5, '2024-12-14 14:46:00', '2024-12-14 14:46:00'),
(9, 2, 6, '2024-12-14 14:46:00', '2024-12-14 14:46:00'),
(10, 3, 2, '2024-12-14 14:47:03', '2024-12-14 14:47:03'),
(11, 3, 3, '2024-12-14 14:47:03', '2024-12-14 14:47:03'),
(12, 3, 6, '2024-12-14 14:47:03', '2024-12-14 14:47:03'),
(13, 3, 7, '2024-12-14 14:47:03', '2024-12-14 14:47:03'),
(14, 3, 8, '2024-12-14 14:47:03', '2024-12-14 14:47:03'),
(15, 3, 9, '2024-12-14 14:47:03', '2024-12-14 14:47:03'),
(16, 3, 10, '2024-12-14 14:47:03', '2024-12-14 14:47:03'),
(17, 3, 11, '2024-12-14 14:47:03', '2024-12-14 14:47:03');

-- --------------------------------------------------------

--
-- Table structure for table `thoughts`
--

CREATE TABLE `thoughts` (
  `id` int(11) NOT NULL,
  `concern_id` int(11) DEFAULT NULL,
  `content` text NOT NULL,
  `audio_url` varchar(255) DEFAULT NULL,
  `duration` varchar(12) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `thoughts`
--

INSERT INTO `thoughts` (`id`, `concern_id`, `content`, `audio_url`, `duration`, `is_active`, `created_at`, `updated_at`) VALUES
(19, 1, 'أخاف ان اتوقف عن التنفس واصاب بالاختناق', '/uploads/thoughts/1736734264401-276413720.webm', '00:05:28.47', 1, '2024-12-09 20:49:35', '2025-01-13 02:11:06'),
(20, 1, 'اخاف ان اصاب بنوبة قلبية', '/uploads/thoughts/1736734240130-675128058.webm', '00:05:28.47', 1, '2024-12-10 20:11:15', '2025-01-13 02:10:42'),
(21, 1, 'اخاف من استمرار نوبة الهلع الى الابد', '/uploads/thoughts/1736734223504-839419934.webm', '00:05:34.41', 1, '2024-12-10 20:18:56', '2025-01-13 02:10:25'),
(22, 1, 'اخاف ان اصاب بجلطة دماغية', '/uploads/thoughts/1736734199610-919716027.webm', '00:05:56.70', 1, '2024-12-10 20:18:56', '2025-01-13 02:10:02'),
(23, 1, 'اخاف ان اموت وتخرج روحي', '/uploads/thoughts/1736734145327-405833305.webm', '00:06:19.00', 1, '2024-12-10 20:18:56', '2025-01-13 02:09:08'),
(24, 1, 'اخاف من ارتفاع ضغط الدم عندي', '/uploads/thoughts/1736734117159-25576743.webm', '00:05:50.76', 1, '2024-12-10 20:18:56', '2025-01-13 02:08:39'),
(25, 1, 'اخاف من الاصابة بالدوار وفقدان التوازن', '/uploads/thoughts/1736734099278-584211205.webm', '00:05:25.50', 1, '2024-12-14 15:05:54', '2025-01-13 02:08:21'),
(26, 1, 'اخاف من الاصابة بنوبة الهلع', '/uploads/thoughts/1736733206389-247111064.webm', '00:05:34.41', 1, '2024-12-14 15:06:16', '2025-01-13 01:53:28'),
(27, 1, 'اخاف من عدم السيطره على نوبه الهلع', '/uploads/thoughts/1736734284527-313165109.webm', '00:06:07.11', 1, '2024-12-14 15:12:33', '2025-01-13 02:11:27'),
(28, 1, 'اخاف من ان يراني الناس عند اصابتي بنوبة الهلع', '/uploads/thoughts/1736734320631-903339135.webm', '00:05:37.39', 1, '2024-12-14 15:13:22', '2025-01-13 02:12:03'),
(29, 1, 'أخاف ان اتوقف عن التنفس واصاب بالاختناق', NULL, NULL, 0, '2025-01-07 07:31:48', '2025-01-13 02:12:21'),
(30, 1, 'أخاف ان اتوقف عن التنفس واصاب بالاختناق', NULL, NULL, 0, '2025-01-07 07:44:05', '2025-01-13 02:12:17'),
(31, 1, 'اخاف من ان افقد السيطره على نفسي وعلى افعالي', '/uploads/thoughts/1736737410861-22763953.webm', '00:05:58.19', 1, '2025-01-13 03:03:33', '2025-01-13 03:03:33');

-- --------------------------------------------------------

--
-- Table structure for table `tokens`
--

CREATE TABLE `tokens` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `token` varchar(255) NOT NULL,
  `expires_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `tokens`
--

INSERT INTO `tokens` (`id`, `user_id`, `token`, `expires_at`, `created_at`, `updated_at`) VALUES
(81, 1, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6eyJpZCI6MSwicm9sZXMiOlsiYWRtaW4iXX0sImlhdCI6MTczODA4ODI4MywiZXhwIjoxNzQwNjgwMjgzfQ.ZH57u-v7ajk2bgXOJm_5C2ePm1FFWmEysN_y0NampGs', '2025-01-28 19:18:04', '2025-01-28 18:18:03', '2025-01-28 18:18:03');

-- --------------------------------------------------------

--
-- Table structure for table `topics`
--

CREATE TABLE `topics` (
  `id` int(11) NOT NULL,
  `image` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `topics`
--

INSERT INTO `topics` (`id`, `image`, `name`, `is_active`, `created_at`, `updated_at`) VALUES
(1, '/uploads/topics/1736178958944-466086015.webp', 'إيجابية وتفاؤل', 1, '2024-12-06 15:45:06', '2025-01-06 16:54:37'),
(2, '/uploads/topics/1736178984248-156340332.webp', 'تحفيز واكتساب الشغف', 1, '2024-12-06 15:45:40', '2025-01-06 15:56:24'),
(3, '/uploads/topics/1736178994031-286697967.webp', 'الاطمئنان والتوكل', 1, '2024-12-06 15:45:50', '2025-01-06 15:56:34'),
(4, '/uploads/topics/1736179009954-909299163.webp', 'سلام نفسي', 1, '2024-12-06 15:46:00', '2025-01-06 15:56:50'),
(5, '/uploads/topics/1736179020125-662631487.webp', 'سعادة وانتعاش', 1, '2024-12-06 15:46:10', '2025-01-06 15:57:00'),
(6, '/uploads/topics/1736179032147-35192079.webp', 'الثقة بالنفس', 1, '2024-12-06 15:46:21', '2025-01-06 15:57:12'),
(7, '/uploads/topics/1736179054229-779640749.webp', 'الرضا والتقبل', 1, '2024-12-06 15:46:54', '2025-01-06 15:57:34'),
(8, '/uploads/topics/1736179063167-621575394.webp', 'مواجهة الخوف', 1, '2024-12-06 15:47:04', '2025-01-06 15:57:43');

-- --------------------------------------------------------

--
-- Table structure for table `topic_users`
--

CREATE TABLE `topic_users` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `topic_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `nickname` varchar(100) DEFAULT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `gender` varchar(10) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `age` int(11) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `name`, `nickname`, `email`, `password`, `gender`, `phone`, `age`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'admin', 'admini', '<EMAIL>', '$2a$10$NBs9R/CBD9XDROxFG7cxSedX1KQao.qGNymWGyQgylw/Qz75Avr1a', 'male', '+972523773738', 36, 1, '2024-10-26 16:02:18', '2024-10-26 16:02:18');

-- --------------------------------------------------------

--
-- Table structure for table `user_answers`
--

CREATE TABLE `user_answers` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `question_id` int(11) NOT NULL,
  `answer` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `user_roles`
--

CREATE TABLE `user_roles` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `role_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `user_roles`
--

INSERT INTO `user_roles` (`id`, `user_id`, `role_id`) VALUES
(1, 1, 1);

-- --------------------------------------------------------

--
-- Table structure for table `user_subscriptions`
--

CREATE TABLE `user_subscriptions` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `subscription_id` int(11) NOT NULL,
  `start_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `end_date` timestamp NULL DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `videos`
--

CREATE TABLE `videos` (
  `id` int(11) NOT NULL,
  `text` varchar(255) DEFAULT NULL,
  `description` mediumtext,
  `image` varchar(255) NOT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `video_id` int(11) DEFAULT NULL,
  `type` enum('category','sub_category') NOT NULL DEFAULT 'category',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `videos`
--

INSERT INTO `videos` (`id`, `text`, `description`, `image`, `is_active`, `video_id`, `type`, `created_at`, `updated_at`) VALUES
(9, 'علاج الفوبيا', 'الفوبيا هي اضطراب قلق يتميز بخوف شديد ومستمر وغير منطقي من كائن معين، موقف، أو نشاط، يؤدي إلى تجنب هذا المسبب أو مواجهته بقلق شديد.\r\n\r\nمعايير التشخيص وفقًا لـ DSM-5:\r\n\r\nالخوف الواضح والمستمر من كائن محدد (مثل الحيوانات) أو اماكن معينة (مثل المرتفعات) أو موقف معين (مثل الطيران أو الأماكن المغلقة).\r\n\r\nرد فعل فوري للقلق عند التعرض للمسبب، قد يتطور إلى نوبة هلع.\r\n\r\nتجنب الموقف أو الكائن المسبب للخوف، أو مواجهته مع قلق شديد.\r\n\r\nعدم تناسب الخوف مع الخطر الفعلي الذي يشكله الموقف أو الكائن.\r\n\r\nاستمرار الأعراض لمدة 6 أشهر أو أكثر.\r\n\r\nتأثير واضح على الأداء اليومي أو الأنشطة الاجتماعية بسبب الخوف.\r\n\r\nعدم ارتباط الأعراض بحالة طبية أو اضطراب نفسي آخر.\r\n\r\nأمثلة شائعة للفوبيا:\r\n\r\nفوبيا المرتفعات، فوبيا الطيران، فوبيا الأماكن البعيدة، فوبيا الأماكن المغلقة، فوبيا الحيوانات، فوبيا العناكب، فوبيا الظلام، فوبيا الدم، فوبيا الحقن، فوبيا التجمعات أو الأماكن العامة، فوبيا المصاعد، فوبيا الصوت العالي، فوبيا البرق والرعد، فوبيا المرض.\r\n\r\n\r\n\r\nالخوف ليس ضعفًا، لكنه يصبح عائقًا عندما يتحكم في حياتك اليومية.\r\nمع العلاج النفسي والدعم المناسب، يمكنك التغلب على مخاوفك والعيش بحرية واطمئنان.\r\nاضغط الآن وابدأ رحلتك نحو حياة أكثر شجاعة وثقة. 🌟', '/uploads/treatment/1736934798995-765568108.webp', 1, NULL, 'category', '2025-01-14 11:21:57', '2025-01-15 09:53:19'),
(10, 'علاج الوسواس القهري', 'الوسواس القهري: عندما تصبح الأفكار عبئًا والسلوكيات قيدًا الوسواس القهري (OCD) هو اضطراب نفسي يتميز بأفكار متكررة ومزعجة (وساوس) تدفعك للقيام بسلوكيات قهرية لتخفيف التوتر. هذا الاضطراب قد يؤثر بشكل كبير على حياتك اليومية. أعراض الوسواس القهري (وفقًا لـ DSM-5): إذا كنت تعاني من الأفكار أو السلوكيات التالية بشكل متكرر وتؤثر على حياتك، فقد تكون بحاجة لدعم: أفكار متكررة مزعجة أو مخيفة لا يمكنك التحكم بها. الحاجة إلى التحقق المستمر (مثل التأكد من إغلاق الأبواب أو إطفاء الأنوار). سلوكيات متكررة أو طقوس (مثل غسل اليدين بشكل مفرط). الترتيب أو التنظيم المبالغ فيه حتى يصبح كل شيء \"مثاليًا\". خوف مفرط من التلوث أو الجراثيم. الشعور بأنك مضطر لأداء سلوك معين لتخفيف التوتر، رغم معرفتك بعدم منطقيته. تأثير هذه الوساوس والسلوكيات على علاقاتك أو عملك أو حياتك اليومية. الوسواس القهري يمكن علاجه! لا تدع هذه الأفكار والسلوكيات تسيطر على حياتك. اضغط الآن لتبدأ رحلتك نحو التحرر واستعادة راحة بالك وحياتك الطبيعية.', '/uploads/treatment/1736854758395-743210624.webp', 1, NULL, 'category', '2025-01-14 11:39:18', '2025-01-14 11:39:18'),
(11, 'علاج الرهاب الاجتماعي', 'الرهاب الاجتماعي هو اضطراب قلق يتسم بالخوف الشديد والمستمر من مواقف اجتماعية أو أداء يمكن أن يتعرض فيها الشخص لتقييم أو انتقاد من الآخرين. معايير التشخيص وفقًا لـ DSM-5: الخوف الواضح أو المستمر من موقف اجتماعي أو أدائي (مثل الحديث أمام الجمهور أو مقابلة أشخاص جدد). الخوف من الإحراج أو الحكم السلبي: يعتقد الشخص أنه سيتصرف بطريقة محرجة أو يتعرض للسخرية. تجنب المواقف الاجتماعية أو مواجهتها مع قلق شديد. رد فعل مبالغ فيه للخوف مقارنة بخطورة الموقف. استمرار الأعراض لمدة 6 أشهر أو أكثر. تأثير واضح على الأداء اليومي (العمل، الدراسة، أو العلاقات الاجتماعية). عدم ارتباط الأعراض بحالة طبية أخرى أو اضطراب عقلي آخر. الرهاب الاجتماعي ليس ضعفًا، بل حالة يمكن علاجها بفعالية. اضغط الآن وابدأ رحلتك نحو الثقة والراحة في المواقف الاجتماعية.', '/uploads/treatment/1736854824892-643784333.webp', 1, NULL, 'category', '2025-01-14 11:40:25', '2025-01-14 11:40:25'),
(12, 'علاج الاكتئاب', 'الاكتئاب: أكثر من مجرد حزن عابر الاكتئاب هو اضطراب نفسي شائع يؤثر على الحالة المزاجية، التفكير، والسلوك. يتجاوز الشعور بالحزن العابر ليؤثر بشكل ملحوظ على الحياة اليومية. أعراض الاكتئاب (وفقًا للدليل التشخيصي الخامس - DSM-5): إذا شعرت بخمسة أو أكثر من هذه الأعراض لمدة أسبوعين أو أكثر، فقد تكون تعاني من الاكتئاب: الشعور بالحزن أو الفراغ بشكل دائم. فقدان الاهتمام أو الاستمتاع بالأشياء التي كنت تحبها. تغير في الشهية أو الوزن (زيادة أو نقصان غير مبرر). اضطرابات النوم (الأرق أو النوم المفرط). الشعور بالتعب أو فقدان الطاقة. الإحساس بالذنب المفرط أو انعدام القيمة. صعوبة في التركيز أو اتخاذ القرارات. بطء في الحركة أو شعور بالتوتر المفرط. أفكار متكررة عن الموت أو الانتحار. الاكتئاب ليس ضعفًا أو خيارًا، بل حالة طبية يمكن علاجها بفعالية. إذا كنت تعاني من هذه الأعراض، اضغط الآن وابدأ رحلتك نحو الشفاء وحياة مليئة بالسعادة والطمأنينة.', '/uploads/treatment/1736854867421-614489921.webp', 1, NULL, 'category', '2025-01-14 11:41:07', '2025-01-14 11:41:07'),
(13, 'علاج اضطراب ما بعد الصمة', 'اضطراب ما بعد الصدمة (PTSD): رحلة من الألم إلى التعافي اضطراب ما بعد الصدمة هو اضطراب نفسي يحدث بعد التعرض لحدث صادم أو مرعب. يمكن أن يكون لهذا الاضطراب تأثير عميق على الحياة اليومية. أعراض اضطراب ما بعد الصدمة (وفقًا لـ DSM-5): إذا كنت تعاني من الأعراض التالية لمدة شهر أو أكثر بعد تجربة صادمة، فقد تكون بحاجة للمساعدة: إعادة معايشة الحدث: استرجاع متكرر للموقف الصادم من خلال كوابيس أو ذكريات مزعجة أو فلاشباك تشعرك وكأنك تعيش الحدث من جديد. تجنب المحفزات: تجنب الأماكن، الأشخاص، أو المواقف التي تذكرك بالحدث المؤلم، مما يؤدي للعزلة والانطواء. تغيرات في المزاج والإدراك: مشاعر دائمة بالذنب، الحزن، أو العزلة، فقدان الاهتمام بالأشياء التي كنت تستمتع بها، وصعوبة تذكر تفاصيل الحدث بشكل دقيق. فرط اليقظة: شعور دائم بالتوتر، صعوبة في النوم، ردود فعل مبالغ فيها على الأحداث اليومية، وصعوبة الاسترخاء. اضطراب ما بعد الصدمة ليس نهاية الطريق. بالعلاج والدعم المناسب، يمكنك استعادة توازنك وسلامك النفسي. اضغط الآن لتبدأ رحلتك نحو التعافي وحياة مليئة بالطمأنينة والثقة.', '/uploads/treatment/1736854899141-681519320.webp', 1, NULL, 'category', '2025-01-14 11:41:39', '2025-01-14 11:41:39'),
(14, 'علاج القلق من الأمراض', 'اضطراب القلق العام (GAD) والقلق من الأمراض: اضطرابات القلق هي حالات شائعة تؤثر على التفكير والمشاعر والجسم. القلق العام يجعل الشخص يشعر بخوف دائم ومفرط من المستقبل أو الأحداث اليومية، والقلق من الأمراض يتركز حول المخاوف المبالغ فيها من الإصابة بمشاكل صحية. أعراض اضطراب القلق العام (وفقًا لـ DSM-5): قلق مفرط ومستمر يصعب التحكم فيه، ويؤثر على مختلف جوانب الحياة. شعور دائم بالتوتر أو الانزعاج. صعوبة في التركيز أو الإحساس بأن العقل \"فارغ\". إرهاق مستمر حتى بعد بذل مجهود بسيط. توتر في العضلات أو آلام جسدية غير مبررة. صعوبة في النوم، سواء الأرق أو النوم المتقطع. أعراض القلق من الأمراض: الخوف المستمر من الإصابة بأمراض خطيرة، حتى مع عدم وجود دليل طبي. المبالغة في تفسير الأعراض البسيطة، مثل الصداع أو التعب، كعلامة على مرض خطير. البحث المتكرر عن الأعراض الصحية على الإنترنت، مما يزيد من القلق. زيارة الأطباء بشكل متكرر، أو على العكس، تجنب الفحوصات خوفًا من تشخيص مرض. تأثير سلبي على الحياة اليومية بسبب هذه المخاوف. القلق ليس عدوك، لكنه يصبح مشكلة عندما يسيطر على حياتك. بالعلاج النفسي المناسب والتقنيات الموجهة، يمكنك التغلب على القلق والعيش بحياة مليئة بالهدوء والثقة. اضغط الآن وابدأ رحلتك نحو راحة البال والتخلص من المخاوف المستمرة.', '/uploads/treatment/1736854952126-190484262.webp', 1, NULL, 'category', '2025-01-14 11:42:32', '2025-01-14 11:42:32'),
(15, 'test', NULL, '/uploads/treatment/1736936807715-qulwpu4.webm', 1, 9, 'sub_category', '2025-01-15 08:36:33', '2025-01-15 10:27:36');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `admins`
--
ALTER TABLE `admins`
  ADD PRIMARY KEY (`id`),
  ADD KEY `fk_email_clinic` (`email_clinic`);

--
-- Indexes for table `answers`
--
ALTER TABLE `answers`
  ADD PRIMARY KEY (`id`),
  ADD KEY `question_id` (`question_id`),
  ADD KEY `fk_user_id` (`user_id`);

--
-- Indexes for table `audio_clips`
--
ALTER TABLE `audio_clips`
  ADD PRIMARY KEY (`id`),
  ADD KEY `thought_id` (`thought_id`);

--
-- Indexes for table `books`
--
ALTER TABLE `books`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `name` (`name`);

--
-- Indexes for table `clinics`
--
ALTER TABLE `clinics`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `concerns`
--
ALTER TABLE `concerns`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `distraction`
--
ALTER TABLE `distraction`
  ADD PRIMARY KEY (`id`),
  ADD KEY `distraction_id` (`distraction_id`);

--
-- Indexes for table `features`
--
ALTER TABLE `features`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `meditation`
--
ALTER TABLE `meditation`
  ADD PRIMARY KEY (`id`),
  ADD KEY `meditation_id` (`meditation_id`);

--
-- Indexes for table `next_questions`
--
ALTER TABLE `next_questions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `question_id` (`question_id`),
  ADD KEY `next_question_id` (`next_question_id`);

--
-- Indexes for table `noti_cat`
--
ALTER TABLE `noti_cat`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `pages`
--
ALTER TABLE `pages`
  ADD PRIMARY KEY (`id`),
  ADD KEY `book_id` (`book_id`);

--
-- Indexes for table `permissions`
--
ALTER TABLE `permissions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `name` (`name`);

--
-- Indexes for table `questions`
--
ALTER TABLE `questions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `thought_id` (`thought_id`),
  ADD KEY `fk_yes_route_id` (`yes_route_id`),
  ADD KEY `fk_no_route_id` (`no_route_id`);

--
-- Indexes for table `roles`
--
ALTER TABLE `roles`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `name` (`name`);

--
-- Indexes for table `role_permissions`
--
ALTER TABLE `role_permissions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `role_permission_unique` (`role_id`,`permission_id`),
  ADD KEY `permission_id` (`permission_id`);

--
-- Indexes for table `subscriptions`
--
ALTER TABLE `subscriptions`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `subscription_features`
--
ALTER TABLE `subscription_features`
  ADD PRIMARY KEY (`id`),
  ADD KEY `subscription_id` (`subscription_id`),
  ADD KEY `fk_feature_id` (`feature_id`);

--
-- Indexes for table `thoughts`
--
ALTER TABLE `thoughts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `concern_id` (`concern_id`);

--
-- Indexes for table `tokens`
--
ALTER TABLE `tokens`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `token` (`token`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `topics`
--
ALTER TABLE `topics`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `topic_users`
--
ALTER TABLE `topic_users`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `topic_id` (`topic_id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`);

--
-- Indexes for table `user_answers`
--
ALTER TABLE `user_answers`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `question_id` (`question_id`);

--
-- Indexes for table `user_roles`
--
ALTER TABLE `user_roles`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_role_unique` (`user_id`,`role_id`),
  ADD KEY `role_id` (`role_id`);

--
-- Indexes for table `user_subscriptions`
--
ALTER TABLE `user_subscriptions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `subscription_id` (`subscription_id`);

--
-- Indexes for table `videos`
--
ALTER TABLE `videos`
  ADD PRIMARY KEY (`id`),
  ADD KEY `fk_video_id` (`video_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `admins`
--
ALTER TABLE `admins`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `answers`
--
ALTER TABLE `answers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `audio_clips`
--
ALTER TABLE `audio_clips`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT for table `books`
--
ALTER TABLE `books`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `clinics`
--
ALTER TABLE `clinics`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `concerns`
--
ALTER TABLE `concerns`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=22;

--
-- AUTO_INCREMENT for table `distraction`
--
ALTER TABLE `distraction`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `features`
--
ALTER TABLE `features`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `meditation`
--
ALTER TABLE `meditation`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `next_questions`
--
ALTER TABLE `next_questions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `noti_cat`
--
ALTER TABLE `noti_cat`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `pages`
--
ALTER TABLE `pages`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=185;

--
-- AUTO_INCREMENT for table `permissions`
--
ALTER TABLE `permissions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `questions`
--
ALTER TABLE `questions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `roles`
--
ALTER TABLE `roles`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=889;

--
-- AUTO_INCREMENT for table `role_permissions`
--
ALTER TABLE `role_permissions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `subscriptions`
--
ALTER TABLE `subscriptions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `subscription_features`
--
ALTER TABLE `subscription_features`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=18;

--
-- AUTO_INCREMENT for table `thoughts`
--
ALTER TABLE `thoughts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=32;

--
-- AUTO_INCREMENT for table `tokens`
--
ALTER TABLE `tokens`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=82;

--
-- AUTO_INCREMENT for table `topics`
--
ALTER TABLE `topics`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `topic_users`
--
ALTER TABLE `topic_users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `user_answers`
--
ALTER TABLE `user_answers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `user_roles`
--
ALTER TABLE `user_roles`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `user_subscriptions`
--
ALTER TABLE `user_subscriptions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `videos`
--
ALTER TABLE `videos`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `admins`
--
ALTER TABLE `admins`
  ADD CONSTRAINT `fk_email_clinic` FOREIGN KEY (`email_clinic`) REFERENCES `users` (`email`) ON DELETE CASCADE;

--
-- Constraints for table `answers`
--
ALTER TABLE `answers`
  ADD CONSTRAINT `answers_ibfk_1` FOREIGN KEY (`question_id`) REFERENCES `questions` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `audio_clips`
--
ALTER TABLE `audio_clips`
  ADD CONSTRAINT `audio_clips_ibfk_1` FOREIGN KEY (`thought_id`) REFERENCES `thoughts` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `clinics`
--
ALTER TABLE `clinics`
  ADD CONSTRAINT `clinics_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `distraction`
--
ALTER TABLE `distraction`
  ADD CONSTRAINT `distraction_ibfk_1` FOREIGN KEY (`distraction_id`) REFERENCES `distraction` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `meditation`
--
ALTER TABLE `meditation`
  ADD CONSTRAINT `meditation_ibfk_1` FOREIGN KEY (`meditation_id`) REFERENCES `meditation` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `next_questions`
--
ALTER TABLE `next_questions`
  ADD CONSTRAINT `next_questions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `next_questions_ibfk_2` FOREIGN KEY (`question_id`) REFERENCES `questions` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `next_questions_ibfk_3` FOREIGN KEY (`next_question_id`) REFERENCES `questions` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `pages`
--
ALTER TABLE `pages`
  ADD CONSTRAINT `pages_ibfk_1` FOREIGN KEY (`book_id`) REFERENCES `books` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `questions`
--
ALTER TABLE `questions`
  ADD CONSTRAINT `fk_no_route_id` FOREIGN KEY (`no_route_id`) REFERENCES `questions` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_yes_route_id` FOREIGN KEY (`yes_route_id`) REFERENCES `questions` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `questions_ibfk_1` FOREIGN KEY (`thought_id`) REFERENCES `thoughts` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `role_permissions`
--
ALTER TABLE `role_permissions`
  ADD CONSTRAINT `role_permissions_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `role_permissions_ibfk_2` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `subscription_features`
--
ALTER TABLE `subscription_features`
  ADD CONSTRAINT `fk_feature_id` FOREIGN KEY (`feature_id`) REFERENCES `features` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `subscription_features_ibfk_1` FOREIGN KEY (`subscription_id`) REFERENCES `subscriptions` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `subscription_features_ibfk_2` FOREIGN KEY (`feature_id`) REFERENCES `features` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `thoughts`
--
ALTER TABLE `thoughts`
  ADD CONSTRAINT `thoughts_ibfk_1` FOREIGN KEY (`concern_id`) REFERENCES `concerns` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `tokens`
--
ALTER TABLE `tokens`
  ADD CONSTRAINT `tokens_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `topic_users`
--
ALTER TABLE `topic_users`
  ADD CONSTRAINT `topic_users_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `topic_users_ibfk_2` FOREIGN KEY (`topic_id`) REFERENCES `topics` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_answers`
--
ALTER TABLE `user_answers`
  ADD CONSTRAINT `user_answers_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_answers_ibfk_2` FOREIGN KEY (`question_id`) REFERENCES `next_questions` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_roles`
--
ALTER TABLE `user_roles`
  ADD CONSTRAINT `user_roles_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_roles_ibfk_2` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_subscriptions`
--
ALTER TABLE `user_subscriptions`
  ADD CONSTRAINT `user_subscriptions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_subscriptions_ibfk_2` FOREIGN KEY (`subscription_id`) REFERENCES `subscriptions` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `videos`
--
ALTER TABLE `videos`
  ADD CONSTRAINT `fk_video_id` FOREIGN KEY (`video_id`) REFERENCES `videos` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
