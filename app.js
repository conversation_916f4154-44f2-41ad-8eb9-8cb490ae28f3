"use strict";
require("dotenv").config();
const express = require("express");
const session = require("express-session");
const path = require("path");
const cors = require("cors");

const routes = require("./routes");
// require('./config/passport-setup');
// const { getUserTopics } = require("./services");

// const cron = require("node-cron");

// cron.schedule("0 0 9 * * *", async () => {
//   await getUserTopics();
// });
const app = express();

// Allowed origins
const allowedOrigins = [
  "http://localhost:5173",
  "https://arab-cbt.com",
  "https://www.arab-cbt.com",
];

const corsOptions = {
  origin: function (origin, callback) {
    if (!origin) return callback(null, true); // For server-to-server or curl requests
    if (allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      console.warn("Blocked by CORS:", origin);
      callback(new Error("Not allowed by CORS: " + origin));
    }
  },
  credentials: true,
};

// Apply CORS for all routes
app.use(cors(corsOptions));

// Handle preflight requests (OPTIONS)
// app.options("*", cors(corsOptions));

// Serve static files
app.use(express.static(path.resolve(__dirname, "public")));

// Increase payload limit for large uploads
app.use(express.json({ limit: "800mb" }));
app.use(express.urlencoded({ limit: "800mb", extended: true }));

// Serve uploads and locals
app.use("/uploads", express.static(path.resolve(__dirname, "public/uploads")));
app.use("/locals", express.static(path.resolve(__dirname, "public/locals")));

// Session middleware
app.use(
  session({
    secret: process.env.SESSION_SECRET || "default_secret",
    resave: false,
    saveUninitialized: true,
    cookie: { secure: false },
  })
);

// Define routes
app.use("/", routes);

app.get("/", (req, res) => {
  res.send("Hello World");
});

// Error handler
app.use((err, req, res, next) => {
  res.header("Access-Control-Allow-Origin", req.headers.origin || "*");
  res.header("Access-Control-Allow-Credentials", "true");
  console.error("Server Error:", err.message);
  res.status(err.status || 500).json({
    error: err.message || "Internal Server Error",
  });
  next();
});

module.exports = app;