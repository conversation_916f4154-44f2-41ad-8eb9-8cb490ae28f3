// app.js
const http = require("http");
const app = require("./app");
const logger = require("./utils/logger");
require("dotenv").config();
require("./dbInit");

const port = process.env.PORT;

async function bootstrap() {
  const server = http.createServer(app);

  server.listen(port, () => {
    console.log(`Listening on port http://localhost:${port}`);
  });


  const exitHandler = () => {
    if (server) {
      server.close(() => {
        logger.info("Server closed");
        process.exit(1);
      });
    } else {
      process.exit(1);
    }
  };

  const unexpectedErrorHandler = (error) => {
    logger.error(error);
    exitHandler();
  };

  process.on("uncaughtException", unexpectedErrorHandler);
  process.on("unhandledRejection", unexpectedErrorHandler);

  process.on("SIGTERM", () => {
    logger.info("SIGTERM received");
    if (server) server.close();
  });
}

bootstrap();