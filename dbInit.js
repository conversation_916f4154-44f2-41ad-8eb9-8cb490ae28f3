const RoleModel = require("./models/roleModel");
const UserModel = require("./models/userModel");
const TokenModel = require("./models/tokenModel");
const SubscriptionPackageModel = require("./models/SubscriptionPackageModel");
const SubscriptionPlanModel = require("./models/SubscriptionPlanModel");
const SubscriptionFeatureModel = require("./models/SubscriptionFeatureModel");
const FeatureModel = require("./models/featureModel");
const UserSubscriptionModel = require("./models/UserSubscriptionModel");
const ClinicModel = require("./models/clinicsModel"); // Capitalize to match convention
const PermissionModel = require("./models/premissionModel"); // Corrected spelling
const RolePermissionModel = require("./models/rolePermissionModel");
const UserRoleModel = require("./models/userRoleModel");
const BooksModel = require("./models/booksModel");
const BooksPurchaseModel = require("./models/BooksPurchaseModel");
const VideosModel = require("./models/videosModel");
const TopicModel = require("./models/topicModel");
const ConcernModel = require("./models/concernModel");
const ThoughtModel = require("./models/thoughtModel");
const AudioClipModel = require("./models/audioClipModel");
const AnswerModel = require("./models/answerModel");
const QuestionModel = require("./models/questionModel");
const DistractionModel = require("./models/distractionModel");
const MeditationModel = require("./models/meditationModel");
const LectureModel = require("./models/lecturesModel");
const ChantingModel = require("./models/chantingModel");
const AppVersionModel = require("./models/appVersionModel");
const UserBookModel = require("./models/userBookModel");
const PaymentMethodModel = require("./models/paymentModel");
const ComparisonHistoryModel = require("./models/comparison_history");
const VideoPurchaseModel = require("./models/videoPurchaseModel");
(async () => {
  try {
    await RoleModel.createRoleTable();
    await PermissionModel.createPermissionTable();
    await RolePermissionModel.createRolePermissionTable();
    await ClinicModel.createClinicTable();
    await UserModel.createUserTable();
    await UserRoleModel.createUserRoleTable();
    await TokenModel.createTokenTable();
    await BooksModel.createBooksTable();
    await BooksModel.createPagesTable();
    await VideosModel.createVideosTable();
    await TopicModel.createTopicTable();
    await ConcernModel.createTable();
    await ThoughtModel.createTable();
    await AudioClipModel.createTable();
    await QuestionModel.createTable();
    await AnswerModel.createTable();
    await DistractionModel.createTable();
    await MeditationModel.createMeditationTable();
    await LectureModel.createLecturesTable();
    await BooksModel.createDiscountOptionsTable();
    await ChantingModel.createTable();
    await AppVersionModel.createTable();
    await BooksPurchaseModel.createTable();
    await SubscriptionPackageModel.createTable();
    await SubscriptionFeatureModel.createTable();
    await FeatureModel.createTable();
    await SubscriptionPlanModel.createTable();
    await UserSubscriptionModel.createTable();
    await UserBookModel.createTable();
    await PaymentMethodModel.createTable();
    await ComparisonHistoryModel.createTable();
    await VideoPurchaseModel.createTable();
    console.log("All tables created successfully");
    console.log("Database initialization completed.");
  } catch (err) {
    console.error("Error creating tables:", err.message);
    process.exit(1); // Exit the process if initialization fails
  }
})();