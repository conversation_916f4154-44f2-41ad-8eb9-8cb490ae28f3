// authMiddleware.js
const jwt = require('jsonwebtoken');
const pool = require('../config/db');
require('dotenv').config();

exports.authenticateUser = (req, res, next) => {
    const token = req.headers['authorization']?.split(' ')[1]; // Assuming the token is sent as a Bearer token
    if (!token) {
        return res.status(401).json({ message: 'No token provided' });
    }

    jwt.verify(token, process.env.ACCESS_TOKEN_SECRET, (err, decoded) => {
        if (err) {
            return res.status(401).json({ message: 'Unauthorized' });
        }
        req.user = decoded; // Attach the decoded user info to the request
        next();
    });
    
};

exports.adminAuthAccess = (req, res, next) => {
    try {
      // جلب التوكن من الهيدر
      const authHeader = req.headers['authorization'];
      const token = authHeader && authHeader.split(' ')[1]; // Bearer <token>
  
      if (!token) {
        return res.status(401).json({ message: 'لم يتم توفير التوكن (Unauthorized)' });
      }
  
      // التحقق من صحة التوكن
      jwt.verify(token, process.env.ACCESS_TOKEN_SECRET, (err, decoded) => {
        if (err) {
            return res.status(401).json({ message: 'Unauthorized' });
        }
    
        req.user = decoded;
    
        if (req.user.role !== 1 && req.user.role !== 2) {
            return res.status(403).json({ message: 'Unauthorized - insufficient role' });
        }
    
        next();
    });
    
    } catch (error) {
      console.error('خطأ في adminAuthAccess:', error.message);
      res.status(500).json({ message: 'حدث خطأ أثناء التحقق من الصلاحيات' });
    }
  };


  exports.videoPurchasesAuth = async (req, res, next) => {
    const token = req.headers['authorization']?.split(' ')[1];

  if (!token) {
    return res.status(401).json({ message: 'No token provided' });
  }

  jwt.verify(token, process.env.ACCESS_TOKEN_SECRET, async (err, decoded) => {
    if (err) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    try {
      const [userRows] = await pool.query(`SELECT * FROM users WHERE id = ?`, [decoded.id]);
      if (userRows.length === 0) return res.status(404).json({ message: 'User not found' });

      const user = userRows[0];

      // fetch purchased videos
      const [videoRows] = await pool.query(
        `SELECT video_id FROM video_purchases WHERE user_id = ?`,
        [user.id]
      );

      req.user = {
        ...user,
        purchasedVideos: videoRows, // important!
      };

      next();
    } catch (error) {
      console.error("authMiddleware error:", error);
      return res.status(500).json({ message: "Failed to authenticate user" });
    }
  });
};