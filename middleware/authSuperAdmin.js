const jwt = require('jsonwebtoken');
require('dotenv').config();

const authSuperAdmin = (req, res, next) => {
    const token = req.headers['authorization'];

    if (!token) {
        return res.status(403).json({ message: 'Access denied. No token provided.' });
    }

    try {
        const decoded = jwt.verify(token.split(' ')[1], process.env.JWT_SECRET);

        if (decoded.role !== 'super admin') {
            return res.status(403).json({ message: 'Access denied. Super admins only.' });
        }

        req.user = decoded;
        next();
    } catch (err) {
        return res.status(400).json({ message: 'Invalid token.' });
    }
};

module.exports = authSuperAdmin;