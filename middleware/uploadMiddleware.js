const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Function to create a Multer storage configuration dynamically based on the table name
const createMulterStorage = (tableName) => {
  return multer.diskStorage({
    destination: (req, file, cb) => {
      const uploadPath = path.join(__dirname, `../public/uploads/${tableName}`);
      fs.mkdirSync(uploadPath, { recursive: true }); // Ensure the directory exists
      cb(null, uploadPath);
    },
    filename: (req, file, cb) => {
      const uniqueName = `${Date.now()}-${Math.round(Math.random() * 1E9)}${path.extname(file.originalname)}`;
      cb(null, uniqueName);
    }
  });
};

// ✅ Log the received field names for debugging
const fileFilter = (req, file, cb) => {
  cb(null, true);
};

const limits = { fileSize: 1000 * 1024 * 1024 }; // 1000MB

exports.MultiFiles = (tableName, fieldNames = []) => {
  const storage = createMulterStorage(tableName);
  return multer({ storage, fileFilter, limits }).fields(fieldNames);
};

exports.SingleFile = (tableName, fieldName = 'file') => {
  const storage = createMulterStorage(tableName);
  return multer({ storage, fileFilter, limits }).single(fieldName);
};

exports.SingleVideo = (tableName, fieldName = 'video') => {
  const storage = createMulterStorage(tableName);
  return multer({ storage, fileFilter, limits }).single(fieldName);
};

exports.SingleImage = (tableName, fieldName = 'image') => {
  const storage = createMulterStorage(tableName);
  return multer({ storage, fileFilter, limits }).single(fieldName);
};

exports.SingleAudio = (tableName, fieldName = 'audio_url') => {
  const storage = createMulterStorage(tableName);
  return multer({ storage, fileFilter, limits }).single(fieldName);
};
