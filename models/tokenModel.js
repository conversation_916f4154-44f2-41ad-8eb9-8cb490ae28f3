const pool = require('../config/db');
require('dotenv').config();
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const { checkTableExist } = require('../utils/dbUtils');

class TokenModel {
    // Function to create the Token table
    static async createTokenTable() {
        const query = `
            CREATE TABLE IF NOT EXISTS tokens (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                token VARCHAR(255) NOT NULL UNIQUE,
                expires_at TIMESTAMP NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            );
        `;
    
        try {
            await checkTableExist('tokens', query);

        } catch (err) {
            console.error('Error creating Tokens table:', err);
            throw err; // Optionally rethrow the error for handling upstream
        }
    }

    // Function to generate a random base64-encoded token
    static generateRandomToken(length = 16) {
        return crypto.randomBytes(length).toString('hex'); // Adjust length as needed
    }

    // Function to save a token in the database
    static async saveToken(userId, token) {
        const sql = `
            INSERT INTO tokens (user_id, token)
            VALUES (?, ?)
            ON DUPLICATE KEY UPDATE token = VALUES(token);
        `;

        try {
            await pool.execute(sql, [userId, token]);
        } catch (err) {
            console.error('Error saving token:', err);
            throw err;
        }
    }

    // Function to get a token by its value
    static async getTokenByValue(tokenValue) {
        const query = 'SELECT * FROM tokens WHERE token = ?';
        try {
            const [rows] = await pool.query(query, [tokenValue]);
            return rows.length > 0 ? rows[0] : null;
        } catch (err) {
            console.error('Error fetching token:', err);
            throw err;
        }
    }

    static async getTokenByUserId(userId) {
        const query = 'SELECT * FROM tokens WHERE user_id = ?';
        try {
            const [rows] = await pool.query(query, [userId]);
            return rows.length > 0 ? rows[0] : null;
        } catch (err) {
            console.error('Error fetching token by user ID:', err.message);
            throw err;
        }
    }

    // Function to generate a JWT token
    static generateToken(userId) {
        const payload = { id: userId };
        const secret = this.generateRandomToken();
        return jwt.sign(payload, secret);
    }

    static async validateToken(userId) {
        const query = 'SELECT * FROM tokens WHERE user_id = ?';
        try {
            const [rows] = await pool.query(query, [userId]);
            return rows.length > 0;
        } catch (err) {
            console.error('Error validating token:', err);
            throw err;
        }
    }

    // Function to delete existing tokens for a user
    static async deleteTokens(userId) {
        const query = 'DELETE FROM tokens WHERE user_id = ?';
        try {
            await pool.query(query, [userId]);
        } catch (err) {
            console.error('Error deleting tokens:', err);
            throw err;
        }
    }
}

module.exports = TokenModel;
