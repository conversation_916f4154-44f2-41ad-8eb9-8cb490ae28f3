const pool = require('../config/db');  // Make sure this is your MySQL connection setup
const { checkTableExist } = require('../utils/dbUtils');  // Utility to check table existence

class UserMentalState {
    // Create the table if it doesn't exist
    static async createTable() {
        const query = `
            CREATE TABLE IF NOT EXISTS mental_state_monitoring (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                thought_id INT NOT NULL,
                before_treatment INT,  -- Mental state score before treatment
                after_treatment INT,   -- Mental state score after treatment
                notes TEXT,            -- Optional additional notes
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id),
                FOREIGN KEY (thought_id) REFERENCES thoughts(id)
            );
        `;

        try {
            await checkTableExist('mental_state_monitoring', query);
        } catch (err) {
            console.error('Error creating table:', err.message);
            throw new Error('Failed to create mental_state_monitoring table');
        }
    }

    // Create a new mental state record for before treatment
    static async createBeforeTreatment(userId, thoughtId, beforeTreatment, notes) {
        const query = `
            INSERT INTO mental_state_monitoring (user_id, thought_id, before_treatment, notes)
            VALUES (?, ?, ?, ?)
        `;

        try {
            const [result] = await pool.query(query, [userId, thoughtId, beforeTreatment, notes || null]);
            return { success: true, id: result.insertId };  // Return the ID of the newly created record
        } catch (err) {
            console.error('Error creating mental state before treatment:', err.message);
            throw new Error('Failed to create mental state before treatment');
        }
    }

    // Update the after treatment state for an existing mental state record
    static async updateAfterTreatment(id, afterTreatment, notes) {
        const query = `
            UPDATE mental_state_monitoring 
            SET after_treatment = ?, notes = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        `;

        try {
            const [result] = await pool.query(query, [afterTreatment, notes || null, id]);
            if (result.affectedRows === 0) {
                throw new Error('No record found to update');
            }
            return { success: true };
        } catch (err) {
            console.error('Error updating mental state after treatment:', err.message);
            throw new Error('Failed to update mental state after treatment');
        }
    }

    // Get mental state monitoring records for a specific user
    static async getUserMentalStates(userId) {
        const query = `SELECT * FROM mental_state_monitoring WHERE user_id = ?`;

        try {
            const [rows] = await pool.query(query, [userId]);
            return rows;  // Return all the records for this user
        } catch (err) {
            console.error('Error fetching mental state monitoring records:', err.message);
            throw new Error('Failed to fetch mental state monitoring records');
        }
    }

    // Get a mental state record by ID
    static async getMentalStateById(id) {
        const query = `SELECT * FROM mental_state_monitoring WHERE id = ?`;

        try {
            const [rows] = await pool.query(query, [id]);
            return rows.length > 0 ? rows[0] : null;
        } catch (err) {
            console.error('Error fetching mental state record by ID:', err.message);
            throw new Error('Failed to fetch mental state record by ID');
        }
    }

    // Delete a mental state record by ID
    static async deleteMentalState(id) {
        const query = `DELETE FROM mental_state_monitoring WHERE id = ?`;

        try {
            const [result] = await pool.query(query, [id]);
            if (result.affectedRows === 0) {
                throw new Error('No record found to delete');
            }
            return { success: true };
        } catch (err) {
            console.error('Error deleting mental state record:', err.message);
            throw new Error('Failed to delete mental state record');
        }
    }
}

module.exports = UserMentalState;
