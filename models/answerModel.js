const pool = require('../config/db');
const { checkTableExist } = require('../utils/dbUtils');

class Answer {
    static async createTable() {
        const query = `
            CREATE TABLE IF NOT EXISTS answers (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                question_id INT NOT NULL,
                concerns_id INT NOT NULL,
                answer_text TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE,
                FOREIGN KEY (concerns_id) REFERENCES concerns(id) ON DELETE CASCADE
            );
        `;

        try {
            await checkTableExist('answers', query);
        } catch (err) {
            console.error('Error creating answers table:', err.message);
            throw new Error('Failed to create answers table');
        }
    }

    static async getTableLength() {
        try {
            const [rows] = await pool.execute('SELECT COUNT(*) as count FROM answers');
            return rows[0].count;
        } catch (err) {
            console.error('Error getting table length:', err.message);
            throw new Error('Failed to get table length');
        }
    }
    

    static async getById(id) {
        try {
            const [rows] = await pool.execute('SELECT * FROM answers WHERE id = ?', [id]);
            return rows[0];
        } catch (err) {
            console.error(`Error retrieving answer with ID ${id}:`, err.message);
            throw new Error('Failed to retrieve answer');
        }
    }

    static async getAll() {
        try {
            const [rows] = await pool.execute('SELECT * FROM answers');
            return rows;
        } catch (err) {
            console.error('Error retrieving all answers:', err.message);
            throw new Error('Failed to retrieve answers');
        }
    }

    static async createAnswer(userId, questionId, concernsId, answerText) {
        try {
            // Get the table length
            const length = await this.getTableLength();
    
            // Modify answer text if the table is empty
            const finalAnswerText = length === 0 ? `(First Answer) ${answerText}` : answerText;
    
            const query = `
                INSERT INTO answers (user_id, question_id, concerns_id, answer_text, created_at)
                VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
            `;
            const [result] = await pool.execute(query, [userId, questionId, concernsId, finalAnswerText]);
            return result.insertId; // Return the ID of the inserted answer
        } catch (error) {
            console.error('Error saving answer:', error.message);
            throw new Error('Failed to save answer');
        }
    }
    

    static async answerQuestion(userId, questionId, answerText) {
        try {
            // Validate input parameters
            if (!questionId || isNaN(Number(questionId))) {
                throw new Error('Invalid question ID provided.');
            }
            if (!userId || isNaN(Number(userId))) {
                throw new Error('Invalid user ID provided.');
            }
            if (typeof answerText !== 'string' || answerText.trim() === '') {
                throw new Error('Answer text must be a non-empty string.');
            }
    
            // Get the table length
            const length = await this.getTableLength();
    
            // Modify answer text if the table is empty
            const finalAnswerText = length === 0 ? `(First Answer) ${answerText}` : answerText;
    
            // Insert answer into the database
            const query = `
                INSERT INTO answers (user_id, question_id, answer_text, created_at)
                VALUES (?, ?, ?, CURRENT_TIMESTAMP)
            `;
            const [result] = await pool.execute(query, [userId, questionId, finalAnswerText]);
            return result.insertId; // Return the ID of the inserted answer
        } catch (err) {
            console.error('Error answering question:', err.message);
    
            // Provide a meaningful error message
            if (err.message.includes('foreign key constraint fails')) {
                throw new Error('The provided question ID or user ID does not exist.');
            }
    
            throw new Error('Failed to answer question');
        }
    }
    

    static async updateById(id, answerText) {
        try {
            await pool.execute(
                'UPDATE answers SET answer_text = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                [answerText, id]
            );
        } catch (err) {
            console.error(`Error updating answer with ID ${id}:`, err.message);
            throw new Error('Failed to update answer');
        }
    }

    static async delete(id) {
        try {
            await pool.execute('DELETE FROM answers WHERE id = ?', [id]);
        } catch (err) {
            console.error(`Error deleting answer with ID ${id}:`, err.message);
            throw new Error('Failed to delete answer');
        }
    }
}

module.exports = Answer;
