const pool = require('../config/db');
const { checkTableExist } = require('../utils/dbUtils');

class AppVersionModel {

    static async createTable() {
        const sql = `
        CREATE TABLE app_versions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            platform ENUM('android', 'ios') NOT NULL,
            version VARCHAR(20) NOT NULL,
            change_log TEXT,
            is_force_update BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
      `;

        try {
            await checkTableExist('app_versions', sql);
        } catch (err) {
            console.error('Error creating app_versions table:', err.message);
            throw new Error('Failed to create app_versions table');
        }
    }

    static async create(platform, version, change_log = '', is_force_update = false) {
        const sql = `
      INSERT INTO app_versions (platform, version, change_log, is_force_update)
      VALUES (?, ?, ?, ?)
    `;
        const [result] = await pool.execute(sql, [platform, version, change_log, is_force_update]);
        return result.insertId;
    }

    static async update(id, platform, version, change_log = '', is_force_update = false) {
        const sql = `
      UPDATE app_versions
      SET platform = ?, version = ?, change_log = ?, is_force_update = ?
      WHERE id = ?
    `;
        const [result] = await pool.execute(sql, [platform, version, change_log, is_force_update, id]);
        return result.affectedRows;
    }

    static async deleteById(id) {
        const sql = `DELETE FROM app_versions WHERE id = ?`;
        const [result] = await pool.execute(sql, [id]);
        return result.affectedRows;
    }

    static async getAll() {
        const sql = `SELECT * FROM app_versions `;
        const [rows] = await pool.execute(sql);
        return rows;
    }

    static async getById(id) {
        const sql = `SELECT * FROM app_versions WHERE id = ?`;
        const [rows] = await pool.execute(sql, [id]);
        return rows[0];
    }
}

module.exports = AppVersionModel;
