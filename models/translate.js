const pool = require('../config/db');
const { checkTableExist } = require('../utils/dbUtils');

class Translate {
    static async createTranslateTable() {
        if (await checkTableExist('translate')) {
            console.log('Translate table already exists');
        } else {
            const createTableQuery = ` 
            CREATE TABLE IF NOT EXISTS translate (
                id INT AUTO_INCREMENT PRIMARY KEY,
                status INT NOT NULL DEFAULT 1,
                locale VARCHAR(255) NOT NULL,
                group VARCHAR(255) NOT NULL,
                key VARCHAR(255) NOT NULL,
                value VARCHAR(255) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY translate_locale_group_key_unique (locale, group, key)
            )`;

            try {
                await pool.query(createTableQuery);
                console.log('Translate table created successfully');
            } catch (err) {
                console.error('Error creating Translate table:', err.message);
                throw new Error('Failed to create Translate table');
            }
        }
    }


    static async addTranslate(data) {
        const { locale, group, key, value } = data;
        const query = `INSERT INTO translate (locale, group, key, value) VALUES (?, ?, ?, ?)`;
        try {
            await pool.query(query, [locale, group, key, value]);
            console.log('Translate added successfully');
        } catch (err) {
            console.error('Error adding Translate:', err.message);
            throw new Error('Failed to add Translate');
        }
    }

    static async getTranslate(locale, group, key) {
        const query = `SELECT * FROM translate WHERE locale = ? AND group = ? AND key = ? WHERE status = 1`;
        try {
            const [rows] = await pool.query(query, [locale, group, key]);
            return rows[0];
        } catch (err) {
            console.error('Error getting Translate:', err.message);
            throw new Error('Failed to get Translate');
        }
    }

    static async getAllTranslates() {
        const query = `SELECT * FROM translate WHERE status = 1`;
        try {
            const [rows] = await pool.query(query);
            return rows;
        } catch (err) {
            console.error('Error getting all Translate:', err.message);
            throw new Error('Failed to get all Translate');
        }
    }

    static async updateTranslate(data) {
        const { id, locale, group, key, value } = data;
        const query = `UPDATE translate SET locale = ?, group = ?, key = ?, value = ? WHERE id = ? AND status = 1`;
        try {
            await pool.query(query, [locale, group, key, value, id]);
            console.log('Translate updated successfully');
        } catch (err) {
            console.error('Error updating Translate:', err.message);
            throw new Error('Failed to update Translate');
        }
    }

    static async deleteTranslate(id) {
        const query = `UPDATE status FROM translate WHERE id = ?`;
        try {
            await pool.query(query, [id]);
            console.log('Translate deleted successfully');
        } catch (err) {
            console.error('Error deleting Translate:', err.message);
            throw new Error('Failed to delete Translate');
        }
    }

}





module.exports = { Translate };