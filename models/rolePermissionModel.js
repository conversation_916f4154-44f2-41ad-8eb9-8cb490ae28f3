const pool = require('../config/db');
const { checkTableExist } = require('../utils/dbUtils');

class RolePermissionModel {
    // Create the role_permissions table if it doesn't exist
    static async createRolePermissionTable() {
        const query = `
            CREATE TABLE IF NOT EXISTS role_permissions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                role_id INT NOT NULL,
                permission_id INT NOT NULL,
                FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
                FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
                UNIQUE KEY role_permission_unique (role_id, permission_id)
            );
        `;

        try {
            await checkTableExist('role_permissions', query);
        } catch (err) {
            console.error('Error creating role permissions table:', err.message);
            throw new Error('Failed to create role permissions table');
        }
    }

    // Assign a permission to a role in the role_permissions table
    static async assignPermissionToRole(roleId, permissionId) {
        const query = `
            INSERT INTO role_permissions (role_id, permission_id)
            VALUES (?, ?)
        `;
        try {
            await pool.query(query, [roleId, permissionId]);
            return { status: 'success', message: `Assigned permission ID ${permissionId} to role ID ${roleId}` };
        } catch (err) {
            console.error('Error assigning permission to role:', err.message);
            throw new Error('Failed to assign permission to role');
        }
    }

    // Fetch all permissions assigned to a specific role
    static async getPermissionsByRole(roleId) {
        const query = `
            SELECT p.id, p.name, p.description
            FROM permissions p
            INNER JOIN role_permissions rp ON p.id = rp.permission_id
            WHERE rp.role_id = ?
        `;
        try {
            const [rows] = await pool.query(query, [roleId]);
            return rows;
        } catch (err) {
            console.error('Error fetching permissions for role:', err.message);
            throw new Error('Failed to fetch permissions for role');
        }
    }
}

module.exports = RolePermissionModel;
