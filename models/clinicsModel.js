const pool = require('../config/db');
const { checkTableExist } = require('../utils/dbUtils');

class ClinicModel {

    static async createClinicTable() {
        const query = `
            CREATE TABLE IF NOT EXISTS clinics (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,   
                clinic_name VARCHAR(255) NOT NULL,
                address_clinic VARCHAR(255) NOT NULL,       
                email_clinic VARCHAR(255) NOT NULL,
                phone_clinic VARCHAR(20) NOT NULL,
                link_whatsapp VARCHAR(255) DEFAULT NULL,
                link_telegram VARCHAR(255) DEFAULT NULL,
                link_instagram VARCHAR(255) DEFAULT NULL,
                link_youtube VARCHAR(255) DEFAULT NULL,
                link_facebook VARCHAR(255) DEFAULT NULL,
                link_twitter VARCHAR(255) DEFAULT NULL,
                link_tiktok VARCHAR(255) DEFAULT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            );
        `;
        try {
            await checkTableExist('clinics', query);
        } catch (err) {
            console.error('Error creating clinics table:', err.message);
            throw err;
        }
    }

    static async createClinic(
        user_id,
        clinic_name,
        address_clinic,
        email_clinic,
        phone_clinic,
        link_whatsapp = '',
        link_telegram = '',
        link_instagram = '',
        link_youtube = '',
        link_facebook = '',
        link_twitter = '',
        link_tiktok = ''
    ) {
        const query = `
            INSERT INTO clinics (
                user_id,
                clinic_name, 
                address_clinic, 
                email_clinic, 
                phone_clinic, 
                link_whatsapp, 
                link_telegram, 
                link_instagram, 
                link_youtube, 
                link_facebook, 
                link_twitter, 
                link_tiktok
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;

        const values = [
            user_id,
            clinic_name,
            address_clinic,
            email_clinic,
            phone_clinic,
            link_whatsapp || null,
            link_telegram || null,
            link_instagram || null,
            link_youtube || null,
            link_facebook || null,
            link_twitter || null,
            link_tiktok || null
        ];

        try {
            const [result] = await pool.query(query, values);
            return result;
        } catch (err) {
            console.error('Error creating clinic:', err.message);
            throw err;
        }
    }
    

    static async findOne(email) {
        const query = 'SELECT * FROM clinics WHERE email_clinic = ?'; // Update column name to email_clinic
        const values = [email];
    
        try {
            const [rows] = await pool.query(query, values);
            return rows.length > 0 ? rows[0] : null;
        } catch (err) {
            console.error('Error fetching admin by email:', err);
            throw err;
        }
    }
    

    static async getClinicById(clinicId) {
        const query = 'SELECT * FROM clinics WHERE id = ?';
        const values = [clinicId];
    
        try {
            const [rows] = await pool.query(query, values);
            return rows.length > 0 ? rows[0] : null; // Return the admin or null if not found
        } catch (err) {
            throw err;
        }
    }
    
    static async getAllClinics() {
        const query = 'SELECT * FROM clinics';
        try {
            const [rows] = await pool.query(query);
            return rows;
        } catch (err) {

            throw err;
        }
    }

    static async updateClinic(id, clinic_name, address_clinic, email, phone_clinic, link_whatsapp, 
        link_telegram, link_instagram, link_youtube, link_facebook, link_twitter, link_tiktok) {
        const query = `
            UPDATE clinics
            SET clinic_name = ?, address_clinic = ?, email_clinic = ?, phone_clinic = ?, link_whatsapp = ?, 
            link_telegram = ?, link_instagram = ?, link_youtube = ?, link_facebook = ?, link_twitter = ?, link_tiktok = ?
            WHERE id = ?
        `;
        const values = [
            clinic_name,
            address_clinic,
            email,
            phone_clinic,
            link_whatsapp,
            link_telegram,
            link_instagram,
            link_youtube,
            link_facebook,
            link_twitter,
            link_tiktok,
        ];

        try {
            const [result] = await pool.query(query, values);
            return result;
        } catch (err) {

            throw err;
        }
    }

    static async logoutClinic(id) {
        const query = 'UPDATE clinics SET token = NULL WHERE id = ?';
        const values = [id];
        try {
            const [result] = await pool.query(query, values);
            return result;
        } catch (err) {

            throw err;
        }
    }

    static async deactivateClinic(userId) {
        const query = `
            UPDATE clinics 
            SET is_active = false 
            WHERE id = ?
        `;
        const values = [userId];
    
        try {
            const [result] = await pool.query(query, values);
            return result;
        } catch (err) {

            throw err;
        }
    }
}

module.exports = ClinicModel;
