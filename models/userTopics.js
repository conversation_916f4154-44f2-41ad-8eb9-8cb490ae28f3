const pool = require('../config/db');
const {checkTableExist} = require('../utils/dbUtils');


class UserTopics {
    static async createUserTopicsTable() {
        const query = `
            CREATE TABLE IF NOT EXISTS user_topics (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                topic_id INT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (topic_id) REFERENCES topics(id) ON DELETE CASCADE
            );`;
        
        try {
            await checkTableExist('user_topics', query);
        } catch (err) {
            console.error('Error creating User Topics table:', err.message);
            throw new Error('Failed to create User Topics table');
        }
    }

    static async addUserTopics(userId, topicIds) {
        const query = `INSERT INTO user_topics (user_id, topic_id) VALUES (?, ?)`;
        const values = topicIds.map(topicId => [userId, topicId]);
    
        try {
            await pool.query(query, [values]);
        } catch (err) {
            console.error('Error adding user topics:', err.message);
            throw new Error('Failed to add user topics');
        }
    }
    

    static async getUserTopics(userId) {
        const query = `SELECT * FROM user_topics WHERE user_id = ?`;
        try {
            const [rows] = await pool.query(query, [userId]);
            return rows;
        } catch (err) {
            console.error('Error getting User Topics:', err.message);
            throw new Error('Failed to get User Topics');
        }
    }


    static async deleteUserTopics(userId) {
        const query = `DELETE FROM user_topics WHERE user_id = ?`;
        try {
            await pool.query(query, [userId]);
            console.log('User topics deleted successfully');
        } catch (err) {
            console.error('Error deleting user topics:', err.message);
            throw new Error('Failed to delete user topics');
        }
    }
}

module.exports = UserTopics;