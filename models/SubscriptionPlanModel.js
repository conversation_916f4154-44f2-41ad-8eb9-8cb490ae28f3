const pool = require("../config/db");
const { checkTableExist } = require("../utils/dbUtils");
class SubscriptionPlanModel {
    static async createTable() {
        const query = `
            CREATE TABLE IF NOT EXISTS subscription_plans (
                id INT AUTO_INCREMENT PRIMARY KEY,
                package_id INT NOT NULL,
                type ENUM('monthly', 'yearly') NOT NULL,
                price DECIMAL(10,2) NOT NULL,
                FOREIGN KEY (package_id) REFERENCES subscription_packages(id) ON DELETE CASCADE
            );
        `;

        try {
            await checkTableExist('subscription_plans', query);
        } catch (err) {
            console.error('Error creating subscription_plans table:', err.message);
            throw new Error('Failed to create subscription_plans table');
        }
    }

    static async getAllPlans() {
        const [rows] = await pool.query("SELECT * FROM subscription_plans");
        return rows;
    }

    static async getPlansByPackageId(package_id) {
        const [rows] = await pool.query("SELECT * FROM subscription_plans WHERE package_id = ?", [package_id]);
        return rows;
    }
}

module.exports = SubscriptionPlanModel;
