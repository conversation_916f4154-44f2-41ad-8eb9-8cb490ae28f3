const pool = require("../config/db");
const bcrypt = require("bcryptjs");
const crypto = require("crypto");
const nodemailer = require("nodemailer");
require("dotenv").config();
const { checkTableExist } = require("../utils/dbUtils");

class UserModel {
  // Function to create the User table
  static async createUserTable() {
    const query = `
			CREATE TABLE IF NOT EXISTS users (
				id INT AUTO_INCREMENT PRIMARY KEY,
				name VARCHAR(100) NOT NULL,
				nickname VARCHAR(100) DEFAULT NULL,
				email VARCHAR(100) UNIQUE NOT NULL,
				password VARCHAR(255) NOT NULL,
				gender VARCHAR(10) DEFAULT NULL,
				phone VARCHAR(20) UNIQUE DEFAULT NULL,
				age INT DEFAULT NULL,
				fcmToken VARCHAR(255) DEFAULT NULL,
        reset_token VARCHAR(255) DEFAULT NULL,
        reset_token_expiry DATETIME DEFAULT NULL,
				is_active BOOLEAN DEFAULT 1,
				created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
				updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
			);
		`;

    try {
      await checkTableExist("users", query);
    } catch (err) {
      console.error("Error creating Users table:", err.message);
      throw new Error("Failed to create users table");
    }
  }




  static async getTableLength() {
    try {
      const [rows] = await pool.query("SELECT COUNT(*) as count FROM users");
      return rows[0].count;
    } catch (err) {
      console.error("Error getting table length:", err.message);
      throw new Error("Failed to get table length");
    }
  }

  // Function to create a new user
  static async createUser(name, nickname, email, age, gender, phone, password) {
    try {
      // Get the table length
      const length = await this.getTableLength();

      // Conditional logic based on table length
      const finalName = length === 0 ? `${name} (First User)` : name;

      const query = `
				INSERT INTO users (name, nickname, email, age, gender, phone, password) 
				VALUES (?, ?, ?, ?, ?, ?, ?)
			`;
      const values = [
        finalName,
        nickname,
        email,
        age,
        gender,
        phone,
        password,
      ];

      const [result] = await pool.query(query, values);
      return result;
    } catch (err) {
      console.error("Error creating user:", err.message);
      throw new Error("Failed to create user");
    }
  }

  //  // Function to get user by email
  // static async getUserByEmail(email) {
  //   const [rows] = await pool.query(
  //     'SELECT * FROM users WHERE LOWER(email) = LOWER(?) LIMIT 1',
  //     [email]
  //   );
  //   return rows[0] || null;
  // }

  static async getUserByPhone(phone) {
    const [rows] = await pool.query(
      'SELECT * FROM users WHERE phone = ? LIMIT 1',
      [phone]
    );
    return rows[0];
  }

  static async getUserByPhone(phone) {
    const [rows] = await pool.query(
      'SELECT * FROM users WHERE phone = ? LIMIT 1',
      [phone]
    );
    return rows[0];
  }

  // Function to get user by email
  static async getUserByEmail(email) {
    const query = "SELECT * FROM users WHERE email = ?";

    try {
      const [rows] = await pool.query(query, [email]);
      return rows.length > 0 ? rows[0] : null;
    } catch (err) {
      console.error("Error fetching user by email:", err.message);
      throw new Error("Failed to fetch user by email");
    }
  }

  // Function to get user by email and role
  static async getClinicByUserId(userId) {
    const query = `
			SELECT c.*
			FROM clinics c
			JOIN users u ON u.id = c.user_id
			WHERE u.id = ?
		`;

    try {
      const [rows] = await pool.query(query, [userId]);
      return rows.length > 0 ? rows[0] : null;
    } catch (error) {
      console.error("Error fetching clinic by user ID:", error.message);
      throw new Error("Failed to fetch clinic by user ID");
    }
  }

  // Function to get user by ID
  static async getUserById(userId) {
    const query = "SELECT * FROM users WHERE id = ?";
    try {
      const [rows] = await pool.query(query, [userId]);
      return rows.length > 0 ? rows[0] : null;
    } catch (err) {
      console.error("Error fetching user by ID:", err.message);
      throw new Error("Failed to fetch user by ID");
    }
  }

  // Update user details by ID
  static async updateUserById(
    userId,
    name,
    nickname,
    email,
    phone,
    gender,
    age,
    password
  ) {
    const updates = [];
    const values = [];

    if (name !== undefined) {
      updates.push("name = ?");
      values.push(name);
    }
    if (nickname !== undefined) {
      updates.push("nickname = ?");
      values.push(nickname);
    }
    if (email !== undefined) {
      updates.push("email = ?");
      values.push(email);
    }
    if (password !== undefined) {
      const hashedPassword = await bcrypt.hash(password, 10);
      updates.push("password = ?");
      values.push(hashedPassword);
    }
    if (phone !== undefined) {
      const phoneRegex = /^\+?[0-9\s\-\(\)]{7,20}$/;
      if (!phoneRegex.test(phone)) {
        throw new Error("Invalid phone number format");
      }
      updates.push("phone = ?");
      values.push(phone);
    }
    if (gender !== undefined) {
      updates.push("gender = ?");
      values.push(gender);
    }
    if (age !== undefined) {
      updates.push("age = ?");
      values.push(age);
    }

    if (updates.length === 0) {
      throw new Error("No fields to update");
    }

    const query = `
			UPDATE users 
			SET ${updates.join(", ")}
			WHERE id = ?
		`;
    values.push(userId);

    try {
      const [result] = await pool.query(query, values);
      return result;
    } catch (err) {
      console.error("Error updating user:", err.message);
      throw new Error("Failed to update user");
    }
  }

  // Function to get all users
  static async getAllUsers() {
    const query = "SELECT * FROM users";

    try {
      const [rows] = await pool.query(query);
      return rows;
    } catch (err) {
      console.error("Error fetching users:", err.message);
      throw new Error("Failed to fetch users");
    }
  }

  // toggle status
  static async updateUserStatus(userId, isActive) {
    const query = "UPDATE users SET is_active = ? WHERE id = ?";
    try {
      const [result] = await pool.query(query, [isActive, userId]);
      return result;
    } catch (err) {
      console.error("Error updating user status:", err.message);
      throw new Error("Failed to update user status");
    }
  }


  // Function to delete (deactivate) a user by ID
  static async deleteUserById(userId) {
    const query = `
			DELETE FROM users 
      WHERE id = ?
		`;

    try {
      const [result] = await pool.query(query, [userId]);
      return result;
    } catch (err) {
      console.error("Error deactivating user:", err.message);
      throw new Error("Failed to deactivate user");
    }
  }

  // function to change password
  static async changePassword(userId, oldPassword, newPassword) {
    try {
      console.log("🔧 Step 1: Fetch user by ID", userId);
      const [rows] = await pool.query(
        `SELECT * FROM users WHERE id = ? AND is_active = true LIMIT 1`,
        [userId]
      );

      if (rows.length === 0) {
        console.log("❌ No user found");
        return { affectedRows: 0 };
      }

      const user = rows[0];
      console.log("👤 User found:", user);

      const isMatch = await bcrypt.compare(oldPassword, user.password);
      console.log("🔐 Password match result:", isMatch);

      if (!isMatch) {
        console.log("❌ Passwords do not match");
        return { affectedRows: 0 };
      }

      const hashedPassword = await bcrypt.hash(newPassword, 10);
      console.log("✅ New hashed password created");

      const [updateResult] = await pool.query(
        `UPDATE users SET password = ? WHERE id = ?`,
        [hashedPassword, userId]
      );

      console.log("✅ Password updated in DB");
      return updateResult;
    } catch (error) {
      console.error("🔥 Error changing password:", error);
      throw new Error("Failed to change password");
    }
  }

  // Forgot Password function
  static async forgotPassword(email) {
    const query = `
			SELECT * FROM users
			WHERE email = ? 
			AND password IS NOT NULL 
			AND is_active = true
			LIMIT 1
		`;
    const values = [email];

    try {
      const [rows] = await pool.query(query, values);

      if (rows.length === 0) {
        return {
          success: false,
          message: "No active user with this email found",
        };
      }

      const user = rows[0];
      const resetToken = crypto.randomBytes(16).toString("hex");
      const resetTokenExpiry = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes from now

      await pool.query(
        `
				UPDATE users
				SET reset_token = ?, reset_token_expiry = ?
				WHERE id = ?
			`,
        [resetToken, resetTokenExpiry, user.id]
      );

      const transporter = nodemailer.createTransport({
        host: "smtp.hostinger.com",
        port: 587,
        secure: false,
        auth: {
          user: process.env.EMAIL_USER,
          pass: process.env.EMAIL_PASS,
        },
      });

      const resetLink = `https://arab-cbt.com/reset-password/${resetToken}`;

      const mailOptions = {
        from: process.env.EMAIL_USER,
        to: user.email,
        subject: "Password Reset Request",
        text: `Hello ${user.name},\n\nYou requested a password reset. Please use the following link to reset your password: ${resetLink}\n\nThis link will expire in 15 minutes.\n\nBest regards,\nYour Team`,
      };

      await transporter.sendMail(mailOptions);

      return {
        success: true,
        message: "Password reset instructions sent to your email",
      };
    } catch (err) {
      console.error(
        "Error generating reset token or sending email:",
        err.message
      );
      throw new Error("Failed to generate reset token or send email");
    }
  }

  static async getUserByIdAndRole(userId, roleId) {
    const sql = `
			SELECT users.* 
			FROM users 
			JOIN user_roles ON users.id = user_roles.user_id 
			WHERE users.id = ? AND user_roles.role_id = ? 
			LIMIT 1
		`;
    try {
      const [rows] = await pool.query(sql, [userId, roleId]);

      return rows.length > 0 ? rows[0] : null;
    } catch (error) {
      console.error("Database error in getUserByIdAndRole:", error.message);
      throw new Error("Failed to fetch user by ID and role");
    }
  }

  // check topic exists or not
  static async checkTopicExists(topicId) {
    const query = "SELECT * FROM topics WHERE id = ?";
    try {
      const [rows] = await pool.query(query, [topicId]);
      return rows.length > 0;
    } catch (err) {
      console.error("Error checking topic exists:", err.message);
      throw new Error("Failed to check topic exists");
    }
  }
}

module.exports = UserModel;