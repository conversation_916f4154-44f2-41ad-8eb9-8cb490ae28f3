const pool = require('../config/db');
const { checkTableExist } = require('../utils/dbUtils');
const { replaceOrUpdateColumn } = require('../helper/replaceColumn');
const { columnExistsInTable } = require('../helper/columnExist');

class LecturesModel {
    // ✅ Create table if it doesn't exist
    static async createLecturesTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS lectures (
                id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
                text VARCHAR(255) NOT NULL,
                description TEXT DEFAULT NULL,
                image VARCHAR(255) NOT NULL,
                lecture_id INT DEFAULT NULL,
                is_active BOOLEAN DEFAULT 1,
                type ENUM('category', 'sub_category') NOT NULL DEFAULT 'category',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (lecture_id) REFERENCES lectures(id) ON DELETE CASCADE
            )
        `;
        try {
            await checkTableExist('lectures', sql);

            await columnExistsInTable('lectures', 'price').then(async (exists) => {
                if (!exists) {
                    await replaceOrUpdateColumn({
                        tableName: 'lectures',
                        newColumn: 'price',
                        newDefinition: 'DECIMAL(10,2) DEFAULT NULL',
                        afterColumn: 'image',
                    });
                }
            });

        } catch (err) {
            console.error('Error creating lectures table:', err.message);
            throw new Error('Failed to create lectures table');
        }
    }

    // ✅ Get all categories and sub-categories grouped
    static async getAllLectures() {
        const sql = `
        SELECT 
            category.id AS category_id,
            category.text AS category_text,
            category.description AS category_description,
            category.type AS category_type,
            category.image AS category_image,
            category.is_active AS category_is_active,
            category.created_at AS category_created_at,
            category.updated_at AS category_updated_at,

            sub_category.id AS sub_category_id,
            sub_category.text AS sub_category_text,
            sub_category.description AS sub_category_description,
            sub_category.type AS sub_category_type,
            sub_category.image AS sub_category_image,
            sub_category.price AS sub_category_price,
            sub_category.created_at AS sub_category_created_at,
            sub_category.updated_at AS sub_category_updated_at
        FROM lectures category
        LEFT JOIN lectures sub_category 
            ON category.id = sub_category.lecture_id AND sub_category.type = 'sub_category'
        WHERE category.type = 'category'
        ORDER BY category.id ASC, sub_category.id ASC
    `;

        try {
            const [rows] = await pool.query(sql);

            const grouped = rows.reduce((acc, row) => {
                let existing = acc.find(cat => cat.id === row.category_id);

                const sub = row.sub_category_id
                    ? {
                        id: row.sub_category_id,
                        text: row.sub_category_text,
                        description: row.sub_category_description,
                        type: row.sub_category_type,
                        image: row.sub_category_image,
                        created_at: row.sub_category_created_at,
                        updated_at: row.sub_category_updated_at,
                    }
                    : null;

                if (existing) {
                    if (sub) existing.sub_categories.push(sub);
                } else {
                    acc.push({
                        id: row.category_id,
                        text: row.category_text,
                        description: row.category_description,
                        type: row.category_type,
                        image: row.category_image,
                        price: row.sub_category_price,
                        is_active: row.category_is_active,
                        created_at: row.category_created_at,
                        updated_at: row.category_updated_at,
                        sub_categories: sub ? [sub] : [],
                    });
                }

                return acc;
            }, []);

            return grouped;
        } catch (err) {
            console.error('Error getting all lectures:', err.message);
            throw new Error('Failed to get all lectures');
        }
    }


    // ✅ Create a category or sub-category
    static async createLecture(text, description, type, image) {
        const sql = `INSERT INTO lectures (text, description, type, image, created_at, updated_at) 
                     VALUES (?, ?, ?, ?, NOW(), NOW())`;
        try {
            const [result] = await pool.query(sql, [text, description || null, type, image]);
            return result.insertId;
        } catch (err) {
            console.error('Error creating lecture:', err.message);
            throw new Error('Failed to create lecture');
        }
    }

    // ✅ Create a sub-lecture (with parent lecture_id)
    static async createSubLecture({ text, description, type, video, lectureId, price = 0 }) {
        const sql = `
        INSERT INTO lectures (text, description, type, image, lecture_id, price)
        VALUES (?, ?, ?, ?, ?, ?)`;
        const values = [text, description || null, type, video, lectureId, price];
        try {
            const [result] = await pool.query(sql, values);
            return result.insertId;
        } catch (err) {
            console.error('Error creating SubLecture:', err.message);
            throw new Error('Failed to create SubLecture');
        }
    }



    // ✅ Get lecture by ID
    static async getLectureById(id) {
        const sql = `SELECT * FROM lectures WHERE id = ?`;
        try {
            const [rows] = await pool.query(sql, [id]);
            return rows.length > 0 ? rows[0] : null;
        } catch (err) {
            console.error(`Error fetching lecture by ID ${id}:`, err.message);
            throw new Error('Failed to fetch lecture');
        }
    }

    // ✅ Update a lecture
    static async updateLecture(id, text, description, image) {
        const sql = `UPDATE lectures SET text = ?, description = ?, image = ? WHERE id = ?`;
        try {
            await pool.query(sql, [text, description || null, image, id]);
        } catch (err) {
            console.error('Error updating lecture:', err.message);
            throw new Error('Failed to update lecture');
        }
    }

    // ✅ Update a sub-lecture
    static async updateSubLecture(id, text, description, type, image, price) {
        const sql = `UPDATE lectures SET text = ?, description = ?, type = ?, image = ?, price = ? WHERE id = ?`;
        try {
            await pool.query(sql, [text, description || null, type, image, price, id]);
        } catch (err) {
            console.error('Error updating SubLecture:', err.message);
            throw new Error('Failed to update SubLecture');
        }
    }

    // put is_active
    static async updateLectureStatus(id, isActive) {
        console.log("Updating Lecture ID:", id, "New Status:", isActive);
        const sql = `UPDATE lectures SET is_active = ? WHERE id = ?`;
        try {
            await pool.query(sql, [isActive, id]);
        } catch (err) {
            console.error('Error updating lecture status:', err.message);
            throw new Error('Failed to update lecture status');
        }
    }

    // ✅ Get all sub-lectures for a category
    static async getSubLecturesByLectureId(lecture_id) {
        const sql = `SELECT * FROM lectures WHERE lecture_id = ? AND type = 'sub_category'`;
        try {
            const [rows] = await pool.query(sql, [lecture_id]);
            return rows;
        } catch (err) {
            console.error('Error fetching SubLectures:', err.message);
            throw new Error('Failed to fetch SubLectures');
        }
    }

    // ✅ Get sub-lectures by lecture title
    static async getSubLecturesByLectureText(text) {
        const sql = `
            SELECT id, text, description, image, lecture_id, is_active, type, created_at, updated_at
            FROM lectures
            WHERE text COLLATE utf8mb4_general_ci = ?
        `;
        try {
            const [rows] = await pool.query(sql, [text]);
            return rows;
        } catch (err) {
            console.error(`Error fetching SubLectures for text "${text}":`, err.message);
            throw new Error(`Failed to fetch SubLectures`);
        }
    }

    // ✅ Update is_active status
    static async updateStatus(id, isActive) {
        const sql = `UPDATE lectures SET is_active = ? WHERE id = ?`;
        try {
            const [result] = await pool.query(sql, [isActive, id]);
            return result;
        } catch (err) {
            console.error('Error updating status:', err.message);
            throw new Error('Failed to update lecture status');
        }
    }


    // ✅ Delete lecture (or sub-lecture)
    static async deleteLecture(id) {
        const sql = `DELETE FROM lectures WHERE id = ?`;
        try {
            await pool.query(sql, [id]);
        } catch (err) {
            console.error('Error deleting Lecture:', err.message);
            throw new Error('Failed to delete Lecture');
        }
    }

    static async deleteSubLecture(id) {
        return this.deleteLecture(id); // Reuse the deleteLecture method
    }

    static async getUserPurchasedLectures(userId) {
        const query = `
      SELECT 
        lp.id AS purchase_id,
        lp.price,
        lp.currency,
        lp.purchased_at,
        l.id AS lecture_id,
        l.title,
        l.description,
        l.thumbnail,
        l.price AS original_price
      FROM lecture_purchases lp
      JOIN lectures l ON lp.lecture_id = l.id
      WHERE lp.user_id = ?
      ORDER BY lp.purchased_at DESC
    `;
        const [rows] = await pool.query(query, [userId]);
        return rows;
    }

    static async isPurchased(userId, lectureId) {
        const [rows] = await pool.query(
            "SELECT id FROM lecture_purchases WHERE user_id = ? AND lecture_id = ?",
            [userId, lectureId]
        );
        return rows.length > 0;
    }

}

module.exports = LecturesModel;
