const pool = require('../config/db');
const { checkTableExist } = require('../utils/dbUtils');

class UserRoleModel {
    // Function to create the UserRole table
    static async createUserRoleTable() {
        const query = `
            CREATE TABLE IF NOT EXISTS user_roles (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                role_id INT NOT NULL,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
                UNIQUE KEY user_role_unique (user_id, role_id)
            );
        `;

        try {
            await checkTableExist('user_roles', query);

        } catch (err) {
            console.error('Error creating user roles table:', err);
            throw err;
        }
    }

    // Function to assign a role to a user
    static async assignRoleToUser(userId, roleId) {
        const query = `
            INSERT INTO user_roles (user_id, role_id)
            VALUES (?, ?)
            ON DUPLICATE KEY UPDATE user_id = user_id
        `;

        try {
            await pool.query(query, [userId, roleId]);
        } catch (err) {
            console.error('Error assigning role to user:', err.message);
            throw new Error('Failed to assign role to user');
        }
    }

    // Function to get roles by user ID
    static async getRolesByUserId(userId) {
        const query = `
            SELECT roles.id, roles.name 
            FROM roles 
            INNER JOIN user_roles ON roles.id = user_roles.role_id 
            WHERE user_roles.user_id = ?
        `;

        try {
            const [rows] = await pool.query(query, [userId]);
            return rows;
        } catch (err) {
            console.error('Error fetching roles for user:', err.message);
            throw new Error('Failed to fetch roles for user');
        }
    }

    static async getRoleByUserId(userId) {
        const [rows] = await pool.query(
            'SELECT role_id FROM user_roles WHERE user_id = ? LIMIT 1',
            [userId]
        );
        return rows[0] ?? { role_id: 3 };
    }

    // Function to update the user role
    static async updateUserRoleById(userId, roleId) {
        const query = `
            UPDATE user_roles
            SET role_id = ?
            WHERE user_id = ?
        `;

        try {
            await pool.query(query, [roleId, userId]);
        } catch (err) {
            console.error('Error updating user role:', err.message);
            throw new Error('Failed to update user role');
        }
    }

    // يجب أن تضيف هذا إذا غير موجود:
    static async getUserRoleByUserId(userId) {
        const [rows] = await pool.query(`SELECT role_id FROM user_roles WHERE user_id = ? LIMIT 1`, [userId]);
        return rows[0]; // يرجع { role_id: 1 } مثلاً
    }


    // Function to update the admin role
    static async updateAdminRoleByUserId(userId, roleId) {
        const query = `
            UPDATE user_roles
            SET role_id = ?
            WHERE user_id = ?
        `;

        try {
            await pool.query(query, [roleId, userId]);
            console.log('Admin role updated successfully');
        } catch (err) {
            console.error('Error updating admin role:', err.message);
            throw new Error('Failed to update admin role');
        }
    }
}

module.exports = UserRoleModel;
