const pool = require('../config/db');
const { checkTableExist } = require('../utils/dbUtils');

class DistractionModel {
    static async createTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS distraction (
                id INT PRIMARY KEY AUTO_INCREMENT,
                title VARCHAR(255) NOT NULL,
                description VARCHAR(255) DEFAULT NULL,
                image VARCHAR(255) DEFAULT NULL,
                type ENUM('category', 'sub_category') NOT NULL DEFAULT 'category',
                is_active BOOLEAN DEFAULT 1,
                distraction_id INT DEFAULT NULL,
                audio_url VARCHAR(255) DEFAULT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (distraction_id) REFERENCES distraction(id) ON DELETE CASCADE
            )
        `;
        try {
            await checkTableExist('distraction', sql);
        } catch (err) {
            console.error('Error creating distraction table:', err.message);
            throw new Error('Failed to create distraction table');
        }
    }

    static async create({ title, image, description, audio_url = null }) {
        try {
            const sql = `
                INSERT INTO distraction (title, image, description, audio_url)
                VALUES (?, ?, ?, ?)
            `;
            const [result] = await pool.query(sql, [title, image, description, audio_url]);
            return result.insertId; // Return the new ID
        } catch (error) {
            console.error("Database error during creation:", error.message);
            throw error;
        }
    }
    
    static async createSubDistraction({ title, audio_url = null, distraction_id, description = "" }) {
        try {
            const sql = `
                INSERT INTO distraction (title, audio_url, distraction_id, description)
                VALUES (?, ?, ?, ?)
            `;
            const [result] = await pool.query(sql, [title, audio_url, distraction_id, description || null]);
            return result.insertId; // Return the new ID
        } catch (error) {
            console.error('Error creating sub-distraction:', error.message);
            throw new Error('Failed to create sub-distraction');
        }
    }

    static async getAll() {
        try {
            const sql = `
                SELECT
                    d1.id AS distraction_id,
                    d1.title AS distraction_title,
                    d1.description AS distraction_description,
                    d1.type AS type,
                    d1.image AS distraction_image,
                    d1.is_active AS distraction_is_active,
                    d1.created_at AS distraction_created_at,
                    d1.updated_at AS distraction_updated_at,
                    d2.id AS sub_distraction_id,
                    d2.title AS sub_distraction_title,
                    d2.type AS sub_type,
                    d2.audio_url AS sub_distraction_audio_url,
                    d2.created_at AS sub_distraction_created_at,
                    d2.updated_at AS sub_distraction_updated_at
                FROM distraction d1
                LEFT JOIN distraction d2 ON d1.id = d2.distraction_id
                WHERE d2.id IS NOT NULL
            `;
    
            const [rows] = await pool.query(sql);
    
            // Group distractions with sub-distractions
            const groupedData = rows.reduce((acc, row) => {
                // Find the existing distraction in the accumulator
                let existingDistraction = acc.find(
                    (distraction) => distraction.id === row.distraction_id
                );
    
                // If the distraction already exists in the accumulator, add sub-distractions
                if (existingDistraction) {
                    if (row.sub_distraction_id) {
                        existingDistraction.audio_url.push({
                            id: row.sub_distraction_id,
                            distraction_id: row.distraction_id,
                            title: row.sub_distraction_title,
                            type: row.sub_type,  // Add type field for sub-distraction
                            audio_url: row.sub_distraction_audio_url,
                            created_at: row.sub_distraction_created_at,
                            updated_at: row.sub_distraction_updated_at,
                        });
                    }
                } else {
                    // If the distraction doesn't exist, create a new distraction
                    const newDistraction = {
                        id: row.distraction_id,
                        title: row.distraction_title,
                        description: row.distraction_description,
                        image: row.distraction_image,
                        type: row.type,  // Add type field for the main distraction
                        is_active: row.distraction_is_active,
                        audio_url: row.sub_distraction_id
                            ? [{
                                id: row.sub_distraction_id,
                                distraction_id: row.distraction_id,
                                title: row.sub_distraction_title,
                                type: row.sub_type,  // Add type field for sub-distraction
                                audio_url: row.sub_distraction_audio_url,
                                created_at: row.sub_distraction_created_at,
                                updated_at: row.sub_distraction_updated_at,
                            }]
                            : [],  // Initialize an empty array if no sub-distraction
                        created_at: row.distraction_created_at,
                        updated_at: row.distraction_updated_at,
                    };
    
                    acc.push(newDistraction); // Add the new distraction to the accumulator
                }
    
                return acc;
            }, []);
    
            // Return only the distractions that have sub-distractions
            return groupedData ; // Wrap the result in a `data` field
        } catch (error) {
            console.error("Error fetching all distractions:", error.message);
            throw new Error("Failed to fetch all distractions");
        }
    }
    

    static async getAllSubDistraction(distractionId) {
        try {
            const sql = `
                SELECT 
                    id, title, description, type, is_active, audio_url, created_at, updated_at 
                FROM distraction 
                WHERE distraction_id = ?
            `;
            const [rows] = await pool.query(sql, [distractionId]);
    
            const result = {
                distraction_id: distractionId,
                sub_distractions: rows.map(row => ({
                    id: row.id,
                    title: row.title,
                    description: row.description || null,
                    is_active: row.is_active,
                    audio_url: row.audio_url || null,
                    type: row.type,
                    created_at: row.created_at,
                    updated_at: row.updated_at
                }))
            };
    
            return result;
    
        } catch (error) {
            console.error(`Error fetching sub-distractions for distraction_id ${distractionId}:`, error);
            throw new Error("Failed to fetch sub-distractions. Please try again later.");
        }
    }
    
    
    static async getById(id) {
        try {
            // First, fetch the main distraction
            const sql = `
                SELECT 
                    d1.id AS distraction_id, 
                    d1.title AS distraction_title, 
                    d1.description AS distraction_description, 
                    d1.type AS type,
                    d1.image AS distraction_image, 
                    d1.is_active AS distraction_is_active, 
                    d1.created_at AS distraction_created_at, 
                    d1.updated_at AS distraction_updated_at, 
                    d2.id AS sub_distraction_id, 
                    d2.title AS sub_distraction_title, 
                    d2.type AS sub_type,
                    d2.audio_url AS sub_distraction_audio_url, 
                    d2.created_at AS sub_distraction_created_at, 
                    d2.updated_at AS sub_distraction_updated_at
                FROM distraction d1
                LEFT JOIN distraction d2 ON d1.id = d2.distraction_id
                WHERE d1.id = ?
            `;
            const [rows] = await pool.query(sql, [id]);
    
            // If no distraction is found, throw an error
            if (rows.length === 0) {
                throw new Error(`No distraction found with id: ${id}`);
            }
    
            // Group distractions with sub-distractions
            const groupedData = rows.reduce((acc, row) => {
                let existingDistraction = acc.find(
                    (distraction) => distraction.id === row.distraction_id
                );
    
                if (existingDistraction) {
                    // Add sub-distraction if it exists
                    if (row.sub_distraction_id) {
                        existingDistraction.audio_url.push({
                            id: row.sub_distraction_id,
                            distraction_id: row.distraction_id,
                            title: row.sub_distraction_title,
                            type: row.sub_type,  // Add type field for sub-distraction
                            audio_url: row.sub_distraction_audio_url,
                            created_at: row.sub_distraction_created_at,
                            updated_at: row.sub_distraction_updated_at,
                        });
                    }
                } else {
                    // Create the new distraction and add the sub-distractions
                    acc.push({
                        id: row.distraction_id,
                        title: row.distraction_title,
                        description: row.distraction_description,
                        image: row.distraction_image,
                        type: row.type,  // Add type field for the main distraction
                        is_active: row.distraction_is_active,
                        audio_url: row.sub_distraction_id
                            ? [{
                                id: row.sub_distraction_id,
                                distraction_id: row.distraction_id,
                                title: row.sub_distraction_title,
                                type: row.sub_type,  // Add type field for sub-distraction
                                audio_url: row.sub_distraction_audio_url,
                                created_at: row.sub_distraction_created_at,
                                updated_at: row.sub_distraction_updated_at,
                            }]
                            : [],
                        created_at: row.distraction_created_at,
                        updated_at: row.distraction_updated_at,
                    });
                }
    
                return acc;
            }, []);
    
            // Return the first result (since there's only one distraction)
            return groupedData[0] ;
        } catch (error) {
            console.error(`Error fetching distraction by ID (${id}):`, error.message);
            throw new Error("Failed to fetch distraction");
        }
    }
    
    
    static async update(id, { title, image, description }) {
        try {
            // Build dynamic update query
            const fields = [];
            const values = [];
    
            if (title) {
                fields.push('title = ?');
                values.push(title);
            }
    
            if (image) {
                fields.push('image = ?');
                values.push(image);
            }
    
            if (description) {
                fields.push('description = ?');
                values.push(description);
            }
    
            if (fields.length === 0) {
                throw new Error('No fields to update.');
            }
    
            const sql = `
                UPDATE distraction
                SET ${fields.join(', ')}, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            `;
            values.push(id);
    
            const [result] = await pool.query(sql, values);
    
            if (result.affectedRows === 0) {
                throw new Error(`No distraction found with id: ${id}`);
            }
    
            return { message: "Distraction updated successfully", affectedRows: result.affectedRows };
        } catch (error) {
            console.error(`Error updating distraction with ID (${id}):`, error.message);
            throw new Error("Failed to update distraction");
        }
    }

    static async subUpdate(id, { title, audio_url }) {
        try {
            const sql = `
                UPDATE distraction
                SET title = ?, audio_url = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            `;
            const [result] = await pool.query(sql, [title, audio_url, id]);
    
            if (result.affectedRows === 0) {
                throw new Error(`No distraction found with id: ${id}`);
            }
    
            return { message: "Sub-distraction updated successfully", affectedRows: result.affectedRows };
        } catch (error) {
            console.error(`Error updating sub-distraction with ID (${id}):`, error.message);
            throw new Error("Failed to update sub-distraction");
        }
    }


    static async getImageById(id) {
        try {
            const sql = `
                SELECT 
                    id, 
                    title, 
                    description, 
                    image,
                FROM distraction 
                WHERE id = ?
            `;
            const [rows] = await pool.query(sql, [id]);
            return rows[0]; // Return the first record
        } catch (error) {
            console.error(`Error fetching distraction by ID (${id}):`, error.message);
            throw new Error("Failed to fetch distraction");
        }
    }

    static async getAudioById(id) {
        try {
            const sql = `
                SELECT 
                    id, 
                    title, 
                    audio_url
                FROM distraction 
                WHERE id = ?
            `;
            const [rows] = await pool.query(sql, [id]);
            return rows[0]; // Return the first record
        } catch (error) {
            console.error(`Error fetching distraction by ID (${id}):`, error.message);
            throw new Error("Failed to fetch distraction");
        }
    }
    
    

    static async delete(id) {
        try {
            const sql = "DELETE FROM distraction WHERE id = ?";
            const [result] = await pool.query(sql, [id]);
    
            if (result.affectedRows === 0) {
                throw new Error(`No distraction found with id: ${id}`);
            }
    
            return { message: "Distraction deleted successfully", affectedRows: result.affectedRows };
        } catch (error) {
            console.error(`Error deleting distraction with ID (${id}):`, error.message);
            throw new Error("Failed to delete distraction");
        }
    }

    static async deleteSubDistractionById(id) {
        try {
            const sql = "DELETE FROM distraction WHERE id = ?";
            const [result] = await pool.query(sql, [id]);
    
            if (result.affectedRows === 0) {
                throw new Error(`No sub-distraction found with id: ${id}`);
            }
    
            return { message: "Sub-distraction deleted successfully", affectedRows: result.affectedRows };
        } catch (error) {
            console.error(`Error deleting sub-distraction with ID (${id}):`, error.message);
            throw new Error("Failed to delete sub-distraction");
        }
    }
    
}

module.exports = DistractionModel;
