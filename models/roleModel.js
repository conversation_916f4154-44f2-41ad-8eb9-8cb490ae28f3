const pool = require('../config/db');
const { checkTableExist } = require('../utils/dbUtils');

class RoleModel {
   static async createRoleTable() {
    const createTableQuery = `
        CREATE TABLE IF NOT EXISTS roles (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(50) UNIQUE NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        );
    `;

    try {
        await checkTableExist('roles', createTableQuery);

        const roles = ['admin', 'user', 'guest'];

        for (const role of roles) {
            const checkRoleQuery = `SELECT COUNT(*) AS count FROM roles WHERE name = ?`;
            const [existingRole] = await pool.query(checkRoleQuery, [role]);

            if (existingRole[0].count === 0) {
                const insertRoleQuery = `INSERT INTO roles (name) VALUES (?)`;
                await pool.query(insertRoleQuery, [role]);
            } else {
                console.log(`Role "${role}" already exists, skipping insertion.`);
            }
        }

    } catch (err) {
        console.error('Error creating roles table or inserting default roles:', err.message || err);
        throw new Error('Failed to create roles table or insert default roles');
    }
}

    // Function to get all roles
    static async getAllRoles() {
        const query = 'SELECT * FROM roles';
        try {
            const [rows] = await pool.query(query);
            return rows;
        } catch (err) {
            console.error('Error fetching roles:', err.message);
            throw new Error('Failed to fetch roles');
        }
    }

    // Function to create a role
    static async createRole(roleName) {
        const checkQuery = 'SELECT id FROM roles WHERE name = ?';
        const insertQuery = 'INSERT INTO roles (name) VALUES (?)';

        try {
            // Check if the role already exists
            const [checkResult] = await pool.query(checkQuery, [roleName]);

            if (checkResult.length > 0) {
                return {
                    status: 'exists',
                    message: `Role "${roleName}" already exists with ID: ${checkResult[0].id}`,
                };
            }

            // Insert the new role into the database
            const [insertResult] = await pool.query(insertQuery, [roleName]);

            return {
                status: 'success',
                message: `Role "${roleName}" created successfully.`,
                role: {
                    id: insertResult.insertId,
                    name: roleName,
                },
            };
        } catch (error) {
            console.error('Error creating role:', error.message || error);
            throw new Error('Failed to create role');
        }
    }

    // Function to update a role
    static async updateRole(roleName, newRoleName) {
        const query = 'UPDATE roles SET name = ? WHERE name = ?';
        try {
            const [result] = await pool.query(query, [newRoleName, roleName]);

            if (result.affectedRows === 0) {
                return {
                    status: 'not_found',
                    message: `Role "${roleName}" not found.`,
                };
            }

            return {
                status: 'success',
                message: `Role "${roleName}" updated to "${newRoleName}" successfully.`,
            };
        } catch (err) {
            console.error('Error updating role:', err.message);
            throw new Error('Failed to update role');
        }
    }

    // Function to delete a role
    static async deleteRole(roleName) {
        const query = 'DELETE FROM roles WHERE name = ?';
        try {
            const [result] = await pool.query(query, [roleName]);

            if (result.affectedRows === 0) {
                return {
                    status: 'not_found',
                    message: `Role "${roleName}" not found.`,
                };
            }

            return {
                status: 'success',
                message: `Role "${roleName}" deleted successfully.`,
            };
        } catch (err) {
            console.error('Error deleting role:', err.message);
            throw new Error('Failed to delete role');
        }
    }

    // Function to assign a permission to a role
    static async assignPermissionToRole(roleId, permissionId) {
        const query = `
            INSERT INTO role_permissions (role_id, permission_id)
            VALUES (?, ?)
            ON DUPLICATE KEY UPDATE role_id = role_id
        `;
        try {
            await pool.query(query, [roleId, permissionId]);
            return {
                status: 'success',
                message: `Permission ID ${permissionId} assigned to Role ID ${roleId} successfully.`,
            };
        } catch (err) {
            console.error('Error assigning permission to role:', err.message);
            throw new Error('Failed to assign permission to role');
        }
    }
}

module.exports = RoleModel;
