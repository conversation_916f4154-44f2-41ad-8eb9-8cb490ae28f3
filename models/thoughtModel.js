const pool = require("../config/db");
const { replaceOrUpdateColumn } = require("../helper/replaceColumn");
const { checkTableExist } = require("../utils/dbUtils");

class Thought {
  static async createTable() {
    const query = `
			CREATE TABLE IF NOT EXISTS thoughts (
				id INT AUTO_INCREMENT PRIMARY KEY,
				concern_id INT NOT NULL,
				content TEXT NOT NULL,
				audio_url VARCHAR(255),
				duration VARCHAR(20) DEFAULT NULL,
				is_active BOOLEAN DEFAULT 1,
				created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
				updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
				FOREIGN KEY (concern_id) REFERENCES concerns(id) ON DELETE CASCADE
			);
		`;

    try {
      await checkTableExist("thoughts", query);
      await replaceOrUpdateColumn({
        tableName: "thoughts",
        newColumn: "belongs_to",
        newDefinition: "VARCHAR(255) DEFAULT NULL",
        afterColumn: "concern_id",

      })
    } catch (err) {
      console.error("Error creating thoughts table:", err.message);
      throw new Error("Failed to create thoughts table");
    }
  }

  static async getTableLength() {
    try {
      const [rows] = await pool.execute(
        "SELECT COUNT(*) as count FROM thoughts"
      );
      return rows[0].count;
    } catch (err) {
      console.error("Error getting table length:", err.message);
      throw new Error("Failed to get table length");
    }
  }

  static async create({ content, concern_id, audio_url, duration, belongs_to }) {
    try {
      // Fetch the current table length
      const length = await this.getTableLength();

      // Adjust the content if it's the first entry
      const finalContent = length === 0 ? `${content} (First Entry)` : content;

      // Insert the thought into the database
      const sql = `
				INSERT INTO thoughts (content, concern_id, audio_url, duration, belongs_to, created_at, updated_at)
				VALUES (?, ?, ?, ?, ?, NOW(), NOW())
			`;
      const [result] = await pool.execute(sql, [
        finalContent,
        concern_id,
        audio_url,
        duration,
        belongs_to
      ]);

      return result.insertId;
    } catch (error) {
      // Log detailed error information
      console.error("Database error while creating thought:", {
        message: error.message,
        stack: error.stack,
      });
      throw new Error("Failed to create thought");
    }
  }

  static async getByConcernId(concernId) {
    const sql = `
			SELECT t.id, t.content, t.created_at, t.audio_url, t.duration, t.belongs_to
			FROM thoughts t
			WHERE t.concern_id = ? AND t.is_active = 1
		`;

    try {
      const [results] = await pool.query(sql, [concernId]);
      return results;
    } catch (error) {
      console.error(
        `Error fetching thoughts for concern ID ${concernId}:`,
        error.message
      );
      throw new Error("Failed to retrieve thoughts");
    }
  }

  static async getAudioByThoughtId(id) {
    const sql = `
      SELECT audio_url, content
      FROM thoughts
      WHERE id = ? AND is_active = 1
    `;

    try {
      const [results] = await pool.query(sql, [id]);
      return results[0] || null;
    } catch (error) {
      console.error(`Error fetching audio for thought ID ${id}:`, error.message);
      throw new Error("Failed to retrieve audio");
    }
  }

  static async getByBelongsTo(belongs_to) {
    try {
      const [rows] = await pool.query(
        `SELECT v.*
       FROM videos v
       INNER JOIN thoughts t ON v.thought_id = t.id
       WHERE t.belongs_to = ?`,
        [belongs_to]
      );

      return rows;
    } catch (err) {
      console.error("Error fetching video by belongs_to:", err);
      throw err;
    }
  }

  static async getById(id) {
    const sql = `
			SELECT *
			FROM thoughts
			WHERE id = ? AND is_active = 1
		`;

    try {
      const [results] = await pool.query(sql, [id]);

      if (results.length === 0) {
        console.warn(`No thoughts found for ID ${id}`);
      }

      return results[0] || null;
    } catch (error) {
      console.error(`Error fetching thought with ID ${id}:`, error.message);
      throw new Error("Failed to retrieve thought");
    }
  }

  static async getAll() {
    try {
      const [rows] = await pool.execute(
        "SELECT * FROM thoughts WHERE is_active = 1"
      );
      return rows;
    } catch (err) {
      console.error("Error retrieving all thoughts:", err.message);
      throw new Error("Failed to retrieve thoughts");
    }
  }

  static async updateById(id, { content, audio_url, duration, belongs_to }) {
    const sqlThoughtUpdate = `
			UPDATE thoughts 
			SET content = ?, audio_url = ?, duration = ?, belongs_to = ?, updated_at = CURRENT_TIMESTAMP 
			WHERE id = ? AND is_active = 1
		`;

    try {
      const [result] = await pool.execute(sqlThoughtUpdate, [
        content,
        audio_url,
        duration,
        belongs_to,
        id,
      ]);
      if (result.affectedRows === 0) {
        throw new Error(`No thought found or updated with ID ${id}`);
      }
      return true;
    } catch (err) {
      console.error(`Error updating thought with ID ${id}:`, err.message);
      throw new Error("Failed to update thought");
    }
  }

  static async delete(id) {
    try {
      const sql =
        "UPDATE thoughts SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
      const [result] = await pool.execute(sql, [id]);
      if (result.affectedRows === 0) {
        throw new Error(`No thought found to delete with ID ${id}`);
      }
      return true;
    } catch (err) {
      console.error(`Error deleting thought with ID ${id}:`, err.message);
      throw new Error("Failed to delete thought");
    }
  }
}

module.exports = Thought;