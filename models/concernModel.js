const pool = require('../config/db');
const { checkTableExist } = require('../utils/dbUtils');

class Concern {
    static async createTable() {
        const query = `
            CREATE TABLE IF NOT EXISTS concerns (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VA<PERSON>HA<PERSON>(255) NOT NULL,
                image VARCHAR(255) NOT NULL,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            );
        `;

        try {
            await checkTableExist('concerns', query);
        } catch (err) {
            console.error('Error creating concerns table:', err.message);
            throw new Error('Failed to create concerns table');
        }
    }

    static async getTableLength() {
        try {
            const [rows] = await pool.execute('SELECT COUNT(*) as count FROM concerns');
            return rows[0].count;
        } catch (err) {
            console.error('Error getting table length:', err.message);
            throw new Error('Failed to get table length');
        }
    }
    

    static async getById(concernId) {
        try {
            // Retrieve the main concern
            const [concernRows] = await pool.execute(
                'SELECT * FROM concerns WHERE id = ?',
                [concernId]
            );

            if (concernRows.length === 0) {
                return null;
            }

            const concern = concernRows[0];

            // Fetch related thoughts
            const [thoughtRows] = await pool.execute(
                'SELECT * FROM thoughts WHERE concern_id = ?',
                [concernId]
            );

            // Map through thoughts and retrieve nested questions and answers for each thought
            for (const thought of thoughtRows) {
                const [questionRows] = await pool.execute(
                    'SELECT * FROM questions WHERE thought_id = ?',
                    [thought.id]
                );

                // Fetch answers for each question and attach them
                for (const question of questionRows) {
                    const [answerRows] = await pool.execute(
                        'SELECT * FROM answers WHERE question_id = ?',
                        [question.id]
                    );
                    question.answers = answerRows; // Attach answers to the question
                }

                thought.questions = questionRows; // Attach questions (with answers) to the thought
            }

            concern.thoughts = thoughtRows; // Attach thoughts (with nested questions and answers) to the concern

            return concern;
        } catch (err) {
            console.error(`Error retrieving concern with ID ${concernId}:`, err.message);
            throw new Error('Failed to retrieve concern with nested details');
        }
    }


    static async getAll() {
        try {
            const [rows] = await pool.execute('SELECT * FROM concerns');
            return rows;
        } catch (err) {
            console.error('Error retrieving all concerns:', err.message);
            throw new Error('Failed to retrieve concerns');
        }
    }

    static async create(name, image) {
        try {
            // Check table length
            const length = await this.getTableLength();
    
            // Increment the value if length is 0
            const finalName = length === 0 ? `${name} ${length + 1}` : name;
    
            const query = `INSERT INTO concerns (name, image) VALUES (?, ?)`;
            const [result] = await pool.execute(query, [finalName, image]);
    
            return result.insertId; // return the ID of the new row
        } catch (err) {
            console.error('Error creating concern:', err.message);
            throw new Error('Failed to create concern');
        }
    }
    


    static async updateById(id, name, image) {
        try {
            await pool.execute(
                'UPDATE concerns SET name = ?, image = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                [name, image, id]
            );
        } catch (err) {
            console.error(`Error updating concern with ID ${id}:`, err.message);
            throw new Error('Failed to update concern');
        }
    }

    static async activate(id) {
        try {
            await pool.execute('UPDATE concerns SET is_active = 1, updated_at = CURRENT_TIMESTAMP WHERE id = ?', [id]);
        } catch (err) {
            console.error(`Error activating concern with ID ${id}:`, err.message);
            throw new Error('Failed to activate concern');
        }
    }

    static async delete(id) {
        try {
            await pool.execute('UPDATE concerns SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?', [id]);
        } catch (err) {
            console.error(`Error deleting concern with ID ${id}:`, err.message);
            throw new Error('Failed to delete concern');
        }
    }
}

module.exports = Concern;
