const pool = require("../config/db");
const { checkTableExist } = require("../utils/dbUtils");

class UserBookModel {

    static async createTable() {
        const query = `CREATE TABLE IF NOT EXISTS user_books (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            book_id INT NOT NULL,
            price_paid DECIMAL(10, 2) NOT NULL,
            purchased_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREI<PERSON><PERSON> KEY (user_id) REFERENCES users(id),
            FOREIGN KEY (book_id) REFERENCES books(id)
            )`;
        try {
            await checkTableExist('user_books', query);
        } catch (err) {
            console.error('Error creating user_books table:', err.message);
            throw new Error('Failed to create user_books table');
        }
    }
    static async create(user_id, book_id, price_paid) {
        const query = `INSERT INTO user_books (user_id, book_id, price_paid) VALUES (?, ?, ?)`;
        await pool.query(query, [user_id, book_id, price_paid]);
    }

    static async hasUserPurchased(user_id, book_id) {
        const query = `SELECT * FROM user_books WHERE user_id = ? AND book_id = ?`;
        const [rows] = await pool.query(query, [user_id, book_id]);
        return rows[0] || null;
    }
}

module.exports = UserBookModel;
