const pool = require('../config/db');
const { replaceOrUpdateColumn } = require('../helper/replaceColumn');
const { checkTableExist } = require('../utils/dbUtils');

class VideosModel {
    static async createVideosTable() {
        const sql = `
            CREATE TABLE IF NOT EXISTS videos (
                id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
                text VARCHAR(255) NOT NULL,
                description TEXT DEFAULT NULL,
                image VARCHAR(255) DEFAULT NULL,
                video_id INT DEFAULT NULL,
                is_active BOOLEAN DEFAULT 1,
                type ENUM('category', 'sub_category', 'item') NOT NULL DEFAULT 'category',
                price DECIMAL(10, 2) DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                <PERSON>OR<PERSON><PERSON><PERSON> KEY (video_id) REFERENCES videos(id) ON DELETE CASCADE
            )
        `;
        try {
            await checkTableExist('videos', sql);
            await replaceOrUpdateColumn({
                tableName: 'videos',
                newColumn: 'file',
                newDefinition: 'VARCHAR(255) DEFAULT NULL',
                afterColumn: 'image',
            })
            
        } catch (err) {
            console.error('Error creating videos table:', err.message);
            throw new Error('Failed to create videos table');
        }
    }

    // ------------------ Category ------------------

    static async getAllVideos() {
        const sql = `SELECT * FROM videos WHERE type = 'category'`;
        const [rows] = await pool.query(sql);
        return rows;
    }

    static async createCategory(text, description, price, image) {
        const sql = `INSERT INTO videos (text, description, price, image, type) VALUES (?, ?, ?, ?, 'category')`;
        const [result] = await pool.query(sql, [text, description, price, image]);
        return result.insertId;
    }

    static async updateCategory(id, text, description, price, image) {
        const sql = `UPDATE videos SET text = ?, description = ?, price = ?, image = ? WHERE id = ? AND type = 'category'`;
        await pool.query(sql, [text, description, price, image, id]);
    }

    static async deleteCategory(id) {
        const sql = `DELETE FROM videos WHERE id = ? AND type = 'category'`;
        const [result] = await pool.query(sql, [id]);
        return result.affectedRows > 0;
    }

    // ------------------ Sub Category ------------------
    static async getItemsBySubCategoryId(subCategoryId) {
        const sql = `
        SELECT id, text, image, file, type, video_id
        FROM videos
        WHERE type = 'item' AND video_id = ?
    `;
        const [rows] = await pool.query(sql, [subCategoryId]);
        return rows;
    }

    static async createSubVideo(text, video_id) {
        const sql = `INSERT INTO videos (text, type, video_id) VALUES (?, 'sub_category', ?)`;
        const [result] = await pool.query(sql, [text, video_id]);
        return result.insertId;
    }

    static async updateSubVideo(id, text) {
        const sql = `UPDATE videos SET text = ? WHERE id = ? AND type = 'sub_category'`;
        await pool.query(sql, [text, id]);
    }


    static async updateSubCategory(id, text) {
        const sql = `UPDATE videos SET text = ? WHERE id = ? AND type = 'sub_category'`;
        await pool.query(sql, [text, id]);
    }

    static async getSubVideosByVideoId(video_id) {
        const sql = `
            SELECT * FROM videos
            WHERE video_id = ? AND type = 'sub_category'
        `;

        try {
            const [rows] = await pool.query(sql, [video_id]);
            return rows;
        } catch (err) {
            console.error('Error fetching sub videos by video_id:', err.message);
            throw new Error('Failed to fetch sub videos');
        }
    }

    static async getSubCategoryByText(text) {
        const sql = `SELECT * FROM videos WHERE text = ? AND type = 'sub_category'`;
        const [rows] = await pool.query(sql, [text]);
        return rows;
    }

    static async getSubVideoById(id) {
        const sql = `SELECT * FROM videos WHERE id = ? AND type = 'sub_category'`;
        const [rows] = await pool.query(sql, [id]);
        return rows.length > 0 ? rows[0] : null;
    }

    static async deleteSubCategoryById(id) {
        const sql = `DELETE FROM videos WHERE id = ?`;
        const [result] = await pool.query(sql, [id]);
        return result.affectedRows > 0;
    }


    // ------------------ Item ------------------
    static async getSubCategoriesWithItems(video_id) {
        const sql = `
        SELECT s.id AS sub_id, s.text AS sub_text, s.image AS sub_image,
               i.id AS item_id, i.text AS item_text, i.image AS item_image, i.file AS item_file
        FROM videos s
        LEFT JOIN videos i ON i.video_id = s.id AND i.type = 'item'
        WHERE s.video_id = ? AND s.type = 'sub_category'
    `;
        const [rows] = await pool.query(sql, [video_id]);
        return rows;
    }

    static async createItem({ text, image = null, file = null, type = 'item', video_id }) {
        const sql = `
            INSERT INTO videos (text, image, file, type, video_id)
            VALUES (?, ?, ?, ?, ?)
        `;
        await pool.query(sql, [text, image, file, type, video_id]);
    }

    static async updateItem(id, { text, image = null, file = null }) {
        const sql = `
            UPDATE videos 
            SET text = ?, image = ?, file = ?
            WHERE id = ? AND type = 'item'
        `;
        await pool.query(sql, [text, image, file, id]);
    }

    static async getItemById(id) {
        const [rows] = await pool.query("SELECT * FROM videos WHERE id = ? AND type = 'item'", [id]);
        return rows[0];
    }

    static async deleteItem(id) {
        const sql = `DELETE FROM videos WHERE id = ? AND type = 'item'`;
        const [result] = await pool.query(sql, [id]);
        return result.affectedRows > 0;
    }

    // ------------------ Common ------------------
    static async getVideoById(id) {
        const sql = `SELECT * FROM videos WHERE id = ?`;
        const [rows] = await pool.query(sql, [id]);
        return rows[0] || null;
    }

    static async toggleVideoStatus(id, isActive) {
        const sql = `UPDATE videos SET is_active = ? WHERE id = ?`;
        await pool.query(sql, [isActive ? 1 : 0, id]);
    }

    static async updateDescription(id, description) {
        const sql = `UPDATE videos SET description = ? WHERE id = ?`;
        await pool.query(sql, [description, id]);
    }
}

module.exports = VideosModel;