const pool = require('../config/db');
const { checkTableExist } = require('../utils/dbUtils');

class BooksModel {
    // Function to create the Books table
    static async createBooksTable() {
        const query = `
            CREATE TABLE IF NOT EXISTS books (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) UNIQUE NOT NULL,
                price DECIMAL(10, 2) NOT NULL,
                image VARCHAR(255) NOT NULL,
                image_secondary VARCHAR(255) DEFAULT NULL,
                is_exists BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            );
        `;
        try {
            await checkTableExist('books', query);
        } catch (err) {
            throw new Error('Failed to create Books table: ' + err.message);
        }
    }

    // Function to create the Pages table
    static async createPagesTable() {
        const query = `
            CREATE TABLE IF NOT EXISTS pages (
                id INT AUTO_INCREMENT PRIMARY KEY,
                book_id INT NOT NULL,
                page_image VARCHAR(255) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE
            );
        `;

        try {
            await checkTableExist("pages", query); // Ensure the 'pages' table is created
        } catch (err) {
            throw new Error("Failed to create Pages table: " + err.message);
        }
    }

    static async createDiscountOptionsTable() {
        const query = `
            CREATE TABLE IF NOT EXISTS discount_options (
                id INT AUTO_INCREMENT PRIMARY KEY,
                subscription_type ENUM('none', 'basic', 'advanced', 'advanced_plus') NOT NULL UNIQUE,
                discount_percentage INT NOT NULL CHECK (discount_percentage BETWEEN 0 AND 100),
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            );
        `;

        try {
            await checkTableExist('discount_options', query);
        } catch (err) {
            throw new Error('Failed to create Discount Options table: ' + err.message);
        }
    }




    static async getTableLength() {
        try {
            const [rows] = await pool.query('SELECT COUNT(*) as count FROM books');
            return rows[0].count;
        } catch (err) {
            console.error('Error getting table length:', err.message);
            throw new Error('Failed to get table length');
        }
    }

    // Function to add a new book
    static async addBook({ name, price, image, image_secondary }) {
        try {
            // Get the table length
            const length = await this.getTableLength();

            // Modify the book name if the table is empty
            const finalName = length === 0 ? `${name} (First Book)` : name;

            const query = `
                INSERT INTO books (name, price, image, image_secondary)
                VALUES (?, ?, ?, ?);
            `;
            const [result] = await pool.query(query, [finalName, price, image, image_secondary]);
            return { id: result.insertId, name: finalName, price, image };
        } catch (err) {
            throw new Error('Failed to add book: ' + err.message);
        }
    }


    // Function to get all books with total pages
    static async getAllBooks() {
        const bookQuery = `
            SELECT 
                b.id,
                b.name,
                b.price AS original_price,
                b.image,
                b.image_secondary,
                b.is_exists,
                b.created_at,
                b.updated_at,
                COUNT(p.id) AS totalPages
            FROM books b
            LEFT JOIN pages p ON b.id = p.book_id
            WHERE b.is_exists = 1
            GROUP BY b.id
        `;

        const pagesQuery = `SELECT book_id, page_image FROM pages ORDER BY book_id`;

        try {
            // Fetch books and discount options
            const [books] = await pool.query(bookQuery);
            const [pages] = await pool.query(pagesQuery);
            const discountOptions = await BooksModel.getDiscountOptions();

            // Map pages to books and apply dynamic discounts
            const booksWithPages = books.map(book => ({
                ...book,
                totalPages: book.totalPages || 0,
                discount_options: discountOptions.map(option => ({
                    subscription_type: option.subscription_type,
                    discount_percentage: option.discount_percentage,
                    discounted_price: parseFloat((book.original_price * (1 - option.discount_percentage / 100)).toFixed(2))
                })),
                pages: pages.filter(page => page.book_id === book.id).map(page => ({
                    book_id: page.book_id,
                    page_image: page.page_image
                }))
            }));

            return { data: booksWithPages };
        } catch (err) {
            throw new Error('Failed to fetch books with pages: ' + err.message);
        }
    }


    // Function to get all page images by book ID
    static async getPagesByBookId(bookId) {
        const query = `
            SELECT * FROM pages WHERE book_id = ?;
        `;
        try {
            const [rows] = await pool.query(query, [bookId]); // Execute query
            return rows; // Return rows from database
        } catch (err) {
            throw new Error("Failed to fetch pages by book ID: " + err.message);
        }
    }

    // Function to get a page by ID
    static async getPageById(id) {
        const query = `
            SELECT 
                id, page_image
            FROM pages
            WHERE id = ?;
        `;
        try {
            const [rows] = await pool.query(query, [id]);
            return rows.length > 0 ? rows[0] : null; // Return the first result or null
        } catch (err) {
            throw new Error("Failed to fetch page by ID: " + err.message);
        }
    }


    // Function to add pages to the book
    static async addPages(bookId, pages) {
        const query = `
        INSERT INTO pages (book_id, page_image) VALUES ?
    `;
        const values = pages.map((page) => [bookId, page.page_image]); // No order_index
        try {
            await pool.query(query, [values]);
        } catch (err) {
            throw new Error("Failed to add pages: " + err.message);
        }
    }


    // Function to get a book by ID with its images
    static async getBookById(bookId) {
        const query = `
            SELECT 
                books.id,
                books.name,
                books.price,
                books.image,
                books.image_secondary,
                books.is_exists,
                books.created_at,
                books.updated_at,
                pages.page_image,
                pages.created_at AS dateAdded
            FROM books
            LEFT JOIN pages ON books.id = pages.book_id
            WHERE books.id = ? AND books.is_exists = 1
        `;

        try {
            const [rows] = await pool.query(query, [bookId]);

            if (rows.length === 0) {
                return null;
            }

            // ✅ Extract book from first row
            const book = {
                id: rows[0].id,
                name: rows[0].name,
                price: rows[0].price,
                image: rows[0].image,
                image_secondary: rows[0].image_secondary,
                is_exists: rows[0].is_exists,
                created_at: rows[0].created_at,
                updated_at: rows[0].updated_at
            };

            // ✅ Collect pages
            const pages = rows
                .filter(row => row.page_image !== null)
                .map(row => ({
                    book_id: row.id,
                    page_image: row.page_image,
                    dateAdded: row.dateAdded
                }));

            const price_discount = await BooksModel.getDiscountOptions();

            return {
                ...book,
                discount_price: price_discount.map((option) => ({
                    subscription_type: option.subscription_type,
                    discount_percentage: option.discount_percentage,
                    discounted_price: parseFloat((book.price * (1 - option.discount_percentage / 100)).toFixed(2))
                })),
                pages
            };
        } catch (err) {
            console.error('Error fetching book by ID:', err.message);
            throw new Error('Failed to fetch book by ID');
        }
    }



    static async updateBookWithImage(id, updatedData, image, imageSecondary) {
        try {
            // Fetch the existing book details to ensure we retain the old image if no new image is uploaded
            const [existingBook] = await pool.query(`SELECT image, image_secondary FROM books WHERE id = ?`, [id]);

            if (!existingBook.length) {
                throw new Error(`Book with ID ${id} not found`);
            }

            // Ensure that if no new image is uploaded, the old image remains
            const newImage = image || existingBook[0].image;
            const newImageSecondary = imageSecondary || existingBook[0].image_secondary;

            const sql = `
                UPDATE books
                SET name = ?, price = ?, image = ?, image_secondary = ?, updated_at = NOW()
                WHERE id = ?
            `;

            const [result] = await pool.query(sql, [
                updatedData.name,
                updatedData.price,
                newImage,
                newImageSecondary,
                id
            ]);

            return result;
        } catch (error) {
            console.error(`Error updating book with ID ${id}:`, error.message);
            throw new Error("Failed to update book");
        }
    }

    static async updatePageById(id, { page_image }) {
        const query = `
            UPDATE pages 
            SET page_image = ?
            WHERE id = ?;
        `;
        try {
            const [result] = await pool.query(query, [page_image, id]);
            return result;
        } catch (err) {
            console.error("Error updating page in database:", err.message);
            throw new Error("Database update failed.");
        }
    }

    // Function to update the status of a book by ID
    static async updateBookStatus(bookId, isExists) {
        const booleanStatus = isExists ? 1 : 0; // MySQL uses 1/0 for boolean
        const query = `
        UPDATE books
        SET is_exists = ?
        WHERE id = ?
    `;

        try {
            const [result] = await pool.query(query, [booleanStatus, bookId]);

            if (result.affectedRows === 0) {
                throw new Error(`No book found with ID ${bookId}`);
            }

            return result;
        } catch (err) {
            console.error(`DB Error [updateBookStatus]: ${err.message}`);
            throw new Error('Failed to update book status: ' + err.message);
        }
    }

    static async deletePageById(id) {
        const query = `DELETE FROM pages WHERE id = ?`;
        try {
            await pool.query(query, [id]);
        } catch (err) {
            throw new Error('Failed to delete page');
        }
    }

    // Discount options functions

    static async addDiscountOption(subscriptionType, discountPercentage) {
        if (discountPercentage < 0 || discountPercentage > 100) {
            throw new Error("Discount percentage must be between 0 and 100");
        }

        const query = `
            INSERT INTO discount_options (subscription_type, discount_percentage) 
            VALUES (?, ?)
        `;

        try {
            const [result] = await pool.query(query, [subscriptionType, discountPercentage]);
            return result.insertId;
        } catch (err) {
            throw new Error("Failed to insert discount option: " + err.message);
        }
    }



    static async getDiscountOptions() {
        const query = `SELECT * FROM discount_options`;

        try {
            const [discounts] = await pool.query(query);
            return discounts;
        } catch (err) {
            throw new Error('Failed to fetch discount options: ' + err.message);
        }
    }

    static async updateDiscountOption(subscriptionType, discountPercentage) {
        const query = `
            UPDATE discount_options 
            SET discount_percentage = ?
            WHERE subscription_type = ?
        `;

        try {
            const [result] = await pool.query(query, [discountPercentage, subscriptionType]);
            if (result.affectedRows === 0) {
                throw new Error(`No discount option found for '${subscriptionType}'`);
            }
            return { message: `Discount for '${subscriptionType}' updated successfully` };
        } catch (err) {
            throw new Error('Failed to update discount option: ' + err.message);
        }
    }

    static async deleteDiscountOption(subscriptionType) {
        const query = `DELETE FROM discount_options WHERE subscription_type = ?`;

        try {
            const [result] = await pool.query(query, [subscriptionType]);
            if (result.affectedRows === 0) {
                throw new Error(`No discount option found for '${subscriptionType}'`);
            }
            return { message: `Discount for '${subscriptionType}' deleted successfully` };
        } catch (err) {
            throw new Error('Failed to delete discount option: ' + err.message);
        }
    }


}

module.exports = BooksModel;
