const pool = require('../config/db');
const { checkTableExist } = require('../utils/dbUtils');
const { replaceOrUpdateColumn } = require('../helper/replaceColumn');

class MeditationModel {
    static async createMeditationTable() {
        const sql = `
      CREATE TABLE IF NOT EXISTS meditation (
        id INT PRIMARY KEY AUTO_INCREMENT,
        title VARCHAR(255) NOT NULL,
        description VARCHAR(255) DEFAULT NULL,
        image VARCHAR(255) DEFAULT NULL,
        secondry_image VARCHAR(255) DEFAULT NULL,
        is_active BOOLEAN DEFAULT 1,
        meditation_id INT DEFAULT NULL,
        audio_url VARCHAR(255) DEFAULT NULL,
        type ENUM('category', 'sub_category') NOT NULL DEFAULT 'category',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (meditation_id) REFERENCES meditation(id) ON DELETE CASCADE
      )
    `;
        try {
            await checkTableExist('meditation', sql);
            // Ensure column exists even on older tables:
            await replaceOrUpdateColumn({
                tableName: 'meditation',
                newColumn: 'secondry_image',
                newDefinition: 'VARCHAR(255) DEFAULT NULL',
                afterColumn: 'image',
            });
        } catch (err) {
            console.error('Error creating meditation table:', err.message);
            throw new Error('Failed to create meditation table');
        }
    }

    // RETURN insertId so your controller can include it in the response
    static async createMeditation({ title, description, image, secondry_image, type = 'category' }) {
        const sql = `
      INSERT INTO meditation (title, description, image, secondry_image, type, is_active, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, 1, NOW(), NOW())
    `;
        try {
            const [res] = await pool.query(sql, [title, description, image, secondry_image, type]);
            return res.insertId; // <-- important
        } catch (err) {
            console.error('Error creating meditation:', err.message);
            throw new Error('Failed to create meditation');
        }
    }

    static async createSubMeditation({ title, audio_url = null, meditation_id, description = null }) {
        const sql = `
      INSERT INTO meditation (title, audio_url, meditation_id, description, type, created_at, updated_at)
      VALUES (?, ?, ?, ?, 'sub_category', NOW(), NOW())
    `;
        try {
            const [result] = await pool.query(sql, [title, audio_url, meditation_id, description]);
            return result.insertId;
        } catch (error) {
            console.error('Error creating sub-meditation:', error.message);
            throw new Error('Failed to create sub-meditation');
        }
    }

    static async getAllMeditation() {
        try {
            const sql = `
        SELECT 
          category.id AS category_id,
          category.title AS category_title,
          category.description AS category_description,
          category.image AS category_image,
          category.secondry_image AS category_secondry_image,
          category.is_active AS category_is_active,
          category.meditation_id AS category_meditation_id,
          category.type AS category_type,
          category.created_at AS category_created_at,
          category.updated_at AS category_updated_at,

          sub.id AS sub_id,
          sub.title AS sub_title,
          sub.description AS sub_description,
          sub.audio_url AS sub_audio_url,
          sub.is_active AS sub_is_active,
          sub.created_at AS sub_created_at,
          sub.updated_at AS sub_updated_at
        FROM meditation category
        LEFT JOIN meditation sub ON category.id = sub.meditation_id AND sub.type = 'sub_category'
        WHERE category.type = 'category'
        ORDER BY category.id, sub.id
      `;
            const [rows] = await pool.query(sql);

            const grouped = rows.reduce((acc, row) => {
                let category = acc.find(c => c.id === row.category_id);

                const sub = row.sub_id
                    ? {
                        id: row.sub_id,
                        title: row.sub_title,
                        description: row.sub_description,
                        // image: row.sub_image, // ❌ not selected; remove to avoid undefined
                        audio_url: row.sub_audio_url,
                        is_active: row.sub_is_active,
                        created_at: row.sub_created_at,
                        updated_at: row.sub_updated_at,
                    }
                    : null;

                if (!category) {
                    category = {
                        id: row.category_id,
                        title: row.category_title,
                        description: row.category_description,
                        image: row.category_image,
                        secondry_image: row.category_secondry_image,
                        is_active: row.category_is_active,
                        meditation_id: row.category_meditation_id,
                        type: row.category_type,
                        created_at: row.category_created_at,
                        updated_at: row.category_updated_at,
                        sub_categories: sub ? [sub] : [],
                    };
                    acc.push(category);
                } else if (sub) {
                    category.sub_categories.push(sub);
                }

                return acc;
            }, []);

            return grouped;
        } catch (error) {
            console.error("Error fetching meditations with sub-categories:", error.message);
            throw new Error("Failed to fetch meditation categories and sub-categories");
        }
    }

    static async getAllSubMeditation(meditationId) {
        try {
            const sql = `
        SELECT 
          id,
          title,
          type,
          audio_url
        FROM meditation
        WHERE meditation_id = ? AND type = 'sub_category'
      `;
            const [rows] = await pool.query(sql, [meditationId]);
            if (!rows || rows.length === 0) return [];
            return rows.map(sub => ({
                sub_id: sub.id,
                title: sub.title,
                type: sub.type,
                audio_url: sub.audio_url,
            }));
        } catch (error) {
            console.error(`Error fetching sub-meditations for meditation ID (${meditationId}):`, error.message);
            throw new Error("Failed to fetch sub-meditations. Please try again later.");
        }
    }

    static async getMeditationById(id) {
        try {
            const sql = `
        SELECT 
          category.id AS category_id,
          category.title AS category_title,
          category.description AS category_description,
          category.image AS category_image,
          category.secondry_image AS category_secondry_image,
          category.is_active AS category_is_active,
          category.type AS category_type,
          category.created_at AS category_created_at,
          category.updated_at AS category_updated_at,
          
          sub.id AS sub_id,
          sub.title AS sub_title,
          sub.description AS sub_description,
          sub.audio_url AS sub_audio_url,
          sub.created_at AS sub_created_at,
          sub.updated_at AS sub_updated_at
        FROM meditation category
        LEFT JOIN meditation sub 
          ON category.id = sub.meditation_id AND sub.type = 'sub_category'
        WHERE category.id = ? AND category.type = 'category'
      `;
            const [rows] = await pool.query(sql, [id]);
            if (rows.length === 0) return null;

            const meditation = {
                id: rows[0].category_id,
                title: rows[0].category_title,
                description: rows[0].category_description,
                image: rows[0].category_image,
                secondry_image: rows[0].category_secondry_image,
                is_active: rows[0].category_is_active,
                type: rows[0].category_type,
                created_at: rows[0].category_created_at,
                updated_at: rows[0].category_updated_at,
                sub_categories: [],
            };

            for (const row of rows) {
                if (row.sub_id) {
                    meditation.sub_categories.push({
                        id: row.sub_id,
                        title: row.sub_title,
                        description: row.sub_description,
                        audio_url: row.sub_audio_url,
                        created_at: row.sub_created_at,
                        updated_at: row.sub_updated_at,
                    });
                }
            }

            // Optionally bubble first audio_url to the parent:
            const firstAudio = meditation.sub_categories.find(sc => sc.audio_url);
            meditation.audio_url = firstAudio?.audio_url || null;

            return meditation;
        } catch (error) {
            console.error(`Error fetching meditation with ID (${id}):`, error.message);
            throw new Error(`Failed to fetch meditation with ID (${id})`);
        }
    }

    static async getSubMeditationById(id) {
        try {
            const sql = `
        SELECT 
          id,
          title,
          description,
          audio_url,
          is_active,
          meditation_id,
          type,
          created_at,
          updated_at
        FROM meditation 
        WHERE id = ? AND type = 'sub_category'
      `;
            const [rows] = await pool.query(sql, [id]);
            if (rows.length === 0) return null;
            return rows[0];
        } catch (error) {
            console.error(`Error fetching sub-meditation with ID (${id}):`, error.message);
            throw new Error(`Failed to fetch sub-meditation with ID (${id})`);
        }
    }

    static async updateMeditation(id, { title, image, secondry_image, description }) {
        try {
            const sql = `
        UPDATE meditation
        SET title = ?, image = ?, secondry_image = ?, description = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ? AND type = 'category'
      `;
            const [result] = await pool.query(sql, [title, image, secondry_image, description, id]);
            return { message: "Meditation updated successfully", affectedRows: result.affectedRows };
        } catch (error) {
            console.error(`Error updating meditation with ID (${id}):`, error.message);
            throw new Error("Failed to update meditation");
        }
    }

    static async updateSubMeditation(id, { title, audio_url }) {
        try {
            const sql = `
        UPDATE meditation
        SET title = ?, audio_url = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ? AND type = 'sub_category'
      `;
            const [result] = await pool.query(sql, [title, audio_url, id]);
            return { message: "Sub-meditation updated successfully", affectedRows: result.affectedRows };
        } catch (error) {
            console.error(`Error updating sub-meditation with ID (${id}):`, error.message);
            throw new Error("Failed to update sub-meditation");
        }
    }

    static async deleteMeditation(id) {
        try {
            const sql = `DELETE FROM meditation WHERE id = ? AND type = 'category'`;
            const [result] = await pool.query(sql, [id]);
            return { message: "Meditation deleted successfully", affectedRows: result.affectedRows };
        } catch (error) {
            console.error(`Error deleting meditation with ID (${id}):`, error.message);
            throw new Error("Failed to delete meditation");
        }
    }

    static async deleteSubMeditation(id) {
        try {
            const sql = `DELETE FROM meditation WHERE id = ? AND type = 'sub_category'`;
            const [result] = await pool.query(sql, [id]);
            return { message: "Sub-meditation deleted successfully", affectedRows: result.affectedRows };
        } catch (error) {
            console.error(`Error deleting sub-meditation with ID (${id}):`, error.message);
            throw new Error("Failed to delete sub-meditation");
        }
    }
}

module.exports = MeditationModel;