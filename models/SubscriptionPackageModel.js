const pool = require("../config/db");
const { checkTableExist } = require("../utils/dbUtils");

class SubscriptionPackageModel {
    static async createTable() {
        const query = `
            CREATE TABLE IF NOT EXISTS subscription_packages (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                monthly_price DECIMAL(10,2) NOT NULL,
                yearly_price DECIMAL(10,2) NOT NULL,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            );
        `;

        try {
            await checkTableExist('subscription_packages', query);
        } catch (err) {
            console.error('Error creating subscription_packages table:', err.message);
            throw new Error('Failed to create subscription_packages table');
        }
    }

    // جلب كل الباقات سواء مفعلة أو لا
    static async getAllPackages() {
        const [rows] = await pool.query(`SELECT * FROM subscription_packages`);
        return rows;
    }

    // جلب فقط الباقات المفعلة
    static async getAllActivePackages() {
        const [rows] = await pool.query("SELECT * FROM subscription_packages WHERE is_active = 1");
        return rows;
    }

    static async getById(id) {
        const [rows] = await pool.query("SELECT * FROM subscription_packages WHERE id = ?", [id]);
        return rows[0];
    }

    static async createPackage({ name, monthly_price, yearly_price, is_active }) {
        const query = `
            INSERT INTO subscription_packages (name, monthly_price, yearly_price, is_active)
            VALUES (?, ?, ?, ?)
        `;
        const [result] = await pool.query(query, [name, monthly_price, yearly_price, is_active]);
        return result;
    }

    static async updatePackage(id, { name, monthly_price, yearly_price }) {
        const query = `
            UPDATE subscription_packages
            SET name = ?, monthly_price = ?, yearly_price = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        `;
        const [result] = await pool.query(query, [name, monthly_price, yearly_price, id]);
        return result;
    }
    

    static async toggleStatus(id, isActive) {
        const query = `
            UPDATE subscription_packages
            SET is_active = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        `;
        const [result] = await pool.query(query, [isActive ? 1 : 0, id]);
        return result;
    }
    
}

module.exports = SubscriptionPackageModel;
