const pool = require('../config/db');
const { checkTableExist } = require('../utils/dbUtils');

class PermissionModel {
    // Create the permissions table if it doesn't exist
    static async createPermissionTable() {
        const query = `
            CREATE TABLE IF NOT EXISTS permissions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) UNIQUE NOT NULL,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            );
        `;
        try {
            checkTableExist('permissions', query);

        } catch (err) {
            throw new Error('Failed to create permissions table');
        }
    }

    // Create a new permission in the permissions table
    static async createPermission(permissionName, description = '') {
        const query = `
            INSERT INTO permissions (name, description)
            VALUES (?, ?)
        `;
        try {
            await pool.query(query, [permissionName, description]);
            return { status: 'success', permissionName };
        } catch (err) {
            if (err.code === 'ER_DUP_ENTRY') {
                return { status: 'error', message: `Permission '${permissionName}' already exists.` };
            }
            throw new Error('Failed to create permission');
        }
    }

    // Fetch all permissions from the permissions table
    static async getAllPermissions() {
        const query = 'SELECT * FROM permissions';
        try {
            const [rows] = await pool.query(query);
            return rows;
        } catch (err) {
            console.error('Error fetching permissions:', err.message);
            throw new Error('Failed to fetch permissions');
        }
    }

    // Fetch a single permission by its ID
    static async getPermissionById(id) {
        const query = 'SELECT * FROM permissions WHERE id = ?';
        try {
            const [rows] = await pool.query(query, [id]);
            if (rows.length === 0) {
                return null; // No permission found with the given ID
            }
            return rows[0];
        } catch (err) {
            console.error(`Error fetching permission with ID ${id}:`, err.message);
            throw new Error('Failed to fetch permission by ID');
        }
    }

    // Update a permission by its ID
    static async updatePermission(id, newDetails) {
        const query = `
            UPDATE permissions
            SET name = ?, description = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        `;
        try {
            const [result] = await pool.query(query, [newDetails.name, newDetails.description, id]);
            if (result.affectedRows === 0) {
                return { status: 'error', message: 'No permission found with the given ID' };
            }
            return { status: 'success', message: `Permission with ID ${id} updated successfully.` };
        } catch (err) {
            if (err.code === 'ER_DUP_ENTRY') {
                return { status: 'error', message: `Permission '${newDetails.name}' already exists.` };
            }
            console.error(`Error updating permission with ID ${id}:`, err.message);
            throw new Error('Failed to update permission');
        }
    }

    // Delete a permission by its ID
    static async deletePermission(id) {
        const query = 'DELETE FROM permissions WHERE id = ?';
        try {
            const [result] = await pool.query(query, [id]);
            if (result.affectedRows === 0) {
                return { status: 'error', message: 'No permission found with the given ID' };
            }
            return { status: 'success', message: `Permission with ID ${id} deleted successfully.` };
        } catch (err) {
            console.error(`Error deleting permission with ID ${id}:`, err.message);
            throw new Error('Failed to delete permission');
        }
    }
}

module.exports = PermissionModel;
