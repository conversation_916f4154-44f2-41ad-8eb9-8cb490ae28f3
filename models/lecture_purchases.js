const pool = require("../config/db");
const { checkTableExist } = require("../utils/dbUtils");

class LecturePurchasesModel {
  static async createTable() {
    const tableName = "lecture_purchases";
    const query = `
      CREATE TABLE IF NOT EXISTS ${tableName} (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        lecture_id INT NOT NULL,
        price_paid DECIMAL(10,2) NOT NULL,
        purchased_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id),
        FOREIGN KEY (lecture_id) REFERENCES lectures(id)
      );
    `;
    try {
      await checkTableExist(tableName, query);
    } catch (err) {
      console.error(`Error creating ${tableName} table:`, err.message);
      throw new Error(`Failed to create ${tableName} table`);
    }
  }

  static async createPurchase({ user_id, lecture_id, price_paid }) {
    try {
      const [result] = await pool.query(
        `INSERT INTO lecture_purchases (user_id, lecture_id, price_paid)
         VALUES (?, ?, ?)`,
        [user_id, lecture_id, price_paid]
      );
      return result.insertId;
    } catch (err) {
      console.error("Error in createPurchase:", err.message);
      throw new Error("Failed to create lecture purchase");
    }
  }

  static async getPurchasesByUser(user_id) {
    try {
      const [rows] = await pool.query(
        `SELECT lp.*, l.title, l.price
         FROM lecture_purchases lp
         JOIN lectures l ON lp.lecture_id = l.id
         WHERE lp.user_id = ?`,
        [user_id]
      );
      return rows;
    } catch (err) {
      console.error("Error in getPurchasesByUser:", err.message);
      throw new Error("Failed to get lecture purchases");
    }
  }
}

module.exports = LecturePurchasesModel;