const pool = require("../config/db");
const { checkTableExist } = require("../utils/dbUtils");
const { replaceOrUpdateColumn } = require("../helper/replaceColumn");

class FeatureModel {
  static async createTable() {
    const query = `
      CREATE TABLE IF NOT EXISTS feature (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        is_active BOOLEAN DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `;

    try {
      await checkTableExist('feature', query);

      await replaceOrUpdateColumn({
        tableName: 'feature',
        oldColumn: null,
        newColumn: 'discount_percentage',
        newDefinition: 'VARCHAR(255) DEFAULT NULL',
        afterColumn: 'name',
      });

      await replaceOrUpdateColumn({
        tableName: 'feature',
        oldColumn: null,
        newColumn: 'type',
        newDefinition: 'VARCHAR(255) DEFAULT NULL',
        afterColumn: 'discount_percentage',
      });

    } catch (err) {
      console.error('Error creating feature table:', err.message);
      throw new Error('Failed to create feature table');
    }
  }

  static async getAll() {
    const [rows] = await pool.query(`SELECT * FROM feature`);
    return rows;
  }

  static async create(name, discount_percentage = null, type = null) {
    const [result] = await pool.query(
      `INSERT INTO feature (name, discount_percentage, type) VALUES (?, ?, ?)`,
      [name, discount_percentage, type]
    );
    return result;
  }

  static async update(id, name, discount_percentage = null, type = null) {
    const [result] = await pool.query(
      `UPDATE feature SET name = ?, discount_percentage = ?, type = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?`,
      [name, discount_percentage, type, id]
    );
    return result;
  }

  static async toggleStatus(id, isActive) {
    const [result] = await pool.query(
      `UPDATE feature SET is_active = ? WHERE id = ?`,
      [isActive ? 1 : 0, id]
    );
    return result;
  }

  static async delete(id) {
    const [result] = await pool.query(
      `DELETE FROM feature WHERE id = ?`,
      [id]
    );
    return result;
  }
}

module.exports = FeatureModel;