const pool = require('../config/db');
const { checkTableExist } = require('../utils/dbUtils');

class Question {
    // Create the questions table
    static async createTable() {
        const createTableQuery = `
            CREATE TABLE IF NOT EXISTS questions (
            id INT(11) NOT NULL PRIMARY KEY AUTO_INCREMENT,
            thought_id INT(11) NOT NULL,
            question_text VARCHAR(255) NOT NULL,
            is_question TINYINT(1) DEFAULT '1',
            is_show TINYINT(1) DEFAULT '1',
            yes_route_id INT(11) DEFAULT NULL,
            no_route_id INT(11) DEFAULT NULL,
            answer_text VARCHAR(255) DEFAULT NULL,
            summary VARCHAR(255) DEFAULT NULL,
            button_yes_text VARCHAR(255) DEFAULT NULL,
            button_no_text VARCHAR(255) DEFAULT NULL,
            created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            question_type ENUM(
                'question', 'introduction', 'sub_question', 'conclusion', 
                'sentence', 'repeating_sentence', 'general_sentence', 'summary'
            ) DEFAULT 'question',

            -- ✅ Corrected: Added commas between constraints
            FOREIGN KEY (thought_id) REFERENCES thoughts(id) ON DELETE CASCADE,
            FOREIGN KEY (yes_route_id) REFERENCES questions(id) ON DELETE SET NULL,
            FOREIGN KEY (no_route_id) REFERENCES questions(id) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
        `;

        try {
            await checkTableExist('questions', createTableQuery);
        } catch (err) {
            console.error('Error creating questions table:', err.message);
            throw new Error('Failed to create or update questions table.');
        }
    }

    // Create multiple questions for a given thought
    static async createQuestions(thoughtId, questions) {
        const validQuestionTypes = [
          'question',
          'introduction',
          'sub_question',
          'conclusion',
          'sentence',
          'repeating_sentence',
          'sentence_general',
          'summary',
        ];
      
        const query = `
          INSERT INTO questions (
            thought_id,
            question_text,
            question_type,
            answer_text,
            summary,
            button_yes_text,
            button_no_text,
            is_question
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `;
      
        try {
          if (!thoughtId) {
            throw new Error('A valid thoughtId is required.');
          }
      
          for (const question of questions) {
            if (!question.questionText || !validQuestionTypes.includes(question.questionType)) {
              throw new Error(`Invalid questionType: ${question.questionType}`);
            }
      
            await pool.execute(query, [
              thoughtId,
              question.questionText.trim(),
              question.questionType,
              question.answerText || null,
              question.summary || null, // ✅ fixed this line (was summaryText)
              question.buttonYesText || null,
              question.buttonNoText || null,
              question.isQuestion ? 1 : 0,
            ]);
          }
      
          return { data: questions, success: true, message: 'Questions created successfully.' };
        } catch (error) {
          console.error('Error creating questions:', error.message);
          throw new Error('Failed to create questions.');
        }
      }
      
      

    static async getQuestionsByThoughtId(thoughtId) {
        const query = `
            SELECT 
                q.id, 
                q.question_text, 
                q.question_type, 
                q.yes_route_id, 
                q.no_route_id, 
                q.answer_text,  
                q.summary,
                q.button_yes_text, 
                q.button_no_text,
                q.is_question,
                c.name AS concern_name
            FROM questions q
            LEFT JOIN thoughts t ON q.thought_id = t.id
            LEFT JOIN concerns c ON t.concern_id = c.id
            WHERE q.thought_id = ?
        `;
    
        try {
            const [rows] = await pool.execute(query, [thoughtId]);
            return rows;
        } catch (error) {
            console.error(`[❌ DB Error] Failed to fetch questions for thought ID ${thoughtId}:`, error.message);
            throw new Error('Failed to retrieve questions.');
        }
    }
    

    // Update a question by its ID
    static async updateById(
        id,
        questionText,
        questionType,
        isQuestion,
        yesRouteId,
        noRouteId,
        answerText,
        summaryText,
        buttonYesText,
        buttonNoText
    ) {
        if (!id) throw new Error('Missing ID in updateById');

        const query = `
          UPDATE questions
          SET 
            question_text = ?, 
            question_type = ?, 
            is_question = ?, 
            yes_route_id = ?, 
            no_route_id = ?, 
            answer_text = ?, 
            summary = ?,
            button_yes_text = ?, 
            button_no_text = ?,
            updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `;

        try {
            const [result] = await pool.execute(query, [
                questionText?.trim() || null,
                questionType?.trim() || null,
                isQuestion ? 1 : 0,
                yesRouteId ?? null,
                noRouteId ?? null,
                answerText ?? null,
                summaryText ?? null,
                buttonYesText ?? null,
                buttonNoText ?? null,
                id,
            ]);


            if (result.affectedRows === 0) {
                return { success: false, message: `No question found with ID ${id}` };
            }

            return { success: true, data: result, message: `Question with ID ${id} updated` };
        } catch (error) {
            console.error('Error in updateById:', error.message);
            throw new Error(`Failed to update question with ID ${id}: ${error.message}`);
        }
    }



    // Get the first question for a specific thought (used to answer the first question)
    static async getTheFirstQuestion() {
        const query = `
            SELECT q.id, q.question_text, q.question_type, q.yes_route_id, q.no_route_id, q.answer_text, q.summary, q.is_question
            FROM questions q
            WHERE q.thought_id = ? AND q.is_show = 1
            LIMIT 1
        `;

        try {
            const [rows] = await pool.execute(query, [1]);
            return rows[0];
        } catch (error) {
            console.error('Error retrieving the first question:', error.message);
            throw new Error('Failed to retrieve the first question.');
        }
    }

    // Delete a question by its ID
    static async delete(id) {
        if (!id) {
            throw new Error('Invalid question ID.');
        }

        const query = 'DELETE FROM questions WHERE id = ?';

        try {
            const [result] = await pool.execute(query, [id]);
            if (result.affectedRows === 0) {
                throw new Error(`No question found with ID ${id}.`);
            }
            return { success: true, message: `Question with ID ${id} deleted successfully.` };
        } catch (err) {
            console.error(`Error deleting question with ID ${id}:`, err.message);
            throw new Error('Failed to delete question.');
        }
    }
}

module.exports = Question;
