const pool = require('../config/db');
const { checkTableExist } = require('../utils/dbUtils');

class AudioClip {
    static async createTable() {
        const query = `
            CREATE TABLE IF NOT EXISTS audio_clips (
                id INT AUTO_INCREMENT PRIMARY KEY,
                thought_id INT,
                audio_url VARCHAR(255) NOT NULL,
                duration INT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (thought_id) REFERENCES thoughts(id) ON DELETE CASCADE
            );
        `;

        try {
            await checkTableExist('audio_clips', query);
        } catch (err) {
            console.error('Error creating audio_clips table:', err.message);
            throw new Error('Failed to create audio_clips table');
        }
    }

    static async getTableLength() {
        try {
            const [rows] = await pool.execute('SELECT COUNT(*) as count FROM audio_clips');
            return rows[0].count;
        } catch (err) {
            console.error('Error getting table length:', err.message);
            throw new Error('Failed to get table length');
        }
    }
    

    static async getById(id) {
        try {
            const [rows] = await pool.execute('SELECT * FROM audio_clips WHERE id = ?', [id]);
            return rows[0];
        } catch (err) {
            console.error(`Error retrieving audio clip with ID ${id}:`, err.message);
            throw new Error('Failed to retrieve audio clip');
        }
    }

    static async getAll() {
        try {
            const [rows] = await pool.execute('SELECT * FROM audio_clips');
            return rows;
        } catch (err) {
            console.error('Error retrieving all audio clips:', err.message);
            throw new Error('Failed to retrieve audio clips');
        }
    }

    static async create(thoughtId, audioUrl, duration) {
        try {
            // Get the table length
            const length = await this.getTableLength();
    
            // Example logic: Modify the duration if table length is 0
            const finalDuration = length === 0 ? duration + 1 : duration;
    
            const [result] = await pool.execute(
                'INSERT INTO audio_clips (thought_id, audio_url, duration) VALUES (?, ?, ?)',
                [thoughtId, audioUrl, finalDuration]
            );
            return result.insertId;
        } catch (err) {
            console.error('Error creating audio clip:', err.message);
            throw new Error('Failed to create audio clip');
        }
    }
    

    static async updateById(id, audioUrl, duration) {
        try {
            await pool.execute(
                'UPDATE audio_clips SET audio_url = ?, duration = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                [audioUrl, duration, id]
            );
        } catch (err) {
            console.error(`Error updating audio clip with ID ${id}:`, err.message);
            throw new Error('Failed to update audio clip');
        }
    }

    static async delete(id) {
        try {
            await pool.execute('DELETE FROM audio_clips WHERE id = ?', [id]);
        } catch (err) {
            console.error(`Error deleting audio clip with ID ${id}:`, err.message);
            throw new Error('Failed to delete audio clip');
        }
    }
}

module.exports = AudioClip;
