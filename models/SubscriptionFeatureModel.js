const pool = require("../config/db");
const { checkTableExist } = require("../utils/dbUtils");

class SubscriptionFeatureModel {
    static async createTable() {
        const query = `
            CREATE TABLE IF NOT EXISTS subscription_features (
                id INT AUTO_INCREMENT PRIMARY KEY,
                package_id INT NOT NULL,
                feature_id INT NOT NULL,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (package_id) REFERENCES subscription_packages(id) ON DELETE CASCADE,
                FOREIGN KEY (feature_id) REFERENCES feature(id) ON DELETE CASCADE
            );
        `;
        try {
            await checkTableExist('subscription_features', query);
        } catch (err) {
            console.error('Error creating subscription_features table:', err.message);
            throw new Error('Failed to create subscription_features table');
        }
    }

    static async getFeaturesByPackageId(packageId) {
    const query = `
        SELECT 
            sf.id,
            sf.is_active,
            f.id AS feature_id,
            f.name AS feature_name,
            f.type AS feature_type,
            f.discount_percentage AS feature_discount_percentage
        FROM 
            subscription_features sf
        JOIN 
            feature f ON sf.feature_id = f.id
        WHERE 
            sf.package_id = ?
    `;
    const [rows] = await pool.query(query, [packageId]);
    return rows;
}



    static async addFeatureToPackage(packageId, featureId) {
        const query = `
            INSERT INTO subscription_features (package_id, feature_id)
            VALUES (?, ?)
        `;
        const [result] = await pool.query(query, [packageId, featureId]);
        return result;
    }


    static async toggleFeatureStatus(id, isActive) {
        const query = `
            UPDATE subscription_features 
            SET is_active = ?, updated_at = CURRENT_TIMESTAMP 
            WHERE id = ?
        `;
        const [result] = await pool.query(query, [isActive ? 1 : 0, id]);
        return result;
    }

    static async updateFeature(id, feature_id) {
        const query = `
            UPDATE subscription_features 
            SET feature_id = ?, updated_at = CURRENT_TIMESTAMP 
            WHERE id = ?
        `;
        const [result] = await pool.query(query, [feature_id, id]);
        return result;
    }




    static async deleteFeature(id) {
        const query = `DELETE FROM subscription_features WHERE id = ?`;
        const [result] = await pool.query(query, [id]);
        return result;
    }


}

module.exports = SubscriptionFeatureModel;
