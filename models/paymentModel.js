const pool = require("../config/db");
const { checkTableExist } = require("../utils/dbUtils");

class PaymentMethodModel {
    /**
     * Create the payment_methods table
     */
    static async createTable() {
        const query = `
        CREATE TABLE IF NOT EXISTS payment_methods (
            id INT AUTO_INCREMENT PRIMARY KEY,
            code VARCHAR(50) NOT NULL UNIQUE,         -- e.g., 'paypal', 'stripe'
            display_name VARCHAR(100) NOT NULL,       -- e.g., 'PayPal', 'Stripe'
            is_active TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        );
    `;
        try {
            await checkTableExist("payment_methods", query);

            // Optional: Insert default payment methods if they don't exist
            const insertQuery = `
            INSERT IGNORE INTO payment_methods (code, display_name)
            VALUES 
              ('paypal', 'PayPal'),
              ('cash', 'Cash Payment');
        `;
            await pool.query(insertQuery);

        } catch (err) {
            console.error("Error creating payment_methods table:", err.message);
            throw new Error("Failed to create payment_methods table");
        }
    }
    /**
     * Create a new payment method
     */
    static async create({ code, display_name, is_active = true }) {
        const query = `
            INSERT INTO payment_methods (code, display_name, is_active)
            VALUES (?, ?, ?)
        `;
        const params = [
            code,
            display_name,
            is_active
        ];
        const [result] = await pool.query(query, params);
        return result.insertId;
    }

    /**
     * Get all payment methods
     */
    static async getAll() {
        const [rows] = await pool.query(`
            SELECT * FROM payment_methods
        `);
        return rows;
    }

    /**
     * Get payment method by ID
     */
    static async getById(id) {
        const [rows] = await pool.query(
            `SELECT * FROM payment_methods WHERE id = ?`,
            [id]
        );
        return rows[0] || null;
    }

    /**
     * Update a payment method
     */
    static async update(id, code,
        display_name) {
        const query = `
            UPDATE payment_methods
            SET code = ?, display_name = ?
            WHERE id = ?
        `;
        const params = [
            code,
            display_name,
            id
        ];
        await pool.query(query, params);
    }

    /**
     * Update is_active status
     */
    static async updateIsActive(id, is_active) {
        const query = `
            UPDATE payment_methods
            SET is_active = ?
            WHERE id = ?
        `;
        await pool.query(query, [is_active, id]);
    }

    /**
     * Remove (delete) a payment method
     */
    static async remove(id) {
        await pool.query(`DELETE FROM payment_methods WHERE id = ?`, [id]);
    }
}

module.exports = PaymentMethodModel;