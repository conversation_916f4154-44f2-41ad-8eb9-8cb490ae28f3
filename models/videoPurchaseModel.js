const pool = require("../config/db");

class VideoPurchaseModel {
    static async createTable() {
        const query = `
      CREATE TABLE IF NOT EXISTS video_purchases (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        video_id INT NOT NULL,
        price DECIMAL(10, 2) NOT NULL,
        purchased_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY unique_purchase (user_id, video_id),
        FOREIGN KEY (video_id) REFERENCES videos(id) ON DELETE CASCADE
      );
    `;
        await pool.query(query);
    }

    static async isPurchased(userId, videoId) {
        const [rows] = await pool.query(
            "SELECT id FROM video_purchases WHERE user_id = ? AND video_id = ?",
            [userId, videoId]
        );
        return rows.length > 0;
    }

    static async createPurchase(userId, videoId, price) {
        const [exists] = await pool.query(
            "SELECT id FROM video_purchases WHERE user_id = ? AND video_id = ?",
            [userId, videoId]
        );
        if (exists.length > 0) {
            throw new Error("Video already purchased");
        }

        const [result] = await pool.query(
            "INSERT INTO video_purchases (user_id, video_id, price) VALUES (?, ?, ?)",
            [userId, videoId, price]
        );

        return result.insertId;
    }

    static async getUserPurchasedVideos(userId) {
        const query = `
      SELECT 
        vp.id AS purchase_id,
        vp.price AS price_paid,
        vp.purchased_at,
        v.id AS video_id,
        v.text AS video_name,
        v.description,
        v.image,
        v.price AS video_price,
        v.type
      FROM video_purchases vp
      JOIN videos v ON vp.video_id = v.id
      WHERE vp.user_id = ?
      ORDER BY vp.purchased_at DESC
    `;
        const [rows] = await pool.query(query, [userId]);
        return rows;
    }

    static async purchaseVideo(req, res) {
        const { video_id, price } = req.body;
        const user_id = req.user?.id;

        try {
            const isPurchased = await VideoPurchaseModel.isPurchased(user_id, video_id);
            if (isPurchased) {
                return res.status(400).json({ message: "Video already purchased" });
            }

            const purchaseId = await VideoPurchaseModel.createPurchase(user_id, video_id, price);
            return res.status(201).json({ message: "Video purchased", purchaseId });
        } catch (err) {
            console.error("Purchase error:", err);
            return res.status(500).json({ message: "Server error" });
        }
    }

    /**
     * Check if a user has purchased a video
     * @param {number} userId - ID of the user
     * @param {number} videoId - ID of the video
     * @returns {Promise<boolean>}
     */
    static async isVideoPurchased(userId, videoId) {
        try {
            const [rows] = await pool.query(
                "SELECT id FROM video_purchases WHERE user_id = ? AND video_id = ?",
                [userId, videoId]
            );
            return rows.length > 0;
        } catch (error) {
            console.error("Database error in isVideoPurchased:", error);
            throw error;
        }
    }
}

module.exports = VideoPurchaseModel;