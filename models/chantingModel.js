const pool = require('../config/db');
const { checkTableExist } = require('../utils/dbUtils');

class ChantingModel {
    // Create the chanting table
    static async createTable() {
        const query = `
            CREATE TABLE IF NOT EXISTS chanting (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                question_id INT NOT NULL,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE
           );
        `;

        try {
            await checkTableExist("chanting", query);
        } catch (err) {
            throw err;
        }
    }

    // Create a new chanting entry for a user and question
    static async createUserChanting(userId, questionId) {
    try {
        // Validate userId and questionId before proceeding
        if (!userId || !questionId) {
            throw new Error("User ID and Question ID are required.");
        }

        // SQL query to insert a new chanting record with user_id and question_id
        const insertQuery = `
            INSERT INTO chanting (user_id, question_id, is_active, created_at, updated_at)
            VALUES (?, ?, TRUE, NOW(), NOW());
        `;

        // Execute the query with the user_id and question_id
        const [result] = await pool.query(insertQuery, [userId, questionId]);

        // Return the success response with the new ID of the chanting record
        return { success: true, id: result.insertId };
    } catch (error) {
        // Log the error message for debugging
        console.error("Error creating chanting:", error.message);

        // Throw a user-friendly error
        throw new Error("Failed to create chanting record. Please try again later.");
    }
}


    // Get chanting count (return count instead of rows)
    static async getChantingCount(user_id, question_id) {
        const sql = `SELECT COUNT(*) AS count FROM chanting WHERE user_id = ? AND question_id = ?`;

        try {
            const [rows] = await pool.query(sql, [user_id, question_id]);
            return rows[0].count;  // Return the count of chanting records for this user-question pair
        } catch (err) {
            throw new Error('Failed to fetch chanting count');
        }
    }

    // Check if a question exists
    static async checkIfQuestionExists(question_id) {
        const sql = `SELECT COUNT(*) AS count FROM questions WHERE id = ?`;

        try {
            const [rows] = await pool.query(sql, [question_id]);
            return rows[0].count > 0;  // Return true if the question exists, false otherwise
        } catch (err) {
            console.error("Error checking if question exists:", err.message);
            throw new Error("Failed to check if question exists");
        }
    }

    // Get all active chantings for a specific user
    static async getUserChantings(user_id) {
        try {
            // SQL query to fetch chantings for a given user_id where the chanting is active
            const sql = `
                SELECT 
                    id, 
                    question_id,
                    is_active, 
                    created_at, 
                    updated_at 
                FROM chanting 
                WHERE user_id = ? AND is_active = 1
            `;
            
            // Execute the query with the user_id parameter
            const [rows] = await pool.query(sql, [user_id]);
    
            // If no chantings are found, return an empty data array and success flag as false
            if (rows.length === 0) {
                return {
                    user_id: user_id,
                    data: [],
                    success: true
                };
            }
    
            // Return the result in the specified format with the user_id and formatted chantings data
            return {
                user_id: user_id,  // The user_id in the response
                data: rows.map(row => ({
                    id: row.id,
                    question_id: row.question_id,
                    is_active: row.is_active,
                    created_at: row.created_at,
                    updated_at: row.updated_at
                })),
                success: true  // Flag indicating successful retrieval of data
            };
        } catch (err) {
            // Log the error with detailed information and re-throw the error with a user-friendly message
            console.error(`Error fetching chantings for user_id ${user_id}:`, err.message);
    
            // Throw a user-friendly error message
            throw new Error("Failed to fetch chantings. Please try again later.");
        }
    }


    static async getQuestionById(question_id) {
        const sql = `
            SELECT id, question_type
            FROM questions
            WHERE id = ? 
            LIMIT 1
        `;
        const [rows] = await pool.query(sql, [question_id]);

        // Return the question data or null if not found
        return rows.length ? rows[0] : null;
    }

    

    // Permanently delete a chanting entry
    static async deleteChanting(id) {
        const query = `
            DELETE FROM chanting WHERE id = ?;
        `;
        try {
            const [result] = await pool.query(query, [id]);
            if (result.affectedRows === 0) {
                throw new Error("No record found to delete.");
            }
            return { success: true };
        } catch (error) {
            throw new Error("Failed to delete chanting record.");
        }
    }
}

module.exports = ChantingModel;
