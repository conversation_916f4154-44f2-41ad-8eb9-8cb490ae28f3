const pool = require('../config/db');
const { checkTableExist } = require('../utils/dbUtils');

class TopicModel {

    static async createTopicTable() {
        const sql = `CREATE TABLE IF NOT EXISTS topics (
            id INT NOT NULL AUTO_INCREMENT,
            image VARCHAR(255) NOT NULL,
            name VARCHAR(255) NOT NULL,
            parent_id INT DEFAULT NULL,
            type ENUM('category', 'sub_category') NOT NULL DEFAULT 'category',
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            FOREIGN KEY (parent_id) REFERENCES topics(id) ON DELETE CASCADE
        );`;

        const sql2 = `CREATE TABLE IF NOT EXISTS topic_users (
            id INT NOT NULL AUTO_INCREMENT,
            user_id INT NOT NULL,
            topic_id INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (topic_id) REFERENCES topics(id) ON DELETE CASCADE
        );`;

        try {
            await checkTableExist('topics', sql);
            await checkTableExist('topic_users', sql2);
        } catch (err) {
            console.error('Error creating Topic table:', err.message);
            throw new Error('Failed to create Topic table');
        }
    }

    static async createTopic(topic) {
        const sql = `INSERT INTO topics (image, name, parent_id, type) VALUES (?, ?, ?, ?)`;

        try {
            await pool.query(sql, [topic.image, topic.name, topic.parent_id || null, topic.type || 'category']);
        } catch (err) {
            console.error('Error creating Topic:', err.message);
            throw new Error('Failed to create Topic');
        }
    }

    static async userChooseTopics(userId, topicIds) {
        const sql = `INSERT INTO topic_users (user_id, topic_id) VALUES (?, ?)`;

        try {
            for (const topicId of topicIds) {
                await pool.query(sql, [userId, topicId]);
            }
        } catch (err) {
            console.error('Error choosing topics:', err.message);
            throw new Error('Failed to choose topics');
        }
    }

    static async getExistingTopicsByIds(topicIds) {
        const sql = `SELECT id FROM topics WHERE id IN (?)`;
        try {
            const [rows] = await pool.query(sql, [topicIds]);
            return rows;
        } catch (err) {
            console.error('Error verifying topic IDs:', err.message);
            throw new Error('Failed to verify topic IDs');
        }
    }

    static async getUserTopics(userId) {
        const sql = `SELECT topic_id FROM topic_users WHERE user_id = ?`;
        try {
            const [rows] = await pool.query(sql, [userId]);
            return rows;
        } catch (err) {
            console.error('Error getting User Topics:', err.message);
            throw new Error('Failed to get User Topics');
        }
    }

    static async getAllTopics() {
        const sql = `SELECT * FROM topics`;
        try {
            const [rows] = await pool.query(sql);
            return rows;
        } catch (err) {
            console.error('Error getting all Topics:', err.message);
            throw new Error('Failed to get all Topics');
        }
    }

    static async getTopicById(id) {
        const sql = `SELECT * FROM topics WHERE id = ?`;
        try {
            const [rows] = await pool.query(sql, [id]);
            return rows[0];
        } catch (err) {
            console.error('Error getting Topic by id:', err.message);
            throw new Error('Failed to get Topic by id');
        }
    }

    static async updateTopic(id, topic) {
        const sql = `UPDATE topics SET image = ?, name = ?, parent_id = ?, type = ? WHERE id = ?`;
        try {
            await pool.query(sql, [topic.image, topic.name, topic.parent_id || null, topic.type || 'category', id]);
        } catch (err) {
            console.error('Error updating Topic:', err.message);
            throw new Error('Failed to update Topic');
        }
    }

    static async activateTopic(id) {
        const sql = `UPDATE topics SET is_active = 1 WHERE id = ?`;
        try {
            await pool.query(sql, [id]);
        } catch (err) {
            console.error('Error activating Topic:', err.message);
            throw new Error('Failed to activate Topic');
        }
    }

    static async updateUserTopicById(userId, topicIds) {
        const deleteSql = `DELETE FROM topic_users WHERE user_id = ?`;
        const insertSql = `INSERT INTO topic_users (user_id, topic_id) VALUES (?, ?)`;
        const connection = await pool.getConnection();

        try {
            await connection.beginTransaction();
            await connection.query(deleteSql, [userId]);

            for (const topicId of topicIds) {
                await connection.query(insertSql, [userId, topicId]);
            }

            await connection.commit();
        } catch (err) {
            await connection.rollback();
            console.error('Error updating user topics:', err.message);
            throw new Error('Failed to update user topics');
        } finally {
            connection.release();
        }
    }

    static async updateTopicStatus(id, isActive) {
        try {
            const query = `UPDATE topics SET is_active = ? WHERE id = ?`;
            const [result] = await pool.query(query, [isActive, id]);

            if (result.affectedRows === 0) {
                throw new Error("Topic not found.");
            }

            return { success: true, message: "Topic status updated successfully." };
        } catch (error) {
            console.error("Error updating topic status:", error.message);
            throw new Error("Failed to update topic status.");
        }
    }

    static async deleteTopic(id) {
        const sql = `DELETE FROM topics WHERE id = ?`;
        try {
            await pool.query(sql, [id]);
        } catch (err) {
            console.error('Error deleting Topic:', err.message);
            throw new Error('Failed to delete Topic');
        }
    }
}

module.exports = TopicModel;
