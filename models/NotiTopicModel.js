const pool = require("../config/db");
const { checkTableExist } = require("../utils/dbUtils");

class NotiTopicModel {
    static async createNewTable() {
        const sql =  `CREATE TABLE IF NOT EXISTS noti_topic (
            id INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
            topic_id INT NOT NULL,
            text VARCHAR(255) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (topic_id) REFERENCES topics(id) ON DELETE CASCADE
        )`;

        try {
            await checkTableExist("noti_topic", sql);
        } catch (err) {
            console.error("Error creating Notification Text table:", err.message);
            throw new Error("Failed to create Notification Text table");
        }
    }

    static async createNotification(topic_id, text) {
        try {
            const query = `INSERT INTO noti_topic (topic_id, text) VALUES (?, ?)`;
            const values = [topic_id, text];
            const [result] = await pool.query(query, values);
            return result;
        } catch (err) {
            console.error("Error creating Notification Text:", err.message);
            throw err;
        }
    }

    // ✅ Add this function to fetch notifications
    static async getNotificationsByTopicId(topic_id) {
        try {
            const query = `SELECT * FROM noti_topic WHERE topic_id = ?`;
            const [result] = await pool.query(query, [topic_id]);
            return result;
        } catch (err) {
            console.error("Error fetching notifications:", err.message);
            throw err;
        }
    }

    static async updateNotificationById(id, text) {
        const sql = `UPDATE notifications SET text = ? WHERE id = ?`;
        try {
          await pool.query(sql, [text, id]);
        } catch (err) {
          console.error("Error updating notification:", err.message);
          throw new Error("Failed to update notification");
        }
      }
      
}

module.exports = NotiTopicModel;
