const pool = require("../config/db");

class BooksPurchaseModel {
    static async createTable() {
        const query = `
      CREATE TABLE IF NOT EXISTS books_purchases (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        book_id INT NOT NULL,
        price DECIMAL(10, 2) NOT NULL,
        purchased_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY unique_purchase (user_id, book_id)
      );
    `;
        await pool.query(query);
    }

    static async isPurchased(user_id, book_id) {
        const [rows] = await pool.query(
            "SELECT id FROM books_purchases WHERE user_id = ? AND book_id = ?",
            [user_id, book_id]
        );
        return rows.length > 0;
    }

    static async getUserPurchasedBooks(userId) {
        const query = `
      SELECT 
        bp.id AS purchase_id,
        bp.price AS price_paid,
        bp.purchased_at AS created_at,
        b.id AS book_id,
        b.name AS book_name,
        b.image,
        b.price AS book_price
      FROM books_purchases bp
      JOIN books b ON bp.book_id = b.id
      WHERE bp.user_id = ?
      ORDER BY bp.purchased_at DESC
    `;
        const [rows] = await pool.query(query, [userId]);
        return rows;
    }

    static async purchaseBook(user_id, book_id, price) {
        const query = `
    INSERT INTO books_purchases (user_id, book_id, price)
    VALUES (?, ?, ?)
  `;
        await pool.query(query, [user_id, book_id, price]);
    }

    static async getBookPrice(book_id) {
        const [rows] = await pool.query("SELECT price FROM books WHERE id = ?", [book_id]);
        return rows.length ? rows[0].price : null;
    }


    // Reports yearly / monthly / daily

    static async getDailyRevenue() {
    const [rows] = await pool.query(`
      SELECT DATE(purchased_at) AS day, SUM(price) AS total
      FROM books_purchases
      GROUP BY day
      ORDER BY day DESC
    `);
    return rows;
  }

  static async getMonthlyRevenue() {
    const [rows] = await pool.query(`
      SELECT YEAR(purchased_at) AS year, MONTH(purchased_at) AS month, SUM(price) AS total
      FROM books_purchases
      GROUP BY year, month
      ORDER BY year DESC, month DESC
    `);
    return rows;
  }

  static async getYearlyRevenue() {
    const [rows] = await pool.query(`
      SELECT YEAR(purchased_at) AS year, SUM(price) AS total
      FROM books_purchases
      GROUP BY year
      ORDER BY year DESC
    `);
    return rows;
  }
}

module.exports = BooksPurchaseModel;