const pool = require("../config/db");
const { checkTableExist } = require("../utils/dbUtils");
const { replaceOrUpdateColumn } = require("../helper/replaceColumn");
const { columnExistsInTable } = require("../helper/columnExist");

class UserSubscriptionModel {
    /**
     * Create the user_subscriptions table
     */
    static async createTable() {
        const query = `
            CREATE TABLE IF NOT EXISTS user_subscriptions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                subscription_type ENUM('package', 'item') NOT NULL,
                reference_id INT NOT NULL,
                plan_type ENUM('monthly', 'yearly') NOT NULL,
                start_date DATE NOT NULL,
                end_date DATE NOT NULL,
                price_paid DECIMAL(10,2) NOT NULL,
                currency VARCHAR(10) DEFAULT 'USD',
                payment_method_id INT DEFAULT NULL,
                payment_reference VARCHAR(255) DEFAULT NULL,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id),
                FOREIGN KEY (payment_method_id) REFERENCES payment_methods(id)
            );
        `;

        try {
            // ✅ Create table if not exists
            await checkTableExist("user_subscriptions", query);

            // 🔍 Check and add subscription_type column
            const subscriptionTypeExists = await columnExistsInTable("user_subscriptions", "subscription_type");
            if (!subscriptionTypeExists) {
                await replaceOrUpdateColumn({
                    tableName: "user_subscriptions",
                    newColumn: "subscription_type",
                    newDefinition: "ENUM('package', 'item') NOT NULL",
                    afterColumn: "user_id"
                });
            }

            // 🔍 Check and add currency column
            const currencyExists = await columnExistsInTable("user_subscriptions", "currency");
            if (!currencyExists) {
                await replaceOrUpdateColumn({
                    tableName: "user_subscriptions",
                    newColumn: "currency",
                    newDefinition: "VARCHAR(10) DEFAULT 'USD'",
                    afterColumn: "price_paid"
                });
            }

            // 🔍 Check and add payment_method_id column
            const paymentMethodExists = await columnExistsInTable("user_subscriptions", "payment_method_id");
            if (!paymentMethodExists) {
                await replaceOrUpdateColumn({
                    tableName: "user_subscriptions",
                    newColumn: "payment_method_id",
                    newDefinition: "INT DEFAULT NULL",
                    afterColumn: "currency"
                });

                // Manually add the foreign key constraint (if not already defined)
                const [fkRows] = await pool.query(`
        SELECT CONSTRAINT_NAME
        FROM information_schema.KEY_COLUMN_USAGE
        WHERE TABLE_NAME = 'user_subscriptions'
          AND COLUMN_NAME = 'payment_method_id'
          AND CONSTRAINT_SCHEMA = DATABASE();
      `);

                if (fkRows.length === 0) {
                    await pool.query(`
          ALTER TABLE user_subscriptions
          ADD CONSTRAINT fk_payment_method FOREIGN KEY (payment_method_id) REFERENCES payment_methods(id);
        `);
                    console.log("🔗 Foreign key fk_payment_method added");
                } else {
                    console.log("✅ Foreign key for payment_method_id already exists");
                }
            }

        } catch (err) {
            console.error("❌ Error creating user_subscriptions table:", err.message);
            throw new Error("Failed to create user_subscriptions table");
        }
    }

    /**
     * Create a new subscription record
     */
    static async createSubscription(data) {
        const query = `
            INSERT INTO user_subscriptions
            (
                user_id,
                subscription_type,
                reference_id,
                plan_type,
                start_date,
                end_date,
                price_paid,
                currency,
                payment_method_id,
                payment_reference
            )
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;

        const params = [
            data.user_id,
            data.subscription_type || 'package',
            data.reference_id,
            data.plan_type,
            data.start_date,
            data.end_date,
            data.price_paid,
            data.currency || "USD",
            Number.isInteger(data.payment_method_id) ? data.payment_method_id : null,
            data.payment_reference || ""
        ];

        try {
            await pool.query(query, params);
        } catch (err) {
            console.error("❌ Database error in createSubscription:", err.message);
            throw err;
        }
    }

    static async getLatestSubscription(user_id) {
        const sql = `
            SELECT * FROM user_subscriptions
            WHERE user_id = ?
            ORDER BY start_date DESC
            LIMIT 1
        `;
        const [rows] = await pool.query(sql, [user_id]);
        return rows[0] || null;
    }

    /**
     * Get all active subscriptions for a user
     */
    static async getUserActiveSubscriptionWithPackage(userId) {
        const [rows] = await pool.query(
            `SELECT 
            us.id,
            us.reference_id AS package_id,
            sp.name AS package_name,
            us.plan_type,
            us.start_date,
            us.end_date,
            us.price_paid,
            us.currency,
            us.is_active
        FROM user_subscriptions us
        JOIN subscription_packages sp ON us.reference_id = sp.id
        WHERE us.user_id = ?
          AND us.is_active = 1
          AND us.end_date >= CURDATE()
          AND us.subscription_type = 'package'
        ORDER BY us.end_date DESC
        LIMIT 1
        `,
            [userId]
        );

        return rows.length > 0 ? rows[0] : null;
    }

    static async getActiveSubscriptionByUserId(userId) {
        const sql = `
            SELECT *
            FROM user_subscriptions
            WHERE user_id = ?  AND end_date >= CURDATE()
            ORDER BY end_date DESC
            LIMIT 1
        `;
        const [rows] = await pool.query(sql, [userId]);
        return rows.length > 0 ? rows[0] : null;
    }

    async getUserActiveSubscription(userId) {
        const [rows] = await db.execute(
            `SELECT us.*, sp.name AS package_name
            FROM user_subscriptions us
            JOIN subscription_packages sp ON us.package_id = sp.id
            WHERE us.user_id = ?
                AND us.is_active = 1
                AND us.end_date >= CURDATE()
        `,
            [userId]
        );

        if (rows.length === 0) {
            return null;
        }
        return rows[0];
    }

    /**
     * Check if a user has an active subscription of a specific type
     */
    static async hasActiveSubscription(user_id, subscription_type, reference_id) {
        const [rows] = await pool.query(
            `
            SELECT * FROM user_subscriptions
            WHERE user_id = ?
              AND subscription_type = ?
              AND reference_id = ?
              AND is_active = 1
              AND end_date >= CURDATE()
            `,
            [user_id, subscription_type, reference_id]
        );
        return rows[0] || null;
    }

    // Get subscription features for Login
    // models/UserSubscriptionModel.js

    static async getSubscriptionFeatures(userSubscriptionId) {
        const [features] = await pool.query(
            `
    SELECT 
      f.id AS feature_id,
      f.name AS feature_name,
      f.type AS feature_type,
      f.discount_percentage AS feature_discount_percentage
    FROM user_subscriptions us
    JOIN subscription_features sf ON us.reference_id = sf.package_id
    JOIN feature f ON sf.feature_id = f.id
    WHERE us.id = ?
    `,
            [userSubscriptionId]
        );
        return features;
    }

    static async deleteUserSubscriptions(user_id) {
        const sql = `
            DELETE FROM user_subscriptions
            WHERE user_id = ?
        `;
        try {
            const [result] = await pool.query(sql, [user_id]);
            console.log(`🗑️ Deleted ${result.affectedRows} existing subscriptions for user ${user_id}`);
            return result;
        } catch (err) {
            console.error("❌ Error deleting old subscriptions:", err.message);
            throw err;
        }
    }
}

module.exports = UserSubscriptionModel;