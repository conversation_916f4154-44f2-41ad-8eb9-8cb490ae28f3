const pool = require('../config/db');
const { checkTableExist } = require('../utils/dbUtils');

class ComparisonHistoryModel {
  static async createTable() {
    const query = `
      CREATE TABLE IF NOT EXISTS comparison_history (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        thought_id INT NOT NULL,
        before_score FLOAT NOT NULL,
        after_score FLOAT DEFAULT NULL,
        after_score_audio FLOAT DEFAULT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (thought_id) REFERENCES thoughts(id)
      );
    `;

    try {
      await checkTableExist("comparison_history", query);
    } catch (err) {
      console.error("Error creating comparison_history table:", err.message);
      throw new Error("Failed to create comparison_history table");
    }
  }

  static async createComparisonHistoryAudioByUserId(userId, thoughtId, { before_score }) {
    const [result] = await pool.query(
      'INSERT INTO comparison_history (user_id, thought_id, before_score) VALUES (?, ?, ?)',
      [userId, thoughtId, before_score]
    );
    return result;
  }

  static async getAllComparisonHistoryByUserId(userId) {
    const [rows] = await pool.query(
      'SELECT * FROM comparison_history WHERE user_id = ?',
      [userId]
    );
    return rows;
  }

  static async getTodayLatestHistoryId(userId, thoughtId) {
    const [rows] = await pool.query(
      `
        SELECT id FROM comparison_history
        WHERE user_id = ? AND thought_id = ? AND DATE(created_at) = CURDATE()
        ORDER BY created_at DESC
        LIMIT 1
      `,
      [userId, thoughtId]
    );
    return rows.length ? rows[0].id : null;
  }

  static async updateAfterScoreById(userId, thoughtId, after_score) {
    const historyId = await this.getTodayLatestHistoryId(userId, thoughtId);
    if (!historyId) return { affectedRows: 0 };

    const [result] = await pool.query(
      `UPDATE comparison_history SET after_score = ? WHERE id = ?`,
      [after_score, historyId]
    );
    return result;
  }

  static async updateAfterScoreAudioById(userId, thoughtId, after_score_audio) {
    const historyId = await this.getTodayLatestHistoryId(userId, thoughtId);
    if (!historyId) return { affectedRows: 0 };

    const [result] = await pool.query(
      `UPDATE comparison_history SET after_score_audio = ? WHERE id = ?`,
      [after_score_audio, historyId]
    );
    return result;
  }
}

module.exports = ComparisonHistoryModel;