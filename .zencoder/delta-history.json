{"snapshots": {"/terminal_output": {"filePath": "/terminal_output", "baseContent": "xrgouda@Amrs-MacBook-Air ArabCBT_back % npm i\nnpm warn deprecated @paypal/checkout-server-sdk@1.0.3: Package no longer supported. The author suggests using the @paypal/paypal-server-sdk package instead: https://www.npmjs.com/package/@paypal/paypal-server-sdk. Contact Support at https://www.npmjs.com/support for more info.\n\nadded 384 packages, and audited 385 packages in 2m\n\n41 packages are looking for funding\n  run `npm fund` for details\n\n5 vulnerabilities (4 low, 1 critical)\n\nTo address all issues, run:\n  npm audit fix\n\nRun `npm audit` for details.\nxrgouda@Amrs-MacBook-Air ArabCBT_back % ", "baseTimestamp": 1757934161456, "deltas": [{"timestamp": 1757934179102, "changes": [{"type": "INSERT", "lineNumber": 14, "content": "xrgouda@Amrs-MacBook-Air ArabCBT_back % npm run develop"}, {"type": "INSERT", "lineNumber": 15, "content": "npm error Missing script: \"develop\""}, {"type": "INSERT", "lineNumber": 16, "content": "npm error"}, {"type": "INSERT", "lineNumber": 17, "content": "npm error To see a list of scripts, run:"}, {"type": "INSERT", "lineNumber": 18, "content": "npm error   npm run"}, {"type": "INSERT", "lineNumber": 19, "content": "npm error A complete log of this run can be found in: /Users/<USER>/.npm/_logs/2025-09-15T11_02_56_989Z-debug-0.log"}]}, {"timestamp": 1758008527483, "changes": [{"type": "DELETE", "lineNumber": 0, "oldContent": "xrgouda@Amrs-MacBook-Air ArabCBT_back % npm i"}, {"type": "DELETE", "lineNumber": 1, "oldContent": "npm warn deprecated @paypal/checkout-server-sdk@1.0.3: Package no longer supported. The author suggests using the @paypal/paypal-server-sdk package instead: https://www.npmjs.com/package/@paypal/paypal-server-sdk. Contact Support at https://www.npmjs.com/support for more info."}, {"type": "INSERT", "lineNumber": 0, "content": "xrgouda@Amrs-MacBook-Air ArabCBT_back % "}, {"type": "DELETE", "lineNumber": 3, "oldContent": "added 384 packages, and audited 385 packages in 2m"}, {"type": "DELETE", "lineNumber": 5, "oldContent": "41 packages are looking for funding"}, {"type": "DELETE", "lineNumber": 6, "oldContent": "  run `npm fund` for details"}, {"type": "DELETE", "lineNumber": 8, "oldContent": "5 vulnerabilities (4 low, 1 critical)"}, {"type": "DELETE", "lineNumber": 10, "oldContent": "To address all issues, run:"}, {"type": "DELETE", "lineNumber": 11, "oldContent": "  npm audit fix"}, {"type": "DELETE", "lineNumber": 13, "oldContent": "Run `npm audit` for details."}, {"type": "DELETE", "lineNumber": 14, "oldContent": "xrgouda@Amrs-MacBook-Air ArabCBT_back % npm run develop"}, {"type": "DELETE", "lineNumber": 15, "oldContent": "xrgouda@Amrs-MacBook-Air ArabCBT_back % "}, {"type": "DELETE", "lineNumber": 16, "oldContent": "npm error Missing script: \"develop\""}, {"type": "DELETE", "lineNumber": 17, "oldContent": "npm error A complete log of this run can be found in: /Users/<USER>/.npm/_logs/2025-09-15T11_02_56_989Z-debug-0.log"}, {"type": "DELETE", "lineNumber": 18, "oldContent": "npm error"}, {"type": "DELETE", "lineNumber": 19, "oldContent": "npm error   npm run"}, {"type": "DELETE", "lineNumber": 20, "oldContent": "npm error To see a list of scripts, run:"}, {"type": "INSERT", "lineNumber": 6, "content": ""}, {"type": "INSERT", "lineNumber": 7, "content": ""}, {"type": "INSERT", "lineNumber": 8, "content": ""}, {"type": "INSERT", "lineNumber": 9, "content": ""}, {"type": "INSERT", "lineNumber": 10, "content": ""}, {"type": "INSERT", "lineNumber": 11, "content": ""}]}, {"timestamp": 1758008559772, "changes": [{"type": "MODIFY", "lineNumber": 0, "content": "xrgouda@Amrs-MacBook-Air ArabCBT_back % brew services start mysql@8.0", "oldContent": "xrgouda@Amrs-MacBook-Air ArabCBT_back % "}]}, {"timestamp": 1758008574745, "changes": [{"type": "INSERT", "lineNumber": 1, "content": "Service `mysql@8.0` already started, use `brew services restart mysql@8.0` to restart."}, {"type": "INSERT", "lineNumber": 2, "content": "xrgouda@Amrs-MacBook-Air ArabCBT_back % "}, {"type": "DELETE", "lineNumber": 10, "oldContent": ""}, {"type": "DELETE", "lineNumber": 11, "oldContent": ""}]}, {"timestamp": 1758008737363, "changes": [{"type": "INSERT", "lineNumber": 2, "content": "xrgouda@Amrs-MacBook-Air ArabCBT_back % npm run dev"}, {"type": "DELETE", "lineNumber": 3, "oldContent": "xrgouda@Amrs-MacBook-Air ArabCBT_back % "}, {"type": "INSERT", "lineNumber": 4, "content": "> backend@1.0.0 dev"}, {"type": "INSERT", "lineNumber": 5, "content": "> nodemon index.js"}, {"type": "INSERT", "lineNumber": 7, "content": "sh: nodemon: command not found"}, {"type": "INSERT", "lineNumber": 8, "content": "xrgouda@Amrs-MacBook-Air ArabCBT_back % npm i"}, {"type": "INSERT", "lineNumber": 9, "content": "npm warn deprecated @paypal/checkout-server-sdk@1.0.3: Package no longer supported. The author suggests using the @paypal/paypal-server-sdk package instead: https://www.npmjs.com/package/@paypal/paypal-server-sdk. Contact Support at https://www.npmjs.com/support for more info."}, {"type": "INSERT", "lineNumber": 11, "content": "added 384 packages, and audited 385 packages in 3m"}, {"type": "INSERT", "lineNumber": 13, "content": "41 packages are looking for funding"}, {"type": "INSERT", "lineNumber": 14, "content": "  run `npm fund` for details"}, {"type": "INSERT", "lineNumber": 16, "content": "5 vulnerabilities (4 low, 1 critical)"}, {"type": "INSERT", "lineNumber": 18, "content": "To address all issues, run:"}, {"type": "INSERT", "lineNumber": 19, "content": "  npm audit fix"}, {"type": "DELETE", "lineNumber": 10, "oldContent": ""}, {"type": "DELETE", "lineNumber": 11, "oldContent": ""}, {"type": "INSERT", "lineNumber": 21, "content": "Run `npm audit` for details."}, {"type": "INSERT", "lineNumber": 22, "content": "xrgouda@Amrs-MacBook-Air ArabCBT_back % "}]}, {"timestamp": 1758008743187, "changes": [{"type": "DELETE", "lineNumber": 5, "oldContent": ""}, {"type": "DELETE", "lineNumber": 8, "oldContent": ""}, {"type": "DELETE", "lineNumber": 10, "oldContent": ""}, {"type": "DELETE", "lineNumber": 12, "oldContent": ""}, {"type": "DELETE", "lineNumber": 16, "oldContent": "xrgouda@Amrs-MacBook-Air ArabCBT_back % "}, {"type": "INSERT", "lineNumber": 12, "content": ""}, {"type": "DELETE", "lineNumber": 18, "oldContent": "Run `npm audit` for details."}, {"type": "DELETE", "lineNumber": 20, "oldContent": "  npm audit fix"}, {"type": "DELETE", "lineNumber": 21, "oldContent": "To address all issues, run:"}, {"type": "INSERT", "lineNumber": 15, "content": ""}, {"type": "INSERT", "lineNumber": 17, "content": ""}, {"type": "INSERT", "lineNumber": 18, "content": "To address all issues, run:"}, {"type": "INSERT", "lineNumber": 19, "content": "  npm audit fix"}, {"type": "INSERT", "lineNumber": 20, "content": ""}, {"type": "INSERT", "lineNumber": 21, "content": "Run `npm audit` for details."}, {"type": "INSERT", "lineNumber": 22, "content": "xrgouda@Amrs-MacBook-Air ArabCBT_back % npm run dev"}, {"type": "INSERT", "lineNumber": 23, "content": ""}, {"type": "INSERT", "lineNumber": 24, "content": "> backend@1.0.0 dev"}, {"type": "INSERT", "lineNumber": 25, "content": "> nodemon index.js"}, {"type": "INSERT", "lineNumber": 26, "content": ""}, {"type": "INSERT", "lineNumber": 27, "content": "[nodemon] 3.1.9"}, {"type": "INSERT", "lineNumber": 28, "content": "[nodemon] to restart at any time, enter `rs`"}, {"type": "INSERT", "lineNumber": 29, "content": "[nodemon] watching path(s): *.*"}, {"type": "INSERT", "lineNumber": 30, "content": "[nodemon] watching extensions: js,mjs,cjs,json"}, {"type": "INSERT", "lineNumber": 31, "content": "[nodemon] starting `node index.js`"}, {"type": "INSERT", "lineNumber": 32, "content": "Listening on port http://localhost:3000"}, {"type": "INSERT", "lineNumber": 33, "content": "Database connected successfully."}, {"type": "INSERT", "lineNumber": 34, "content": "Table 'roles' already exists."}, {"type": "INSERT", "lineNumber": 35, "content": "Role \"admin\" already exists, skipping insertion."}, {"type": "INSERT", "lineNumber": 36, "content": "Role \"user\" already exists, skipping insertion."}, {"type": "INSERT", "lineNumber": 37, "content": "Role \"guest\" already exists, skipping insertion."}, {"type": "INSERT", "lineNumber": 38, "content": "Table 'permissions' already exists."}, {"type": "INSERT", "lineNumber": 39, "content": "Table 'role_permissions' already exists."}, {"type": "INSERT", "lineNumber": 40, "content": "Table 'clinics' already exists."}, {"type": "INSERT", "lineNumber": 41, "content": "Table 'users' already exists."}, {"type": "INSERT", "lineNumber": 42, "content": "Table 'user_roles' already exists."}, {"type": "INSERT", "lineNumber": 43, "content": "Table 'tokens' already exists."}, {"type": "INSERT", "lineNumber": 44, "content": "Table 'books' already exists."}, {"type": "INSERT", "lineNumber": 45, "content": "Table 'pages' already exists."}, {"type": "INSERT", "lineNumber": 46, "content": "Table 'videos' already exists."}, {"type": "INSERT", "lineNumber": 47, "content": "Table 'topics' already exists."}, {"type": "INSERT", "lineNumber": 48, "content": "Table 'topic_users' already exists."}, {"type": "INSERT", "lineNumber": 49, "content": "Table 'concerns' already exists."}, {"type": "INSERT", "lineNumber": 50, "content": "Table 'thoughts' already exists."}, {"type": "INSERT", "lineNumber": 51, "content": "Table 'audio_clips' already exists."}, {"type": "INSERT", "lineNumber": 52, "content": "Table 'questions' already exists."}, {"type": "INSERT", "lineNumber": 53, "content": "Table 'answers' already exists."}, {"type": "INSERT", "lineNumber": 54, "content": "Table 'distraction' already exists."}, {"type": "INSERT", "lineNumber": 55, "content": "Table 'meditation' already exists."}, {"type": "INSERT", "lineNumber": 56, "content": "Table 'lectures' already exists."}, {"type": "INSERT", "lineNumber": 57, "content": "Table 'discount_options' already exists."}, {"type": "INSERT", "lineNumber": 58, "content": "Table 'chanting' already exists."}, {"type": "INSERT", "lineNumber": 59, "content": "Table 'app_versions' already exists."}, {"type": "INSERT", "lineNumber": 60, "content": "Table 'subscription_packages' already exists."}, {"type": "INSERT", "lineNumber": 61, "content": "Table 'subscription_features' already exists."}, {"type": "INSERT", "lineNumber": 62, "content": "Table 'feature' already exists."}, {"type": "INSERT", "lineNumber": 63, "content": "Table 'subscription_plans' already exists."}, {"type": "INSERT", "lineNumber": 64, "content": "Table 'user_subscriptions' already exists."}, {"type": "INSERT", "lineNumber": 65, "content": "Table 'user_books' already exists."}, {"type": "INSERT", "lineNumber": 66, "content": "Table 'payment_methods' already exists."}, {"type": "INSERT", "lineNumber": 67, "content": "Table 'comparison_history' already exists."}, {"type": "INSERT", "lineNumber": 68, "content": "All tables created successfully"}, {"type": "INSERT", "lineNumber": 69, "content": "Database initialization completed."}, {"type": "INSERT", "lineNumber": 70, "content": ""}]}, {"timestamp": 1758008889637, "changes": [{"type": "DELETE", "lineNumber": 0, "oldContent": "xrgouda@Amrs-MacBook-Air ArabCBT_back % brew services start mysql@8.0"}, {"type": "DELETE", "lineNumber": 1, "oldContent": "Service `mysql@8.0` already started, use `brew services restart mysql@8.0` to restart."}, {"type": "DELETE", "lineNumber": 2, "oldContent": "xrgouda@Amrs-MacBook-Air ArabCBT_back % npm run dev"}, {"type": "INSERT", "lineNumber": 0, "content": "xrgouda@Amrs-MacBook-Air ArabCBT_back % "}, {"type": "DELETE", "lineNumber": 4, "oldContent": "> backend@1.0.0 dev"}, {"type": "DELETE", "lineNumber": 5, "oldContent": "> nodemon index.js"}, {"type": "DELETE", "lineNumber": 7, "oldContent": "sh: nodemon: command not found"}, {"type": "DELETE", "lineNumber": 8, "oldContent": "xrgouda@Amrs-MacBook-Air ArabCBT_back % npm i"}, {"type": "DELETE", "lineNumber": 10, "oldContent": "npm warn deprecated @paypal/checkout-server-sdk@1.0.3: Package no longer supported. The author suggests using the @paypal/paypal-server-sdk package instead: https://www.npmjs.com/package/@paypal/paypal-server-sdk. Contact Support at https://www.npmjs.com/support for more info."}, {"type": "DELETE", "lineNumber": 13, "oldContent": "added 384 packages, and audited 385 packages in 3m"}, {"type": "DELETE", "lineNumber": 15, "oldContent": "41 packages are looking for funding"}, {"type": "DELETE", "lineNumber": 16, "oldContent": "To address all issues, run:"}, {"type": "DELETE", "lineNumber": 17, "oldContent": "  npm audit fix"}, {"type": "DELETE", "lineNumber": 18, "oldContent": "  run `npm fund` for details"}, {"type": "DELETE", "lineNumber": 20, "oldContent": "Run `npm audit` for details."}, {"type": "DELETE", "lineNumber": 21, "oldContent": "xrgouda@Amrs-MacBook-Air ArabCBT_back % npm run dev"}, {"type": "DELETE", "lineNumber": 22, "oldContent": "5 vulnerabilities (4 low, 1 critical)"}, {"type": "DELETE", "lineNumber": 25, "oldContent": "> backend@1.0.0 dev"}, {"type": "DELETE", "lineNumber": 26, "oldContent": "Database initialization completed."}, {"type": "DELETE", "lineNumber": 27, "oldContent": "> nodemon index.js"}, {"type": "DELETE", "lineNumber": 28, "oldContent": "All tables created successfully"}, {"type": "DELETE", "lineNumber": 30, "oldContent": "Table 'comparison_history' already exists."}, {"type": "DELETE", "lineNumber": 31, "oldContent": "[nodemon] 3.1.9"}, {"type": "DELETE", "lineNumber": 32, "oldContent": "Table 'payment_methods' already exists."}, {"type": "DELETE", "lineNumber": 33, "oldContent": "[nodemon] to restart at any time, enter `rs`"}, {"type": "DELETE", "lineNumber": 34, "oldContent": "Table 'user_books' already exists."}, {"type": "DELETE", "lineNumber": 35, "oldContent": "[nodemon] watching path(s): *.*"}, {"type": "DELETE", "lineNumber": 36, "oldContent": "Table 'user_subscriptions' already exists."}, {"type": "DELETE", "lineNumber": 37, "oldContent": "[nodemon] watching extensions: js,mjs,cjs,json"}, {"type": "DELETE", "lineNumber": 38, "oldContent": "Table 'subscription_plans' already exists."}, {"type": "DELETE", "lineNumber": 39, "oldContent": "[nodemon] starting `node index.js`"}, {"type": "DELETE", "lineNumber": 40, "oldContent": "Table 'feature' already exists."}, {"type": "DELETE", "lineNumber": 41, "oldContent": "Listening on port http://localhost:3000"}, {"type": "DELETE", "lineNumber": 42, "oldContent": "Table 'subscription_features' already exists."}, {"type": "DELETE", "lineNumber": 43, "oldContent": "Database connected successfully."}, {"type": "DELETE", "lineNumber": 44, "oldContent": "Table 'subscription_packages' already exists."}, {"type": "DELETE", "lineNumber": 45, "oldContent": "Table 'roles' already exists."}, {"type": "DELETE", "lineNumber": 46, "oldContent": "Table 'app_versions' already exists."}, {"type": "DELETE", "lineNumber": 47, "oldContent": "Role \"admin\" already exists, skipping insertion."}, {"type": "DELETE", "lineNumber": 48, "oldContent": "Table 'chanting' already exists."}, {"type": "DELETE", "lineNumber": 49, "oldContent": "Role \"user\" already exists, skipping insertion."}, {"type": "DELETE", "lineNumber": 50, "oldContent": "Table 'discount_options' already exists."}, {"type": "DELETE", "lineNumber": 51, "oldContent": "Role \"guest\" already exists, skipping insertion."}, {"type": "DELETE", "lineNumber": 52, "oldContent": "Table 'lectures' already exists."}, {"type": "DELETE", "lineNumber": 53, "oldContent": "Table 'permissions' already exists."}, {"type": "DELETE", "lineNumber": 54, "oldContent": "Table 'meditation' already exists."}, {"type": "DELETE", "lineNumber": 55, "oldContent": "Table 'role_permissions' already exists."}, {"type": "DELETE", "lineNumber": 56, "oldContent": "Table 'distraction' already exists."}, {"type": "DELETE", "lineNumber": 57, "oldContent": "Table 'clinics' already exists."}, {"type": "DELETE", "lineNumber": 58, "oldContent": "Table 'answers' already exists."}, {"type": "DELETE", "lineNumber": 59, "oldContent": "Table 'users' already exists."}, {"type": "DELETE", "lineNumber": 60, "oldContent": "Table 'questions' already exists."}, {"type": "DELETE", "lineNumber": 61, "oldContent": "Table 'user_roles' already exists."}, {"type": "DELETE", "lineNumber": 62, "oldContent": "Table 'audio_clips' already exists."}, {"type": "DELETE", "lineNumber": 63, "oldContent": "Table 'tokens' already exists."}, {"type": "DELETE", "lineNumber": 64, "oldContent": "Table 'thoughts' already exists."}, {"type": "DELETE", "lineNumber": 65, "oldContent": "Table 'books' already exists."}, {"type": "DELETE", "lineNumber": 66, "oldContent": "Table 'concerns' already exists."}, {"type": "DELETE", "lineNumber": 67, "oldContent": "Table 'pages' already exists."}, {"type": "DELETE", "lineNumber": 68, "oldContent": "Table 'topic_users' already exists."}, {"type": "DELETE", "lineNumber": 69, "oldContent": "Table 'videos' already exists."}, {"type": "DELETE", "lineNumber": 70, "oldContent": "Table 'topics' already exists."}, {"type": "INSERT", "lineNumber": 11, "content": ""}]}, {"timestamp": 1758008897362, "changes": [{"type": "MODIFY", "lineNumber": 0, "content": "xrgouda@Amrs-MacBook-Air ArabCBT_back % mysql ", "oldContent": "xrgouda@Amrs-MacBook-Air ArabCBT_back % "}]}, {"timestamp": 1758008902310, "changes": [{"type": "MODIFY", "lineNumber": 0, "content": "xrgouda@Amrs-MacBook-Air ArabCBT_back % mysql _", "oldContent": "xrgouda@Amrs-MacBook-Air ArabCBT_back % mysql "}]}, {"timestamp": 1758008905995, "changes": [{"type": "MODIFY", "lineNumber": 0, "content": "xrgouda@Amrs-MacBook-Air ArabCBT_back % mysql -u", "oldContent": "xrgouda@Amrs-MacBook-Air ArabCBT_back % mysql _"}]}, {"timestamp": 1758008910612, "changes": [{"type": "MODIFY", "lineNumber": 0, "content": "xrgouda@Amrs-MacBook-Air ArabCBT_back % mysql -u root ", "oldContent": "xrgouda@Amrs-MacBook-Air ArabCBT_back % mysql -u"}]}, {"timestamp": 1758008916742, "changes": [{"type": "MODIFY", "lineNumber": 0, "content": "xrgouda@Amrs-MacBook-Air ArabCBT_back % mysql -u root -b", "oldContent": "xrgouda@Amrs-MacBook-Air ArabCBT_back % mysql -u root "}]}, {"timestamp": 1758008919322, "changes": [{"type": "MODIFY", "lineNumber": 0, "content": "xrgouda@Amrs-MacBook-Air ArabCBT_back % mysql -u root -p", "oldContent": "xrgouda@Amrs-MacBook-Air ArabCBT_back % mysql -u root -b"}]}, {"timestamp": 1758008922289, "changes": [{"type": "INSERT", "lineNumber": 1, "content": "zsh: command not found: mysql"}, {"type": "INSERT", "lineNumber": 2, "content": "xrgouda@Amrs-MacBook-Air ArabCBT_back % "}, {"type": "DELETE", "lineNumber": 10, "oldContent": ""}, {"type": "DELETE", "lineNumber": 11, "oldContent": ""}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/Auto Star/ArabCBT_back/package.json": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/Auto Star/ArabCBT_back/package.json", "baseContent": "{\n  \"name\": \"backend\",\n  \"version\": \"1.0.0\",\n  \"main\": \"index.js\",\n  \"scripts\": {\n    \"dev\": \"nodemon index.js\",\n    \"start\": \"pm2 start index.js --name ArabCBT\"\n  },\n  \"keywords\": [],\n  \"author\": \"\",\n  \"license\": \"ISC\",\n  \"description\": \"\",\n  \"dependencies\": {\n    \"@paypal/checkout-server-sdk\": \"^1.0.3\",\n    \"bcryptjs\": \"^2.4.3\",\n    \"child_process\": \"^1.0.2\",\n    \"compression\": \"^1.7.5\",\n    \"cors\": \"^2.8.5\",\n    \"dotenv\": \"^16.4.5\",\n    \"express\": \"^4.21.0\",\n    \"express-session\": \"^1.18.0\",\n    \"express-validator\": \"^7.2.0\",\n    \"firebase-admin\": \"^13.2.0\",\n    \"fluent-ffmpeg\": \"^2.1.3\",\n    \"helmet\": \"^8.0.0\",\n    \"http\": \"^0.0.1-security\",\n    \"joi\": \"^17.13.3\",\n    \"jsonwebtoken\": \"^9.0.2\",\n    \"multer\": \"^1.4.5-lts.1\",\n    \"mysql2\": \"^3.11.3\",\n    \"node-cron\": \"^3.0.3\",\n    \"nodemailer\": \"^6.9.15\",\n    \"nodemon\": \"^3.1.4\",\n    \"passport\": \"^0.7.0\",\n    \"passport-google-oauth20\": \"^2.0.0\",\n    \"path\": \"^0.12.7\",\n    \"sequelize\": \"^6.37.5\",\n    \"sharp\": \"^0.33.5\",\n    \"uuid\": \"^11.0.5\",\n    \"winston\": \"^3.17.0\"\n  }\n}\n", "baseTimestamp": 1757934170514}}}