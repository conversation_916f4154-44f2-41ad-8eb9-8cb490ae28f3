{"snapshots": {"/terminal_output": {"filePath": "/terminal_output", "baseContent": "xrgouda@Amrs-MacBook-Air ArabCBT_back % npm i\nnpm warn deprecated @paypal/checkout-server-sdk@1.0.3: Package no longer supported. The author suggests using the @paypal/paypal-server-sdk package instead: https://www.npmjs.com/package/@paypal/paypal-server-sdk. Contact Support at https://www.npmjs.com/support for more info.\n\nadded 384 packages, and audited 385 packages in 2m\n\n41 packages are looking for funding\n  run `npm fund` for details\n\n5 vulnerabilities (4 low, 1 critical)\n\nTo address all issues, run:\n  npm audit fix\n\nRun `npm audit` for details.\nxrgouda@Amrs-MacBook-Air ArabCBT_back % ", "baseTimestamp": 1757934161456, "deltas": [{"timestamp": 1757934179102, "changes": [{"type": "INSERT", "lineNumber": 14, "content": "xrgouda@Amrs-MacBook-Air ArabCBT_back % npm run develop"}, {"type": "INSERT", "lineNumber": 15, "content": "npm error Missing script: \"develop\""}, {"type": "INSERT", "lineNumber": 16, "content": "npm error"}, {"type": "INSERT", "lineNumber": 17, "content": "npm error To see a list of scripts, run:"}, {"type": "INSERT", "lineNumber": 18, "content": "npm error   npm run"}, {"type": "INSERT", "lineNumber": 19, "content": "npm error A complete log of this run can be found in: /Users/<USER>/.npm/_logs/2025-09-15T11_02_56_989Z-debug-0.log"}]}]}, "/Users/<USER>/Flutter-Projects/Ajory/Auto Star/ArabCBT_back/package.json": {"filePath": "/Users/<USER>/Flutter-Projects/Ajory/Auto Star/ArabCBT_back/package.json", "baseContent": "{\n  \"name\": \"backend\",\n  \"version\": \"1.0.0\",\n  \"main\": \"index.js\",\n  \"scripts\": {\n    \"dev\": \"nodemon index.js\",\n    \"start\": \"pm2 start index.js --name ArabCBT\"\n  },\n  \"keywords\": [],\n  \"author\": \"\",\n  \"license\": \"ISC\",\n  \"description\": \"\",\n  \"dependencies\": {\n    \"@paypal/checkout-server-sdk\": \"^1.0.3\",\n    \"bcryptjs\": \"^2.4.3\",\n    \"child_process\": \"^1.0.2\",\n    \"compression\": \"^1.7.5\",\n    \"cors\": \"^2.8.5\",\n    \"dotenv\": \"^16.4.5\",\n    \"express\": \"^4.21.0\",\n    \"express-session\": \"^1.18.0\",\n    \"express-validator\": \"^7.2.0\",\n    \"firebase-admin\": \"^13.2.0\",\n    \"fluent-ffmpeg\": \"^2.1.3\",\n    \"helmet\": \"^8.0.0\",\n    \"http\": \"^0.0.1-security\",\n    \"joi\": \"^17.13.3\",\n    \"jsonwebtoken\": \"^9.0.2\",\n    \"multer\": \"^1.4.5-lts.1\",\n    \"mysql2\": \"^3.11.3\",\n    \"node-cron\": \"^3.0.3\",\n    \"nodemailer\": \"^6.9.15\",\n    \"nodemon\": \"^3.1.4\",\n    \"passport\": \"^0.7.0\",\n    \"passport-google-oauth20\": \"^2.0.0\",\n    \"path\": \"^0.12.7\",\n    \"sequelize\": \"^6.37.5\",\n    \"sharp\": \"^0.33.5\",\n    \"uuid\": \"^11.0.5\",\n    \"winston\": \"^3.17.0\"\n  }\n}\n", "baseTimestamp": 1757934170514}}}