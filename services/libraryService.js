const sequelize = require("../config/sequelize");
const LibraryModel = require("../models/libraryModel");

async function create(req) {
  const { body } = req;

  const data = await LibraryModel.create(body);
  return data;
}

async function update(req) {
  const { body } = req;
  const { id } = req.params;

  const checkLib = await LibraryModel.findOne({
    where: {
      id,
    },
  });

  if (!checkLib) {
    throw new Error("User library not found");
  }

  const data = await checkLib.update(body);

  return data;
}

async function getUserLibrary(req) {
  const { userId } = req.params;

  const [data, metadata] = await sequelize.query(
    `
		SELECT 
				*,
				CASE 
					WHEN lib.type = 'books' THEN b.name
					
					ELSE NULL
				END AS library_item_title
		 FROM library AS lib
		 LEFT JOIN books AS b ON lib.libraryId = b.id AND lib.type = 'books'
		 
		 WHERE lib.userId = :userId;
		`,
    {
      replacements: { userId },
      type: sequelize.QueryTypes.SELECT,
    }
  );

  return data;
}

module.exports = { create, update, getUserLibrary };
/**
 * SELECT 
				lib.*, 
				v.*,
				b.*,
				CASE 
					WHEN lib.type = 'books' THEN b.name
					WHEN lib.type = 'videos' THEN v.text
					ELSE NULL
				END AS library_item_title
		 FROM library AS lib
		 LEFT JOIN books AS b ON lib.libraryId = b.id AND lib.type = 'books'
		 LEFT JOIN videos AS v ON lib.libraryId = v.id AND lib.type = 'videos'
		 WHERE lib.userId = :userId;
 */
