const FavoriteModel = require("../models/favoriteModel");

async function create(req) {
  const { body } = req;

  const data = await FavoriteModel.create(body);
  return data;
}

async function update(req) {
  const { body } = req;
  const { id } = req.params;

  const checkLib = await FavoriteModel.findOne({
    where: {
      id,
    },
  });

  if (!checkLib) {
    throw new Error("User library not found");
  }

  const data = await checkLib.update(body);

  return data;
}

async function userFavorite(req) {
  const { userId } = req.params;
  const data = FavoriteModel.findAll({
    where: {
      userId,
    },
  });

  return data;
}

module.exports = { create, update, userFavorite };
