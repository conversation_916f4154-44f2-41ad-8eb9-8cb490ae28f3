const { QueryTypes } = require("sequelize");
const sequelize = require("../config/sequelize");
const { sendNotification } = require("./firebaseService");

async function getUserTopics() {
  try {
    const limit = 100;

    const countUsers = await sequelize.query(
      `SELECT COUNT(*) AS count FROM users`,
      {
        type: QueryTypes.SELECT,
      }
    );
    const count = isFinite(countUsers[0]?.count) ? countUsers[0]?.count : 0;
    const totalPages = Math.ceil(count / limit);

    for (let page = 1; page <= totalPages; page++) {
      try {
        const offset = (page - 1) * limit;
        const query = `
	  SELECT 
	  sub.id,
	  sub.fcmToken, 
	  GROUP_CONCAT(sub.topic_name SEPARATOR ', ') AS topics
	FROM (
	  SELECT 
		u.id,
		u.fcmToken,
		t.name AS topic_name,
		ROW_NUMBER() OVER (PARTITION BY u.id ORDER BY t.id) AS rn
	  FROM users AS u
	  JOIN topic_users ut ON u.id = ut.user_id
	  JOIN topics t ON t.id = ut.topic_id
	  WHERE ut.is_send = 0
	) AS sub
	WHERE sub.rn <= 3
	GROUP BY sub.id, sub.fcmToken
	LIMIT :limit OFFSET :offset;
  `;

        const results = await sequelize.query(query, {
          replacements: { limit: Number(limit), offset: Number(offset) },
          type: QueryTypes.SELECT,
        });

        for (let index = 0; index < results.length; index++) {
          const user = results[index];
          if (!user || !user.fcmToken) {
            continue;
          }

          await sendNotification(user.topics, user.fcmToken);
        }
      } catch (error) {
        console.log(error.message);
      }
    }
  } catch (error) {
    console.log(error.message);
  }
}

module.exports = { getUserTopics };
