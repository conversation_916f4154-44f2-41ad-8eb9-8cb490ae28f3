const admin = require("firebase-admin");
const path = require("path");

admin.initializeApp({
  credential: admin.credential.cert(
    path.join(process.cwd(), "arabcbt_firebase.json")
  ),
});

async function sendNotification(payload, token) {
  try {
    const message = {
      notification: {
        title: "Hello from Firebase!",
        body: payload,
      },
      token,
    };

    const res = await admin.messaging().send(message);
    console.log(res);
  } catch (error) {
    console.log(error.message);
  }
}

module.exports = { sendNotification };
