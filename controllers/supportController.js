const nodemailer = require('nodemailer');

exports.sendSupportMessage = async (req, res) => {
  const { email, message } = req.body;

  if (!email || !message) {
    return res.status(400).json({ error: 'Email and message are required.' });
  }

  try {
    // Set up nodemailer transporter
    const transporter = nodemailer.createTransport({
      service: 'gmail', // Or use your preferred email service
      auth: {
        user: process.env.EMAIL_USER, // Replace with your email
        pass: process.env.EMAIL_PASS, // Replace with your email password or app password
      },
    });

    // Verify connection configuration
    transporter.verify((error, success) => {
      if (error) {
        console.error("Error verifying transporter:", error);
      } else {
        console.log("Server is ready to take our messages:", success);
      }
    });

    // Mail options
    const mailOptions = {
      from: email, // The sender's email address
      to: process.env.EMAIL_USER, // The support developer's email address
      subject: 'Support Request',
      text: message,
    };

    // Send email
    await transporter.sendMail(mailOptions);

    res.status(200).json({ message: 'Support message sent successfully!' });
  } catch (error) {
    console.error('Error sending email:', error.message, error.response);
    res.status(500).json({ error: 'Failed to send email.' });
  }
};
