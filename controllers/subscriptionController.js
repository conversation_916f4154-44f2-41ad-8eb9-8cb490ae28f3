const SubscriptionPackageModel = require("../models/SubscriptionPackageModel");
const SubscriptionPlanModel = require("../models/SubscriptionPlanModel");
const SubscriptionFeatureModel = require("../models/SubscriptionFeatureModel");


exports.getAllPackagesWithDetails = async (req, res) => {
    try {
        const packages = await SubscriptionPackageModel.getAllPackages();

        const detailedPackages = await Promise.all(
            packages.map(async (pkg) => {
                const features = await SubscriptionFeatureModel.getFeaturesByPackageId(pkg.id);

                return {
                    ...pkg,
                    features,
                };
            })
        );
        res.json({ success: true, packages: detailedPackages });
    } catch (error) {
        console.error("Failed to fetch packages:", error.message);
        res.status(500).json({ error: "Failed to fetch subscription packages" });
    }
};

exports.getAllPackagesPlans = async (req, res) => {
    try {
        const plans = await SubscriptionPlanModel.getAllPlans();
        res.json({ success: true, plans });
    } catch (error) {
        console.error("Error fetching packages and plans:", error.message);
        res.status(500).json({ error: "Failed to fetch packages and plans" });
    }
};

exports.getActiveSubscription = async (req, res) => {
    const { user_id } = req.query;

    if (!user_id) {
        return res.status(400).json({ error: "user_id is required" });
    }

    try {
        const subscription = await UserSubscriptionModel.getUserActiveSubscription(user_id);

        if (!subscription) {
            return res.json({ has_active_subscription: false });
        }

        const packageDetails = await SubscriptionPackageModel.getById(subscription.package_id);
        const features = await SubscriptionFeatureModel.getFeaturesByPackageId(subscription.package_id);

        return res.json({
            has_active_subscription: true,
            subscription: {
                ...subscription,
                package: {
                    ...packageDetails,
                    features
                }
            }
        });
    } catch (error) {
        console.error("Error fetching active subscription:", error.message);
        res.status(500).json({ error: "Failed to fetch active subscription" });
    }
};


exports.checkUserServicePurchase = async (req, res) => {
    const { user_id, service, service_id } = req.body;

    if (!user_id || !service || !service_id) {
        return res
            .status(400)
            .json({ error: "user_id, service, and service_id are required" });
    }

    try {
        let rows;

        if (service === "package") {
            // Check active subscription
            [rows] = await pool.query(
                `
        SELECT price_paid
        FROM user_subscriptions
        WHERE user_id = ?
          AND subscription_type = 'package'
          AND reference_id = ?
          AND is_active = 1
          AND end_date >= CURDATE()
        `,
                [user_id, service_id]
            );
        } else if (service === "item") {
            // Check active subscription to item
            [rows] = await pool.query(
                `
        SELECT price_paid
        FROM user_subscriptions
        WHERE user_id = ?
          AND subscription_type = 'item'
          AND reference_id = ?
          AND is_active = 1
          AND end_date >= CURDATE()
        `,
                [user_id, service_id]
            );
        } else if (service === "book") {
            // Example for book purchases table
            [rows] = await pool.query(
                `
        SELECT price_paid
        FROM book_purchases
        WHERE user_id = ?
          AND book_id = ?
        `,
                [user_id, service_id]
            );
        } else {
            return res.status(400).json({ error: "Unknown service type" });
        }

        if (rows.length > 0) {
            return res.json({
                success: true,
                purchased: true,
                price_paid: rows[0].price_paid,
            });
        } else {
            return res.json({
                success: true,
                purchased: false,
            });
        }
    } catch (error) {
        console.error("Error checking purchase:", error.message);
        res.status(500).json({ error: "Failed to check purchase" });
    }
};

// subscription.controller.js
exports.checkUserSubscription = async (req, res) => {
    const { user_id } = req.query;

    if (!user_id) {
        return res.status(400).json({ error: "user_id is required" });
    }

    try {
        const [rows] = await pool.query(
            `
      SELECT *
      FROM user_subscriptions
      WHERE user_id = ?
        AND is_active = 1
        AND end_date >= CURDATE()
      `,
            [user_id]
        );

        if (rows.length > 0) {
            return res.json({ active: true, subscription: rows[0] });
        } else {
            return res.json({ active: false });
        }
    } catch (error) {
        console.error("Error checking subscription:", error.message);
        res.status(500).json({ error: "Failed to check subscription" });
    }
};


const UserSubscriptionModel = require("../models/UserSubscriptionModel");

exports.getPackageById = async (req, res) => {
    const { id } = req.params;
    try {
        const package = await SubscriptionPackageModel.getById(id);
        res.json({ success: true, package });
    } catch (error) {
        console.error("Error fetching package:", error.message);
        res.status(500).json({ error: "Failed to fetch package" });
    }
};

exports.subscribeToPackage = async (req, res) => {
    const {
        user_id,
        reference_id,           // ID of package/item
        subscription_type,      // 'package' or 'item'
        plan_type,              // 'monthly', 'yearly', 'one_time'
        price_paid,
        currency = "USD",
        payment_method_id,
        payment_reference
    } = req.body;

    if (
        !user_id ||
        !reference_id ||
        !subscription_type ||
        !plan_type ||
        !price_paid ||
        !payment_method_id ||
        !payment_reference
    ) {
        return res.status(400).json({ error: "Missing required fields" });
    }

    const validTypes = ["package", "item"];
    const validPlans = ["monthly", "yearly", "one_time"];

    if (!validTypes.includes(subscription_type)) {
        return res.status(400).json({ error: "Invalid subscription_type. Must be 'package' or 'item'." });
    }

    if (!validPlans.includes(plan_type)) {
        return res.status(400).json({ error: "Invalid plan_type. Must be 'monthly', 'yearly', or 'one_time'." });
    }

    const start_date = new Date();
    let end_date = null;

    if (plan_type === "monthly") {
        end_date = new Date(start_date);
        end_date.setMonth(end_date.getMonth() + 1);
    } else if (plan_type === "yearly") {
        end_date = new Date(start_date);
        end_date.setFullYear(end_date.getFullYear() + 1);
    }

    try {
        // ✅ Remove old subscriptions
        await UserSubscriptionModel.deleteUserSubscriptions(user_id);

        // ✅ Create new subscription
        await UserSubscriptionModel.createSubscription({
            user_id,
            subscription_type,
            reference_id,
            plan_type,
            start_date: start_date.toISOString().split('T')[0],
            end_date: end_date ? end_date.toISOString().split('T')[0] : null,
            price_paid,
            currency,
            payment_method_id,
            payment_reference
        });

        // 📥 Fetch new subscription
        const subscription = await UserSubscriptionModel.getLatestSubscription(user_id);

        return res.status(200).json({
            success: true,
            message: "User subscribed successfully",
            data: subscription || null
        });

    } catch (error) {
        console.error("❌ Subscription error:", error);
        return res.status(500).json({ error: "Failed to subscribe user" });
    }
};

exports.createPackage = async (req, res) => {
    const { name, monthly_price, yearly_price } = req.body;

    if (!name || !monthly_price || !yearly_price) {
        return res.status(400).json({ error: "All fields are required" });
    }

    try {
        const result = await SubscriptionPackageModel.createPackage({
            name,
            monthly_price,
            yearly_price,
            is_active: true, // افتراضيًا مفعلة
        });

        res.status(201).json({ success: true, package_id: result.insertId, message: "Package created successfully" });
    } catch (error) {
        console.error("Failed to create package:", error.message);
        res.status(500).json({ error: "Failed to create package" });
    }
};



exports.updatePackage = async (req, res) => {
    const { id } = req.params;
    const { name, monthly_price, yearly_price } = req.body;

    if (!name || !monthly_price || !yearly_price) {
        return res.status(400).json({ error: "All fields are required" });
    }

    try {
        const result = await SubscriptionPackageModel.updatePackage(id, {
            name,
            monthly_price,
            yearly_price
        });

        res.json({ success: true, message: "Package updated successfully", result });
    } catch (error) {
        console.error("Failed to update package:", error.message);
        res.status(500).json({ error: "Failed to update package" });
    }
};

// POST /user-subscriptions/has-purchased
exports.hasUserPurchased = async (req, res) => {
    const { user_id, package_id } = req.body;

    if (!user_id || !package_id) {
        return res.status(400).json({ error: "Missing user_id or package_id" });
    }

    try {
        const subscription = await UserSubscriptionModel.getUserSubscription(user_id, package_id);
        if (!subscription) {
            return res.json({ purchased: false });
        }
        return res.json({ purchased: true, price_paid: subscription.price_paid });
    } catch (error) {
        console.error("Purchase check error:", error.message);
        res.status(500).json({ error: "Failed to check purchase" });
    }
};




exports.togglePackageStatus = async (req, res) => {
    const { id } = req.params;
    const { is_active } = req.body;

    if (typeof is_active === 'undefined') {
        return res.status(400).json({ error: "Missing is_active field" });
    }

    try {
        const result = await SubscriptionPackageModel.toggleStatus(id, is_active);
        res.json({ success: true, message: "Package status updated", result });
    } catch (error) {
        console.error("Failed to update package status:", error.message);
        res.status(500).json({ error: "Failed to update package status" });
    }
};


// ✅ عرض جميع الميزات الخاصة بباقة
exports.getFeaturesByPackageId = async (req, res) => {
    const { package_id } = req.params;
    try {
        const features = await SubscriptionFeatureModel.getFeaturesByPackageId(package_id);
        res.json({ success: true, features });
    } catch (error) {
        console.error("Error fetching features:", error.message);
        res.status(500).json({ error: "Failed to fetch features" });
    }
};

// ✅ إضافة ميزة لباقة معينة
exports.addFeatureToPackage = async (req, res) => {
    const { package_id } = req.params;
    const { feature_id } = req.body;

    if (!package_id || !feature_id) {
        return res.status(400).json({ error: 'package_id and feature_id are required' });
    }

    try {
        await SubscriptionFeatureModel.addFeatureToPackage(package_id, feature_id);
        res.json({ success: true, message: "Feature added" });
    } catch (error) {
        console.error("Error adding feature:", error.message);
        res.status(500).json({ error: "Failed to add feature" });
    }
};


exports.updateFeature = async (req, res) => {
    const { id } = req.params;
    const { feature_id } = req.body;

    if (!feature_id) {
        return res.status(400).json({ error: 'feature_id is required' });
    }

    try {
        const result = await SubscriptionFeatureModel.updateFeature(id, feature_id);
        if (result.affectedRows === 0) {
            return res.status(404).json({ error: "Feature not found" });
        }

        res.status(200).json({ success: true, message: "Feature updated" });
    } catch (err) {
        console.error("Error updating feature:", err.message);
        res.status(500).json({ error: "Failed to update feature" });
    }
};




// ✅ حذف الميزة
exports.deleteFeature = async (req, res) => {
    const { id } = req.params;
    try {
        await SubscriptionFeatureModel.deleteFeature(id);
        res.json({ success: true, message: "Feature deleted" });
    } catch (error) {
        console.error("Error deleting feature:", error.message);
        res.status(500).json({ error: "Failed to delete feature" });
    }
};

// ✅ تفعيل أو تعطيل الميزة
exports.toggleFeatureStatus = async (req, res) => {
    const { id } = req.params;
    const { is_active } = req.body;

    if (typeof is_active === "undefined") {
        return res.status(400).json({ error: 'is_active is required' });
    }

    try {
        await SubscriptionFeatureModel.toggleFeatureStatus(id, is_active);
        res.json({ success: true, message: "Feature status updated" });
    } catch (error) {
        console.error("Error toggling feature status:", error.message);
        res.status(500).json({ error: "Failed to update feature status" });
    }
};

