const ComparisonHistoryModel = require("../models/comparison_history");

// GET /comparison-history
exports.getAllComparisonHistoryByUserId = async (req, res) => {
  try {
    const tokenUserId = req.user.id;         // from JWT token
    const { userId } = req.params;           // from URL

    if (parseInt(userId) !== tokenUserId) {
      return res.status(403).json({
        success: false,
        message: "Forbidden: You can only access your own comparison history",
      });
    }

    const comparisonHistory = await ComparisonHistoryModel.getAllComparisonHistoryByUserId(userId);

    res.status(200).json({
      success: true,
      data: comparisonHistory,
    });
  } catch (error) {
    console.error("Error fetching comparison history:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch comparison history",
    });
  }
};

// POST /comparison-history/:thoughtId
exports.createComparisonHistoryAudio = async (req, res) => {
  try {
    const userId = req.user.id; // מהטוקן
    const { thoughtId } = req.params;
    const { before_score } = req.body;

    if (before_score === undefined || before_score === null) {
      return res.status(400).json({
        success: false,
        message: "before_score is required",
      });
    }

    const result = await ComparisonHistoryModel.createComparisonHistoryAudioByUserId(
      userId,
      thoughtId,
      { before_score }
    );

    res.status(201).json({
      success: true,
      message: "History created successfully",
      data: { id: result.insertId },
    });
  } catch (error) {
    console.error("Error creating comparison history:", error);
    res.status(500).json({
      success: false,
      message: "Failed to create history",
    });
  }
};

// PUT /comparison-history/:thoughtId/after-score
exports.updateComparisonHistoryById = async (req, res) => {
  try {
    const userId = req.user.id;
    const { thoughtId } = req.params;
    const { after_score } = req.body;

    if (after_score === undefined || after_score === null) {
      return res.status(400).json({
        success: false,
        message: "after_score is required",
      });
    }

    const result = await ComparisonHistoryModel.updateAfterScoreById(userId, thoughtId, after_score);

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: "No matching entry found to update today",
      });
    }

    res.status(200).json({
      success: true,
      message: "after_score updated successfully",
    });
  } catch (error) {
    console.error("Error updating after_score:", error);
    res.status(500).json({
      success: false,
      message: "Failed to update after_score",
    });
  }
};

// PUT /comparison-history/:thoughtId/after-score-audio
exports.updateComparisonHistoryAudioById = async (req, res) => {
  try {
    const userId = req.user.id;
    const { thoughtId } = req.params;
    const { after_score_audio } = req.body;

    if (after_score_audio === undefined || after_score_audio === null) {
      return res.status(400).json({
        success: false,
        message: "after_score_audio is required",
      });
    }

    const result = await ComparisonHistoryModel.updateAfterScoreAudioById(userId, thoughtId, after_score_audio);

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: "No matching entry found to update today",
      });
    }

    res.status(200).json({
      success: true,
      message: "after_score_audio updated successfully",
    });
  } catch (error) {
    console.error("Error updating after_score_audio:", error);
    res.status(500).json({
      success: false,
      message: "Failed to update after_score_audio",
    });
  }
};