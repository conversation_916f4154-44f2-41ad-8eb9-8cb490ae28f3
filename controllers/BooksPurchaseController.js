const pool = require("../config/db");
const BooksPurchaseModel = require("../models/BooksPurchaseModel");
const UserSubscriptionModel = require("../models/UserSubscriptionModel");
const SubscriptionPackageModel = require("../models/SubscriptionPackageModel");
const SubscriptionFeatureModel = require("../models/SubscriptionFeatureModel");
const BooksModel = require("../models/booksModel");



exports.purchaseBook = async (req, res) => {
    const { user_id, book_id } = req.body;

    if (!user_id || !book_id) {
        return res.status(400).json({ error: "user_id and book_id are required" });
    }

    const [bookRows] = await pool.query("SELECT price FROM books WHERE id = ?", [book_id]);
    if (!bookRows.length) return res.status(404).json({ error: "Book not found" });

    const price = bookRows[0].price;

    const alreadyPurchased = await BooksPurchaseModel.isPurchased(user_id, book_id);
    if (alreadyPurchased) {
        return res.status(400).json({ error: "Book already purchased" });
    }

    await BooksPurchaseModel.purchaseBook(user_id, book_id, price);
    res.json({ success: true, message: "Book purchased", price });
};

exports.getBookWithPurchaseStatus = async (req, res) => {
    const { id } = req.params;
    const user_id = req.user?.id; // ✅ من التوكن

    const [bookRows] = await pool.query("SELECT * FROM books WHERE id = ?", [id]);
    if (!bookRows.length) return res.status(404).json({ error: "Book not found" });

    const book = bookRows[0];
    const result = {
        id: book.id,
        name: book.name,
        price: book.price,
        image: book.image
    };

    if (user_id) {
        const [purchase] = await pool.query(
            "SELECT id FROM books_purchases WHERE user_id = ? AND book_id = ?",
            [user_id, id]
        );
        result.purchased = purchase.length > 0;
    }

    res.json(result);
};

exports.getBookWithDiscount = async (req, res) => {
    const { id } = req.params;
    const user_id = req.user?.id;

    try {
        const book = await BooksModel.getBookById(id);
        if (!book) return res.status(404).json({ error: "Book not found" });

        // تحقق إذا تم شراؤه
        const [purchased] = await pool.query(
            "SELECT id FROM books_purchases WHERE user_id = ? AND book_id = ?",
            [user_id, id]
        );

        let discount = 0;
        let discounted_price = book.price;

        const subscription = await UserSubscriptionModel.getUserActiveSubscription(user_id);

        if (subscription) {
            const pkg = await SubscriptionPackageModel.getById(subscription.package_id);

            // لو عندك جدول ميزات يحتوي الخصم لكل ميزة
            discount = await SubscriptionFeatureModel.getDiscountValue(pkg.id, 'books_discount');
            discounted_price = parseFloat((book.price * (1 - discount / 100)).toFixed(2));
        }

        res.json({
            id: book.id,
            name: book.name,
            price: book.price,
            image: book.image,
            discount: {
                percentage: discount,
                final_price: discounted_price
            },
            purchased: purchased.length > 0
        });

    } catch (error) {
        console.error("Error in getBookWithDiscount:", error.message);
        res.status(500).json({ error: "Failed to fetch book with discount" });
    }
};

exports.getUserPurchaseHistory = async (req, res) => {
    const user_id = req.user?.id;

    if (!user_id) {
        return res.status(401).json({ error: "Unauthorized: user_id missing from token" });
    }

    try {
        const [rows] = await pool.query(`
            SELECT 
                bp.book_id,
                b.name AS book_name,
                bp.price,
                bp.created_at AS purchased_at
            FROM books_purchases bp
            JOIN books b ON bp.book_id = b.id
            WHERE bp.user_id = ?
            ORDER BY bp.created_at DESC
        `, [user_id]);

        res.status(200).json({ success: true, purchases: rows });
    } catch (error) {
        console.error("Error fetching purchase history:", error.message);
        res.status(500).json({ error: "Failed to fetch purchase history" });
    }
};

exports.purchaseBookWithDiscount = async (req, res) => {
    const { user_id, book_id } = req.body;

    if (!user_id || !book_id) {
        return res.status(400).json({ error: "user_id and book_id are required" });
    }

    try {
        const [books] = await pool.query("SELECT price FROM books WHERE id = ?", [book_id]);
        if (!books.length) return res.status(404).json({ error: "Book not found" });

        let price = books[0].price;

        const subscription = await UserSubscriptionModel.getUserActiveSubscription(user_id);
        let discount = 0;

        if (subscription) {
            const pkg = await SubscriptionPackageModel.getById(subscription.package_id);
            discount = await SubscriptionFeatureModel.getDiscountValue(pkg.id, 'books_discount');
            price = parseFloat((price * (1 - discount / 100)).toFixed(2));
        }

        const alreadyPurchased = await BooksPurchaseModel.isPurchased(user_id, book_id);
        if (alreadyPurchased) {
            return res.status(400).json({ error: "Book already purchased" });
        }

        await BooksPurchaseModel.purchaseBook(user_id, book_id, price);
        res.json({ success: true, price, discount, message: "Book purchased successfully" });
    } catch (err) {
        console.error("Error purchasing book with discount:", err.message);
        res.status(500).json({ error: "Purchase failed" });
    }
};


exports.getAllPurchasesForAdmin = async (req, res) => {
    try {
        const [rows] = await pool.query(`
            SELECT 
                bp.id AS purchase_id,
                bp.user_id,
                u.name AS full_name,
                u.email,
                bp.book_id,
                b.name AS book_name,
                bp.price,
                bp.created_at AS purchased_at
            FROM books_purchases bp
            JOIN books b ON bp.book_id = b.id
            JOIN users u ON bp.user_id = u.id
            ORDER BY bp.created_at DESC
        `);
        

        res.status(200).json({ success: true, purchases: rows });
    } catch (error) {
        console.error("Error fetching all purchases for admin:", error.message);
        res.status(500).json({ error: "Failed to fetch admin purchases" });
    }
};
