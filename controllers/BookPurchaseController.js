const BooksPurchaseModel = require("../models/BooksPurchaseModel");
const pool = require("../config/db");
exports.checkBookPurchase = async (req, res) => {
  const { user_id, book_id } = req.query;

  if (!user_id || !book_id) {
    return res.status(400).json({ error: "user_id and book_id are required" });
  }

  try {
    const purchased = await BooksPurchaseModel.isPurchased(user_id, book_id);
    res.json({ purchased });
  } catch (error) {
    console.error("Error checking purchase:", error.message);
    res.status(500).json({ error: "Failed to check purchase" });
  }
};

exports.purchaseBook = async (req, res) => {
  const { book_id } = req.body;
  const user_id = req.user?.id;

  if (!user_id || !book_id) {
    return res.status(400).json({ error: "user_id and book_id are required" });
  }

  try {
    const alreadyPurchased = await BooksPurchaseModel.isPurchased(user_id, book_id);
    if (alreadyPurchased) {
      return res.status(400).json({ error: "Book already purchased" });
    }

    const price = await BooksPurchaseModel.getBookPrice(book_id);
    if (!price) return res.status(404).json({ error: "Book not found" });

    await BooksPurchaseModel.purchaseBook(user_id, book_id, price);
    res.json({ success: true, message: "Book purchased", price });
  } catch (err) {
    console.error("Error purchasing book:", err.message);
    res.status(500).json({ error: "Purchase failed" });
  }
};


exports.getAdminPurchases = async (req, res) => {
  try {
    const query = `
      SELECT 
        bp.id AS purchase_id,
        bp.price AS price_paid,
        bp.purchased_at AS created_at,
        u.name AS full_name,
        u.email,
        b.name AS book_name
      FROM books_purchases bp
      JOIN users u ON bp.user_id = u.id
      JOIN books b ON bp.book_id = b.id
      ORDER BY bp.purchased_at DESC
    `;

    const [rows] = await pool.query(query);

    const formatted = rows.map((p) => ({
      full_name: p.full_name,
      email: p.email,
      book_name: p.book_name,
      price: p.price_paid,
      purchased_at: p.created_at,
    }));

    res.json({ purchases: formatted });
  } catch (error) {
    console.error("Failed to fetch purchases:", error);
    res.status(500).json({ error: "Failed to fetch purchases" });
  }
};

exports.getRevenueSummary = async (req, res) => {
  try {
    const daily = await BooksPurchaseModel.getDailyRevenue();
    const monthly = await BooksPurchaseModel.getMonthlyRevenue();
    const yearly = await BooksPurchaseModel.getYearlyRevenue();

    res.json({ daily, monthly, yearly });
  } catch (error) {
    console.error('Failed to fetch revenue summary:', error);
    res.status(500).json({ error: 'Failed to fetch revenue summary' });
  }
};