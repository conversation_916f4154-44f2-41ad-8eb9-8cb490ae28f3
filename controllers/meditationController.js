const fs = require('fs');
const path = require('path');
const ffmpeg = require('fluent-ffmpeg');
const meditationModel = require('../models/meditationModel');
const { toWebpAndStore, safeUnlink } = require('../helper/helperWebpToStore');


exports.createMeditation = async (req, res) => {
    try {
        const { title, description = '', type = 'category' } = req.body;
        if (!title || !title.trim()) {
            return res.status(400).json({ error: 'title is required' });
        }

        const mainFile = req.files?.image?.[0] ?? null;
        // FIX: read the right field name (secondry_image), accept alias image_secondry as fallback
        const secondaryFile =
            (req.files?.secondry_image && req.files.secondry_image[0]) ||
            (req.files?.image_secondry && req.files.image_secondry[0]) ||
            null;

        const imagePath = await toWebpAndStore(mainFile, 'meditation');
        const secondaryImagePath = await toWebpAndStore(secondaryFile, 'meditation');

        const normalizedType = String(type).toLowerCase().trim();
        const allowedTypes = new Set(['category', 'item', 'session']);
        const finalType = allowedTypes.has(normalizedType) ? normalizedType : 'category';

        // FIX: persist to DB column "image_secondry"
        const result = await meditationModel.createMeditation({
            title: title.trim(),
            description: description.trim(),
            image: imagePath,
            secondry_image: secondaryImagePath, // ✅ matches model & DB
            type: finalType,
        });

        return res.status(201).json({
            message: 'Meditation created successfully',
            id: result?.insertId ?? null,
            image: imagePath,
            image_secondry: secondaryImagePath, // FIX: consistent key in API
            type: finalType,
        });
    } catch (err) {
        console.error('Error creating meditation:', err);
        return res.status(500).json({ error: 'Failed to create meditation' });
    }
};

exports.createSubMeditation = async (req, res) => {
    try {
        const { title, meditation_id, description = null, type = "sub_category" } = req.body;

        if (!meditation_id || !title) {
            return res.status(400).json({ message: "Title and meditation_id are required." });
        }

        let audio_url = null;

        // ✅ Handle file conversion if audio uploaded
        if (req.file) {
            const inputFilePath = req.file.path;
            const uploadDir = path.join(__dirname, "../public/uploads/meditation");
            const outputFileName = `${Date.now()}-${Math.round(Math.random() * 1e9)}.webm`;
            const outputFilePath = path.join(uploadDir, outputFileName);

            // Ensure upload directory exists
            fs.mkdirSync(uploadDir, { recursive: true });

            await new Promise((resolve, reject) => {
                ffmpeg(inputFilePath)
                    .output(outputFilePath)
                    .audioCodec('libvorbis')
                    .format('webm')
                    .on('end', () => {
                        // Remove original file
                        try {
                            fs.unlinkSync(inputFilePath);
                        } catch (unlinkErr) {
                            console.warn("Warning: failed to delete original file", unlinkErr.message);
                        }
                        resolve();
                    })
                    .on('error', (err) => {
                        console.error("Error converting audio file:", err.message);
                        reject(err);
                    })
                    .run();
            });

            audio_url = `/uploads/meditation/${outputFileName}`;
        }

        // ✅ Create sub-meditation
        const createdId = await meditationModel.createSubMeditation({
            title,
            audio_url,
            type,
            meditation_id,
            description,
        });

        return res.status(201).json({
            message: "Sub-meditation created successfully",
            data: { id: createdId, title, audio_url, type, meditation_id, description },
        });

    } catch (error) {
        console.error("Error creating sub-meditation:", error.message);
        return res.status(500).json({ message: "Error creating sub-meditation", error: error.message });
    }
};


exports.getAllMeditation = async (req, res) => {
    try {
        const meditations = await meditationModel.getAllMeditation();
        res.status(200).json({ data: meditations });
    } catch (error) {
        console.error("Error retrieving meditations:", error);
        res.status(500).json({ message: "Error retrieving meditations", error: error.message });
    }
};


exports.getAllSubMeditation = async (req, res) => {
    try {
        const { meditationId } = req.params;

        if (!meditationId || isNaN(meditationId)) {
            return res.status(400).json({ message: "A valid Meditation ID is required." });
        }

        const subMeditations = await meditationModel.getAllSubMeditation(meditationId);

        return res.status(200).json({
            success: true,
            data: subMeditations,
            count: subMeditations.length
        });

    } catch (error) {
        console.error("Error retrieving sub-meditations:", error.message);
        return res.status(500).json({
            success: false,
            message: "Error retrieving sub-meditations",
            error: error.message
        });
    }
};


exports.getMeditationById = async (req, res) => {
    try {
        const { id } = req.params;

        if (!id) {
            return res.status(400).json({ message: "Meditation ID is required." });
        }

        const meditation = await meditationModel.getMeditationById(id);

        if (!meditation) {
            return res.status(404).json({ message: `Meditation with ID ${id} not found.` });
        }

        // Build the full response with sub_categories if available
        const response = {
            id: meditation.id,
            title: meditation.title,
            description: meditation.description,
            image: meditation.image,
            is_active: meditation.is_active,
            type: meditation.type,
            audio_url: meditation.audio_url || null,
            created_at: meditation.created_at,
            updated_at: meditation.updated_at,
        };

        // If there are sub_categories, include them
        if (Array.isArray(meditation.sub_categories)) {
            response.sub_categories = meditation.sub_categories.map(sub => ({
                id: sub.id,
                title: sub.title,
                description: sub.description,
                audio_url: sub.audio_url || null,
                created_at: sub.created_at,
                updated_at: sub.updated_at,
            }));
        }

        res.status(200).json({ data: response });

    } catch (error) {
        console.error("Error retrieving meditation by ID:", error.message);
        res.status(500).json({ message: "Error retrieving meditation", error: error.message });
    }
};

exports.getSubByMeditationId = async (req, res) => {
    try {
        const { id } = req.params;

        if (!id || isNaN(Number(id))) {
            return res.status(400).json({ message: "A valid sub-meditation ID is required." });
        }

        const meditation = await meditationModel.getSubMeditationById(Number(id));

        if (!meditation) {
            return res.status(404).json({ message: `Sub-meditation with ID ${id} not found.` });
        }

        return res.status(200).json({
            data: {
                id: meditation.id,
                title: meditation.title,
                description: meditation.description,
                audio_url: meditation.audio_url,
                is_active: meditation.is_active,
                meditation_id: meditation.meditation_id,
                type: meditation.type,
                created_at: meditation.created_at,
                updated_at: meditation.updated_at
            }
        });
    } catch (error) {
        console.error("Error retrieving sub-meditation by ID:", error.message);
        return res.status(500).json({
            message: "Internal server error while retrieving sub-meditation.",
            error: error.message
        });
    }
};


exports.updateMeditation = async (req, res) => {
    try {
        const { id } = req.params;
        const { title, description } = req.body;

        const existing = await meditationModel.getMeditationById(id);
        if (!existing) {
            return res.status(404).json({ message: 'Meditation not found.' });
        }

        // Files (accept both spellings for the secondary field)
        const primaryFile = req.files?.image?.[0] || null;
        const secondaryFile = req.files?.secondry_image?.[0] || req.files?.image_secondry?.[0] || null;

        // Convert only if a file was provided
        const newImagePath = primaryFile ? await toWebpAndStore(primaryFile, 'meditation') : null;
        const newSecondaryPath = secondaryFile ? await toWebpAndStore(secondaryFile, 'meditation') : null;

        // Resolve final values (keep old if not provided)
        const finalTitle = typeof title === 'string' ? title.trim() : existing.title;
        const finalDescription = typeof description === 'string' ? description.trim() : existing.description;
        const finalImage = newImagePath || existing.image || null;

        // existing may use secondry_image (your model) — fall back if someone earlier used image_secondry
        const existingSecondry = existing.secondry_image ?? existing.image_secondry ?? null;
        const finalSecondry = newSecondaryPath || existingSecondry || null;

        // Persist (match your model signature: secondry_image)
        await meditationModel.updateMeditation(id, {
            title: finalTitle,
            description: finalDescription,
            image: finalImage,
            secondry_image: finalSecondry,
        });

        // Cleanup old files only if replaced
        if (newImagePath && existing.image && existing.image !== newImagePath) {
            await safeUnlink(existing.image);
        }
        if (newSecondaryPath && existingSecondry && existingSecondry !== newSecondaryPath) {
            await safeUnlink(existingSecondry);
        }

        return res.status(200).json({
            message: 'Meditation updated successfully',
            id: Number(id),
            title: finalTitle,
            description: finalDescription,
            image: finalImage,
            secondry_image: finalSecondry, // <-- consistent API key
        });
    } catch (err) {
        console.error('Error updating meditation:', err);
        return res.status(500).json({ error: 'Failed to update meditation', details: err.message });
    }
};
exports.updateSubMeditation = async (req, res) => {
    try {
        const { id } = req.params;
        const { title, description } = req.body;

        const existingMeditation = await meditationModel.getSubMeditationById(id);
        if (!existingMeditation) {
            return res.status(404).json({ message: "Sub-meditation not found." });
        }

        let audioUrl = existingMeditation.audio_url;

        if (req.file) {
            const uploadDir = path.join(__dirname, "../public/uploads/meditation");
            const newFilename = `${Date.now()}-${Math.random().toString(36).substring(2)}.webm`;
            const newFilePath = path.join(uploadDir, newFilename);

            if (audioUrl) {
                const oldPath = path.join(__dirname, "../public", audioUrl);
                if (fs.existsSync(oldPath)) {
                    fs.unlinkSync(oldPath);
                }
            }

            await new Promise((resolve, reject) => {
                ffmpeg(req.file.path)
                    .audioCodec('libvorbis')
                    .format('webm')
                    .on('end', () => {
                        fs.unlinkSync(req.file.path);
                        resolve();
                    })
                    .on('error', reject)
                    .save(newFilePath);
            });

            audioUrl = `/uploads/meditation/${newFilename}`;
        }

        await meditationModel.updateSubMeditation(id, {
            title,
            audio_url: audioUrl
        });

        res.status(200).json({
            message: "Sub-meditation updated successfully",
            data: { id, title, description, audio_url: audioUrl }
        });
    } catch (err) {
        console.error("Error updating sub-meditation:", err.message);
        res.status(500).json({ error: "Failed to update sub-meditation", details: err.message });
    }
};





exports.deleteMeditation = async (req, res) => {
    try {
        const { id } = req.params;

        const meditation = await meditationModel.getMeditationById(id);
        if (!meditation) {
            return res.status(404).json({ message: 'Meditation not found' });
        }

        // Remove files (both images) using safeUnlink for correct pathing
        if (meditation.image) {
            await safeUnlink(meditation.image);
        }
        if (meditation.image_secondry) {
            await safeUnlink(meditation.image_secondry);
        }

        await meditationModel.deleteMeditation(id);
        res.status(200).json({ message: 'Meditation deleted successfully' });
    } catch (err) {
        console.error('Error deleting meditation:', err.message);
        res.status(500).json({ error: 'Failed to delete meditation' });
    }
};

exports.deleteSubMeditation = async (req, res) => {
    try {
        const { id } = req.params;

        // Fetch sub-meditation details
        const subMeditation = await meditationModel.getSubMeditationById(id);
        if (!subMeditation) {
            return res.status(404).json({ message: "Sub-meditation not found" });
        }

        // Remove file from the file system
        if (subMeditation.audio_url) {
            const filePath = path.join(__dirname, "../public", subMeditation.audio_url);
            if (fs.existsSync(filePath)) {
                fs.unlinkSync(filePath);
            }
        }

        // Delete from the database
        await meditationModel.deleteSubMeditation(id);

        res.status(200).json({ message: "Sub-meditation deleted successfully" });
    } catch (err) {
        console.error("Error deleting sub-meditation:", err.message);
        res.status(500).json({ error: "Failed to delete sub-meditation" });
    }
};


