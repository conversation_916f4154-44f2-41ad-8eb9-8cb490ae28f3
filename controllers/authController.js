const bcrypt = require('bcryptjs');
const TokenModel = require('../models/tokenModel');
const UserModel = require('../models/userModel');
const UserRoleModel = require('../models/userRoleModel');
const TopicModel = require('../models/topicModel');
const jwt = require('jsonwebtoken');
// const mailer = require('../utils/mailer');
const VideoPurchaseModel = require('../models/videoPurchaseModel');
const BooksPurchaseModel = require('../models/BooksPurchaseModel');
const UserSubscriptionModel = require("../models/UserSubscriptionModel");
require('dotenv').config();


// Register a new user
exports.registerUser = async (req, res) => {
    const { name, nickname, password, email, age, gender, phone, role = 3 } = req.body;

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        return res.status(400).json({ message: 'Invalid email format' });
    }

    // Validate phone number format (supports international format)
    const phoneRegex = /^\+?[0-9\s-]{7,15}$/;
    if (!phoneRegex.test(phone)) {
        return res.status(400).json({ message: 'Invalid phone number format' });
    }

    try {
        // Check if the email already exists
        const existingUser = await UserModel.getUserByEmail(email);
        if (existingUser) {
            return res.status(400).json({ message: 'Email already in use' });
        }

        // Hash the password and create a new user
        const hashedPassword = await bcrypt.hash(password, 10);
        const result = await UserModel.createUser(name, nickname, email, age, gender, phone, hashedPassword, role);
        const userId = result.insertId;

        // Assign role to the new user
        await UserRoleModel.assignRoleToUser(userId, role);

        // Generate and save token
        const token = TokenModel.generateToken(userId);
        await TokenModel.saveToken(userId, token);

        res.status(201).json({
            data: {
                id: userId,
                token,
                message: 'User created successfully'
            },
        });
    } catch (err) {
        console.error('Error registering user:', err);
        res.status(500).json({ message: 'Server error', details: err.message });
    }
};

// Login user

exports.loginUser = async (req, res) => {
    const emailRaw = req.body.email ?? "";
    const password = req.body.password ?? "";
    const email = emailRaw.trim().toLowerCase();

    try {
        // 1. Get user by email
        const user = await UserModel.getUserByEmail(email);
        if (!user) {
            return res.status(400).json({ error: "Invalid email or password" });
        }

        // 2. Check password
        const isMatch = await bcrypt.compare(password, user.password);
        if (!isMatch) {
            return res.status(400).json({ error: "Invalid email or password" });
        }

        // 3. Remove old tokens
        const oldToken = await TokenModel.getTokenByUserId(user.id);
        if (oldToken) {
            await TokenModel.deleteTokens(user.id);
        }

        // 4. Get roles
        const roleRow = await UserRoleModel.getUserRoleByUserId(user.id);
        const roleId = roleRow?.role_id ?? 2;

        const roleNames = await UserRoleModel.getRolesByUserId(user.id);
        const userRoles = roleNames.map((r) => r.name);

        if (roleId === 1 && !userRoles.includes("admin")) {
            userRoles.push("admin");
        }

        // 5. Generate token
        const token = jwt.sign({ id: user.id, role: roleId }, process.env.ACCESS_TOKEN_SECRET, { expiresIn: '1h' });
        await TokenModel.saveToken(user.id, token);

        // 6. Get clinic if admin
        const clinic = userRoles.includes("admin") ? await UserModel.getClinicByUserId(user.id) : null;

        // 7. Get topics
        const hasTopics = await UserModel.checkTopicExists(user.id);
        const topics = hasTopics ? await TopicModel.getUserTopics(user.id) : [];

        // 8. Get active subscription & features
        const subscription = await UserSubscriptionModel.getUserActiveSubscriptionWithPackage(user.id);
        let features = [];
        if (subscription) {
            features = await UserSubscriptionModel.getSubscriptionFeatures(subscription.id);
        }

        const purchasedBooks = await BooksPurchaseModel.getUserPurchasedBooks(user.id); // ✅ רשימת ספרים שרכש
        const purchasedVideos = await VideoPurchaseModel.getUserPurchasedVideos(user.id);
        // 9. Send response
        return res.status(200).json({
            data: {
                id: user.id,
                name: user.name,
                nickname: user.nickname,
                email: user.email,
                phone: user.phone,
                age: user.age,
                gender: user.gender,
                endDate: user.end_date,
                createdAt: user.created_at,
                roles: userRoles,
                topics,
                subscription: subscription
                    ? {
                        id: subscription.id,
                        packageId: subscription.package_id,
                        packageName: subscription.package_name,
                        planType: subscription.plan_type,
                        startDate: subscription.start_date,
                        endDate: subscription.end_date,
                        pricePaid: subscription.price_paid,
                        currency: subscription.currency,
                        isActive: subscription.is_active,
                        features,
                    }
                    : null,
                clinic: clinic
                    ? {
                        id: clinic.id,
                        name: clinic.clinic_name,
                        address: clinic.address_clinic,
                        phone: clinic.phone_clinic,
                        link_whatsapp: clinic.link_whatsapp,
                        link_telegram: clinic.link_telegram,
                        link_instagram: clinic.link_instagram,
                        link_youtube: clinic.link_youtube,
                        link_facebook: clinic.link_facebook,
                        link_twitter: clinic.link_twitter,
                        link_tiktok: clinic.link_tiktok,
                        email: clinic.email_clinic,
                    }
                    : null,
                purchasedBooks,
                purchasedVideos
            },
            token,
            message: "Login successful",
        });
    } catch (err) {
        console.error("loginUser error:", err);
        res.status(500).json({ error: "Failed to login user", details: err.message });
    }
};
// Get user by ID
exports.getUserById = async (req, res) => {
    try {
        const userId = req.user.id;

        // Get base user data
        const user = await UserModel.getUserById(userId);
        if (!user) return res.status(404).json({ message: 'User not found' });

        // Get roles
        const roles = await UserRoleModel.getRolesByUserId(userId);

        // Get topics (update with your actual method)
        const topics = await TopicModel.getUserTopics(userId);

        // Get active subscription
        const subscription = await UserSubscriptionModel.getUserActiveSubscriptionWithPackage(user.id);
        let features = [];
        if (subscription) {
            features = await UserSubscriptionModel.getSubscriptionFeatures(subscription.id);
        }

        // Get purchased books
        const purchasedBooks = await BooksPurchaseModel.getUserPurchasedBooks(userId);

        // Get purchased videos
        const purchasedVideos = await VideoPurchaseModel.getUserPurchasedVideos(userId);

        res.status(200).json({
            data: {
                id: user.id,
                name: user.name,
                nickname: user.nickname,
                email: user.email,
                phone: user.phone,
                age: user.age,
                gender: user.gender,
                endDate: user.end_date,
                createdAt: user.created_at,
                roles: roles.map(role => role.name),
                topics,
                subscription: subscription
                    ? {
                        id: subscription.id,
                        packageId: subscription.package_id,
                        packageName: subscription.package_name,
                        planType: subscription.plan_type,
                        startDate: subscription.start_date,
                        endDate: subscription.end_date,
                        pricePaid: subscription.price_paid,
                        currency: subscription.currency,
                        isActive: subscription.is_active,
                        features,
                    }
                    : null,
                purchasedBooks,
                purchasedVideos
            },
            message: "User data fetched successfully"
        });

    } catch (error) {
        console.error('Error fetching user data:', error);
        res.status(500).json({ message: 'Server error', details: error.message });
    }
};

// Get all users
exports.getAllUsers = async (req, res) => {
    try {
        const users = await UserModel.getAllUsers();

        // Fetch roles for each user and attach them to the user data
        const usersWithRoles = await Promise.all(users.map(async user => {
            const roles = await UserRoleModel.getRolesByUserId(user.id);
            return {
                ...user,
                roles: roles.map(role => role.name)
            };
        }));

        res.status(200).json({ data: usersWithRoles });
    } catch (error) {
        console.error('Error fetching users:', error);
        res.status(500).json({ message: 'Server error', details: error.message });
    }
};

exports.getUserProfile = async (req, res) => {
    const requestedId = parseInt(req.params.id, 10);
    const { id: authUserId, role } = req.user;

    // Allow access if: user is requesting their own profile OR user is admin/manager (role 1 or 2)
    const isAdmin = role === 1 || role === 2;
    const isOwner = authUserId === requestedId;

    if (!isAdmin && !isOwner) {
        return res.status(403).json({ message: 'Access denied: insufficient permissions' });
    }

    try {
        const user = await UserModel.getUserById(requestedId);
        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        res.status(200).json({ data: user });
    } catch (error) {
        console.error('Error fetching user profile:', error);
        res.status(500).json({ message: 'Server error', details: error.message });
    }
};

// get count of the users from list of users
exports.getNumberOfUsers = async (req, res) => {
    try {
        const count = await UserModel.getNumberOfUsers();
        res.status(200).json({ data: count });
    } catch (error) {
        console.error('Error fetching number of users:', error);
        res.status(500).json({ message: 'Server error', details: error.message });
    }
};

// Update user data
exports.updateUser = async (req, res) => {
    try {
        const userId = req.params.id; // Get user ID from URL parameter
        const { name, nickname, email, phone, gender, age, password } = req.body;

        // Validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (email && !emailRegex.test(email)) {
            return res.status(400).json({ message: 'Invalid email format' });
        }

        // Validate phone number format (only digits allowed)
        const phoneRegex = /^\d+$/;
        if (phone && !phoneRegex.test(phone)) {
            return res.status(400).json({ message: 'Invalid phone number format' });
        }

        // Validate password format (minimum 8 characters)
        if (password && password.length < 8) {
            return res.status(400).json({ message: 'Password must be at least 8 characters long' });
        }

        // Validate nickname length (minimum 3 characters)
        if (nickname && nickname.length < 3) {
            return res.status(400).json({ message: 'Nickname must be at least 3 characters' });
        }

        // Validate name length (minimum 3 characters)
        if (name && name.length < 3) {
            return res.status(400).json({ message: 'Name must be at least 3 characters' });
        }

        // Validate age (must be positive)
        if (age !== undefined && age < 0) {
            return res.status(400).json({ message: 'Age must be greater than 0' });
        }

        // Validate gender (optional but must be 'male' or 'female' if provided)
        if (gender && gender !== 'male' && gender !== 'female') {
            return res.status(400).json({ message: 'Invalid gender value' });
        }

        // Update the user in the database
        const result = await UserModel.updateUserById(userId, name, nickname, email, phone, gender, age, password);

        // Check if the update was successful
        if (result.affectedRows === 0) {
            return res.status(404).json({ message: 'User not found' });
        }

        // Fetch the updated user data from the database
        const updatedUser = await UserModel.getUserById(userId);

        // Send a successful response with the updated user data
        res.status(200).json({
            data: updatedUser, // Return the updated user data
            message: 'User updated successfully'
        });
    } catch (error) {
        console.error('Error updating user data:', error);
        res.status(500).json({ message: 'Server error', details: error.message });
    }
};



// Update user role by id 
exports.updateUserRoleById = async (req, res) => {
    try {
        const userId = req.params.id;
        const { role } = req.body;

        if (!role) {
            return res.status(400).json({ message: 'Role is required' });
        }
        const result = await UserRoleModel.updateUserRoleById(userId, role);
        if (result.affectedRows === 0) {
            return res.status(404).json({ message: 'User not found' });
        }
        res.status(200).json({ message: 'User role updated successfully' });
    } catch (error) {
        console.error('Error updating user role:', error);
        res.status(500).json({ message: 'Server error', details: error.message });
    }
};

// Update admin role by user ID
exports.updateAdminRoleByUserId = async (req, res) => {
    try {
        const userId = req.params.id;
        const { role } = req.body;

        if (!role) {
            return res.status(400).json({ message: 'Role is required' });
        }
        const result = await UserRoleModel.updateAdminRoleByUserId(userId, role);
        if (result.affectedRows === 0) {
            return res.status(404).json({ message: 'User not found' });
        }
        res.status(200).json({ message: 'Admin role updated successfully' });
    } catch (error) {
        console.error('Error updating admin role:', error);
        res.status(500).json({ message: 'Server error', details: error.message });
    }
};

// change password
exports.changePassword = async (req, res) => {
    try {
        const userId = req.params.id;
        const { currentPassword, newPassword } = req.body;
        const result = await UserModel.changePassword(userId, currentPassword, newPassword);
        if (result.affectedRows === 0) {
            return res.status(404).json({ message: 'User not found' });
        }
        res.status(200).json({ message: 'Password changed successfully' });
    } catch (error) {
        console.error('Error changing password:', error);
        res.status(500).json({ message: 'Server error', details: error.message });
    }
};

// forgot password
exports.forgotPassword = async (req, res) => {
    try {
        const { email } = req.body;
        const result = await UserModel.forgotPassword(email);
        if (result.affectedRows === 0) {
            return res.status(404).json({ message: 'User not found' });
        }
        res.status(200).json({ message: 'Password reset email sent successfully' });
    } catch (error) {
        console.error('Error sending password reset email:', error);
        res.status(500).json({ message: 'Server error', details: error.message });
    }
};

exports.resetPassword = async (req, res) => {
    const { token } = req.body;
    const { newPassword } = req.body;

    try {
        // Check if token exists and is not expired
        const query = `SELECT id FROM users WHERE reset_token = ? AND reset_token_expiry > NOW() LIMIT 1`;
        const [user] = await pool.query(query, [token]);

        if (user.length === 0) {
            return res.status(400).json({ success: false, message: 'Invalid or expired token.' });
        }

        // Hash the new password
        const hashedPassword = await bcrypt.hash(newPassword, 10);

        // Update the user's password and clear the reset token
        const updateQuery = `UPDATE users SET password = ?, reset_token = NULL, reset_token_expiry = NULL WHERE id = ?`;
        await pool.query(updateQuery, [hashedPassword, user[0].id]);

        return res.status(200).json({ success: true, message: 'Password has been reset successfully.' });

    } catch (error) {
        console.error('Error resetting password:', error);
        return res.status(500).json({ success: false, message: 'Failed to reset password.' });
    }
};

// Logout user and delete tokens
exports.logoutUser = async (req, res) => {
    try {
        const userId = req.user.id;

        // Delete all tokens for the user
        await TokenModel.deleteTokens(userId);

        res.status(200).json({ message: 'Logged out successfully' });
    } catch (error) {
        console.error('Error logging out user:', error);
        res.status(500).json({ message: 'Logout failed', details: error.message });
    }
};

exports.validateUserToken = async (req, res) => {
    try {
        const userId = req.user.id;
        const isValid = await TokenModel.validateToken(userId);
        if (isValid) {
            res.status(200).json({ message: 'Token is valid' });
        } else {
            res.status(400).json({ message: 'Token is invalid or expired' });
        }
    } catch (error) {
        console.error('Error validating user token:', error);
        res.status(500).json({ message: 'Token validation failed', details: error.message });
    }
};

// toggle user status
exports.toggleUserStatus = async (req, res) => {
    try {
        const userId = req.params.id;
        const { is_active } = req.body;

        // Validate input
        if (typeof is_active === "undefined") {
            return res.status(400).json({ success: false, error: "Missing is_active value" });
        }

        const user = await UserModel.getUserById(userId);
        if (!user) {
            return res.status(404).json({ success: false, error: "User not found" });
        }

        await UserModel.updateUserStatus(userId, is_active);

        return res.status(200).json({
            success: true,
            newStatus: is_active,
            message: "User status updated successfully"
        });
    } catch (error) {
        console.error("Error toggling user status:", error);
        return res.status(500).json({
            success: false,
            error: "Server error",
            details: error.message
        });
    }
};

// Delete a user
exports.deleteUser = async (req, res) => {
    try {
        const userId = req.params.id;

        const result = await UserModel.deleteUserById(userId);

        if (result.affectedRows === 0) {
            return res.status(404).json({ message: 'User not found' });
        }

        res.status(200).json({ message: 'User deleted successfully' });
    } catch (error) {
        console.error('Error deleting user:', error);
        res.status(500).json({ message: 'Server error', details: error.message });
    }
};
