const answerModel = require('../models/answerModel');
const questionModel = require('../models/questionModel');

exports.getTheFirstQuestion = async (req, res) => {
    try {
        const question = await questionModel.getTheFirstQuestion();
        if (!question) {
            return res.status(404).json({ error: 'No first question found.' });
        }
        res.status(200).json(question);
    } catch (error) {
        console.error('Error retrieving the first question:', error.message);
        res.status(500).json({ message: 'Failed to retrieve the first question.' });
    }
};

// Answer a question and fetch the next question
exports.answerQuestion = async (req, res) => {
    try {
        const { userId, questionId } = req.params;
        const { answerText } = req.body;

        // Validate input
        if (!userId || !questionId || !answerText) {
            return res.status(400).json({ error: 'Missing required fields: userId, questionId, or answerText.' });
        }

        // Save the answer
        const answerId = await answerModel.createAnswer(userId, questionId, answerText);

        // Fetch the current question
        const currentQuestion = await questionModel.getById(questionId);

        if (!currentQuestion) {
            return res.status(404).json({ error: `Question with ID ${questionId} not found.` });
        }

        // Determine the next question based on the routes
        const nextQuestionId = currentQuestion.yes_route_id; // Adjust logic if using yes/no conditions
        if (!nextQuestionId) {
            return res.status(200).json({ 
                message: 'Answer recorded. No more questions available.', 
                answerId, 
                nextQuestion: null 
            });
        }

        const nextQuestion = await questionModel.getById(nextQuestionId);
        res.status(200).json({
            message: 'Answer recorded successfully.',
            answerId,
            nextQuestion: nextQuestion || null
        });
    } catch (error) {
        console.error('Error answering question:', error.message);
        res.status(500).json({ error: 'Failed to process the answer.' });
    }
};


// Save multiple answers for a thought
exports.saveAnswers = async (req, res) => {
    const { thoughtId, userId, answers } = req.body;

    try {
        // Validate input
        if (!thoughtId || !userId || !Array.isArray(answers) || answers.length === 0) {
            return res.status(400).json({ error: 'Invalid request data: thoughtId, userId, or answers.' });
        }

        // Save each answer
        for (const answer of answers) {
            const { questionId, answerText } = answer;
            if (!questionId || !answerText) {
                return res.status(400).json({ error: 'Invalid answer data: questionId or answerText is missing.' });
            }
            await answerModel.createAnswer(userId, questionId, answerText);
        }

        res.status(201).json({ message: 'Answers saved successfully.' });
    } catch (error) {
        console.error('Error saving answers:', error.message);
        res.status(500).json({ error: 'Failed to save answers.' });
    }
};
