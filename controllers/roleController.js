const RoleModel = require('../models/roleModel');


exports.getAllRoles = async (req, res) => {
    try {
        const roles = await RoleModel.getAllRoles();
        res.json(roles);
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: 'Internal server error' });
    }
};

exports.createRole = async (req, res) => {
    const { name } = req.body;
    try {
        const result = await RoleModel.createRole(name);
        const userId = result.insertId;
        
        res.status(201).json({
            data:{
                id: userId,
                name
            }
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: 'Internal server error' });
    }
};

exports.updateRole = async (req, res) => {
    const { name } = req.body;
    const { id } = req.params;
    try {
        const result = await RoleModel.updateRole(name, id);
        res.status(200).json(result);
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: 'Internal server error' });
    }
};

exports.deleteRole = async (req, res) => {
    const { id } = req.params;
    try {
        const result = await RoleModel.deleteRole(id);
        res.status(200).json(result);
    } catch (err) {
        console.error(err);
        res.status(500).json({ error: 'Internal server error' });
    }
};

