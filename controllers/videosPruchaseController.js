const VideoPurchaseModel = require('../models/videoPurchaseModel');
const pool = require('../config/db');

exports.purchaseVideo = async (req, res) => {
  try {
    const userId = req.user.id;
    const { video_id } = req.body;

    if (!video_id) {
      return res.status(400).json({ error: "Missing video_id in request body" });
    }

    const [video] = await pool.query(
      "SELECT id, price FROM videos WHERE id = ? AND is_active = 1",
      [video_id]
    );

    if (video.length === 0) {
      return res.status(404).json({ error: "Video not found or inactive" });
    }

    const videoPrice = video[0].price;

    const purchaseId = await VideoPurchaseModel.createPurchase(userId, video_id, videoPrice);

    return res.status(201).json({ message: "Video purchased successfully", purchaseId });
  } catch (error) {
    if (error.message.includes("already purchased")) {
      return res.status(409).json({ error: "You already purchased this video" });
    }

    console.error("purchaseVideo error:", error);
    return res.status(500).json({ error: "Failed to purchase video" });
  }
};

exports.checkVideoPurchase = async (req, res) => {
  try {
    const { video_id } = req.query;

    if (!video_id) {
      return res.status(400).json({ error: "Missing video_id in request query" });
    }

    const purchased = req.user?.purchasedVideos?.some(
      (video) => video.video_id === parseInt(video_id)
    );

    return res.status(200).json({ isPurchased: !!purchased });
  } catch (error) {
    console.error("checkVideoPurchase error:", error);
    return res.status(500).json({ error: "Failed to check video purchase" });
  }
};

exports.getPurchasedVideos = async (req, res) => {
  try {
    const userId = req.user.id;
    const videos = await VideoPurchaseModel.getUserPurchasedVideos(userId);

    res.status(200).json({
      message: 'Purchased videos fetched successfully',
      videos,
    });
  } catch (error) {
    console.error('Error fetching purchased videos:', error);
    res.status(500).json({ error: 'Failed to fetch purchased videos' });
  }
};