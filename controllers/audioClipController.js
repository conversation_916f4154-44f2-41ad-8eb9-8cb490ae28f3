const AudioClip = require('../models/audioClipModel'); // Ensure this model is correctly set up

exports.createAudioClip = async (req, res) => {
    const { thoughtId, duration } = req.body;
    const audioUrl = req.file ? req.file.path : null;

    if (!audioUrl) {
        return res.status(400).json({ error: 'Audio file is required' });
    }

    try {
        const id = await AudioClip.create(thoughtId, audioUrl, duration);
        res.status(201).json({ id });
    } catch (error) {
        console.error('Error creating audio clip:', error);
        res.status(500).json({ error: error.message });
    }
};

exports.getAudioClipById = async (req, res) => {
    const { id } = req.params;
    try {
        const audioClip = await AudioClip.getById(id);
        if (!audioClip) {
            return res.status(404).json({ error: 'Audio clip not found' });
        }
        res.json(audioClip);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
};

exports.getAllAudioClips = async (req, res) => {
    try {
        const audioClips = await AudioClip.getAll();
        res.json(audioClips);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
};

exports.updateAudioClip = async (req, res) => {
    const { id } = req.params;
    const { thoughtId, duration } = req.body;
    const audioUrl = req.file ? req.file.path : undefined; // optional update for audio file

    try {
        const updated = await AudioClip.updateById(id, { thoughtId, audioUrl, duration });
        if (!updated) {
            return res.status(404).json({ error: 'Audio clip not found or not updated' });
        }
        res.json({ message: 'Audio clip updated successfully' });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
};

exports.deleteAudioClip = async (req, res) => {
    const { id } = req.params;
    try {
        const deleted = await AudioClip.delete(id);
        if (!deleted) {
            return res.status(404).json({ error: 'Audio clip not found' });
        }
        res.json({ message: 'Audio clip deleted successfully' });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
};
