const Concern = require('../models/concernModel'); // Adjust this path based on your file structure
const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

exports.createConcern = async (req, res) => {
    const { name } = req.body;
    let image = null;

    if (!name) {
        return res.status(400).json({ error: 'Name is required.' });
    }

    try {
        // Check if an image file was uploaded
        if (req.file) {
            const originalPath = req.file.path;
            const webpFilename = `${Date.now()}-${Math.round(Math.random() * 1e9)}.webp`;
            const webpPath = path.join(__dirname, '../public/uploads/concerns', webpFilename);

            // Convert image to .webp
            await sharp(originalPath)
                .webp({ quality: 80 })
                .toFile(webpPath);

            // Delete the original uploaded file
            fs.unlinkSync(originalPath);

            image = `/uploads/concerns/${webpFilename}`;
        }

        // Fetch the current count of concerns
        const concerns = await Concern.getAll();
        const index = concerns.length === 0 ? 1 : concerns.length + 1;

        // Create a new concern with the provided name, image, and index
        const id = await Concern.create({ name, image, index });

        res.status(201).json({ message: 'Concern created successfully', id });
    } catch (err) {
        console.error('Error creating concern:', err.message);
        res.status(500).json({ error: 'Failed to create concern', details: err.message });
    }
};




exports.getConcernById = async (req, res) => {
    const { id } = req.params;
    try {
        const concern = await Concern.getById(id);
        res.status(200).json(concern);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
};

exports.getAllConcerns = async (req, res) => {
    try {
        const concerns = await Concern.getAll();
        
        res.status(200).json(concerns);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
};

exports.updateConcern = async (req, res) => {
    const { id } = req.params;
    const { name } = req.body;

    if (!name) {
        return res.status(400).json({ error: 'Name is required.' });
    }

    try {
        // Fetch the existing concern details
        const existingConcern = await Concern.getById(id); // Assuming Concern.getById fetches the concern details
        if (!existingConcern) {
            return res.status(404).json({ error: 'Concern not found.' });
        }

        let updatedImagePath = existingConcern.image;

        // If a new file is uploaded, convert it to .webp and remove the old file
        if (req.file) {
            const originalPath = req.file.path;
            const webpFilename = `${Date.now()}-${Math.round(Math.random() * 1e9)}.webp`;
            const webpPath = path.join(__dirname, '../public/uploads/concerns', webpFilename);

            // Convert the new image to .webp
            await sharp(originalPath)
                .webp({ quality: 80 })
                .toFile(webpPath);

            // Delete the original uploaded file
            fs.unlinkSync(originalPath);

            // Remove the old image file if it exists
            if (existingConcern.image) {
                const oldImagePath = path.join(__dirname, '../public', existingConcern.image);
                if (fs.existsSync(oldImagePath)) {
                    fs.unlinkSync(oldImagePath);
                }
            }

            updatedImagePath = `/uploads/concerns/${webpFilename}`;
        }

        // Update the concern in the database
        await Concern.updateById(id, name, updatedImagePath);

        res.status(200).json({ message: 'Concern updated successfully' });
    } catch (error) {
        console.error(`Error updating concern with ID ${id}:`, error.message);
        res.status(500).json({ error: 'Failed to update concern', details: error.message });
    }
};



exports.activateConcern = async (req, res) => {
    const { id } = req.params;
    try {
        await Concern.activate(id);
        res.status(200).json({ message: 'Concern activated successfully' });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
};

exports.deleteConcern = async (req, res) => {
    const { id } = req.params;
    try {
        await Concern.delete(id);
        res.status(200).json({ message: 'Concern deleted successfully' });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
};
