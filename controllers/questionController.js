const Question = require('../models/questionModel');

// Create multiple questions for a thought ID
exports.createQuestions = async (req, res) => {
    const { thoughtId, questions } = req.body;

    try {
        if (!thoughtId || !Array.isArray(questions) || questions.length === 0) {
            return res.status(400).json({ error: "Invalid input data" });
        }

        const result = await Question.createQuestions(thoughtId, questions);
        res.status(201).json({ message: "Questions created successfully", result });
    } catch (error) {
        console.error("Error creating questions:", error.message);
        res.status(500).json({ error: "Failed to create questions", details: error.message });
    }
};

// Get questions by thought ID
exports.getQuestionsByThoughtId = async (req, res) => {
    const { thoughtId } = req.params;

    // Validate input
    if (!thoughtId || isNaN(Number(thoughtId))) {
        return res.status(400).json({ error: 'Invalid thought ID.' });
    }

    try {
        const questions = await Question.getQuestionsByThoughtId(thoughtId);

        if (!questions || questions.length === 0) {
            console.warn(`[⚠️ Warning] No questions found for thoughtId: ${thoughtId}`);
            return res.status(404).json({ error: 'No questions found for this thought.' });
        }

        return res.status(200).json(questions);
    } catch (error) {
        console.error(`[❌ Server Error] Failed to fetch questions for thoughtId ${thoughtId}:`, error.message);

        // Return detailed error only in development
        return res.status(500).json({
            error: 'Failed to fetch questions.',
            message: process.env.NODE_ENV === 'development' ? error.message : undefined,
        });
    }
};


// Get a single question by ID
exports.getQuestionById = async (req, res) => {
    const { id } = req.params;

    // Validate the ID
    const parsedId = parseInt(id, 10);
    if (isNaN(parsedId)) {
        return res.status(400).json({ error: 'Invalid question ID. Must be a number.' });
    }

    try {
        const question = await Question.getById(parsedId);

        if (!question) {
            console.warn(`[WARN] No question found with ID ${parsedId}`);
            return res.status(404).json({ error: `No question found with ID ${parsedId}` });
        }

        return res.status(200).json({ success: true, data: question });
    } catch (error) {
        console.error(`[ERROR] Failed to retrieve question with ID ${parsedId}:`, error.message);
        return res.status(500).json({
            error: 'Failed to retrieve question',
            message: error.message,
        });
    }
};


// Update a question by ID
exports.updateQuestion = async (req, res) => {
    const { questionId } = req.params;
  
    if (!questionId) {
      return res.status(400).json({ error: 'Missing question ID' });
    }
  
    const {
      questionText,
      questionType,
      isQuestion,
      yesRouteId,
      noRouteId,
      answerText,
      summaryText,
      buttonYesText,
      buttonNoText,
    } = req.body;
  
    try {
      const updateResult = await Question.updateById(
        questionId,
        questionText,
        questionType,
        isQuestion,
        yesRouteId,
        noRouteId,
        answerText,
        summaryText,
        buttonYesText,
        buttonNoText
      );
  
      if (!updateResult.success) {
        return res.status(404).json({ error: updateResult.message });
      }
  
      res.status(200).json({ message: 'Question updated successfully' });
    } catch (error) {
      console.error('Error updating question:', error.message);
      res.status(500).json({ error: 'Failed to update question', details: error.message });
    }
  };
  


// Delete a question by ID
exports.deleteQuestion = async (req, res) => {
    const { id } = req.params;

    if (!id) {
        return res.status(400).json({ error: 'Question ID is required.' });
    }

    try {
        const deleteResult = await Question.delete(id);

        res.status(200).json({ message: 'Question deleted successfully.', result: deleteResult });
    } catch (error) {
        console.error('Error deleting question:', error.message);
        res.status(500).json({ error: 'Failed to delete question.' });
    }
};
