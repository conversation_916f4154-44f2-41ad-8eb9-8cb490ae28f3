const pool = require('../config/db');

exports.subscribeToPackage = async (req, res) => {
  try {
    const { user_id, package_id, payment_method, amount_paid } = req.body;

    // שלב 1: בדיקת חבילה
    const [[pkg]] = await pool.query('SELECT * FROM subscription_packages WHERE id = ?', [package_id]);
    if (!pkg) return res.status(404).json({ error: 'Package not found' });

    // שלב 2: חישוב תאריכי התחלה וסיום
    const start_date = new Date();
    const end_date = new Date();
    end_date.setMonth(end_date.getMonth() + 1);

    // שלב 3: הוספת המנוי למסד הנתונים
    await pool.query(
      `INSERT INTO user_subscriptions 
        (user_id, package_id, payment_method, amount_paid, start_date, end_date, created_at)
       VALUES (?, ?, ?, ?, ?, ?, NOW())`,
      [user_id, package_id, payment_method, amount_paid, start_date, end_date]
    );

    // שלב 4: תגובת הצלחה
    res.json({ success: true, message: 'Subscription successful' });

  } catch (err) {
    console.error('Subscription error:', err.message);
    res.status(500).json({ error: 'Internal server error' });
  }
};