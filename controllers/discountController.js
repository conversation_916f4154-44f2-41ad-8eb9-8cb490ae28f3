const BooksModel = require("../models/booksModel");

class DiscountController {
    // Add a new discount option
    static async addDiscountOption(req, res) {
    
        const { subscriptionType, discountPercentage } = req.body;
    
        if (!subscriptionType || discountPercentage === undefined) {
            return res.status(400).json({ error: "Missing required fields" });
        }
    
        try {
            const result = await BooksModel.addDiscountOption(subscriptionType, discountPercentage);
            res.status(201).json(result);
        } catch (err) {
            console.error("Error adding discount:", err); // Log the error
            res.status(500).json({ error: err.message });
        }
    }
    
    static async getDiscountOptions(req, res) {
        try {
            const discounts = await BooksModel.getDiscountOptions();

            res.status(200).json({
                data: discounts.map(discount => ({
                    id: discount.id,
                    subscription_type: discount.subscription_type,
                    discount_percentage: discount.discount_percentage,
                    is_active: discount.is_active,
                }))
            });
        } catch (err) {
            res.status(500).json({ error: err.message });
        }
    }

    // Get all discount options
    static async updateDiscountOption(req, res) {
        const { subscriptionType, discountPercentage } = req.body;

        if (!subscriptionType || discountPercentage === undefined) {
            return res.status(400).json({ error: "Missing required fields" });
        }

        try {
            const result = await BooksModel.updateDiscountOption(subscriptionType, discountPercentage);
            res.status(200).json(result);
        } catch (err) {
            res.status(500).json({ error: err.message });
        }
    }

    static async deleteDiscountOption(req, res) {
        const { subscriptionType } = req.body;

        if (!subscriptionType) {
            return res.status(400).json({ error: "Missing required fields" });
        }

        try {
            const result = await BooksModel.deleteDiscountOption(subscriptionType);
            res.status(200).json(result);
        } catch (err) {
            res.status(500).json({ error: err.message });
        }
    }
}

module.exports = DiscountController;
