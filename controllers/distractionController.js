const distractionModel = require('../models/distractionModel');
const fs = require('fs');
const path = require('path');   
const sharp = require('sharp');
const ffmpeg = require('fluent-ffmpeg');


// Create a distraction
exports.createDistraction = async (req, res) => {
    try {
        const { title, description, audio_url = null } = req.body;

        if (!title) {
            return res.status(400).json({ message: "Title is required." });
        }

        let image = null;

        // Check if an image file was uploaded
        if (req.files?.image?.[0]) {
            const uploadedFile = req.files.image[0];
            const inputFilePath = uploadedFile.path;
            const outputFileName = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}.webp`;
            const outputFilePath = path.join(__dirname, '../public/uploads/distractions', outputFileName);

            // Convert image to .webp
            await sharp(inputFilePath)
                .toFormat('webp')
                .toFile(outputFilePath);

            // Remove the original uploaded file
            fs.unlinkSync(inputFilePath);

            // Set the webp file path
            image = `/uploads/distractions/${outputFileName}`;
        }

        // Create a new distraction record
        const createdDistractionId = await distractionModel.create({
            title,
            image,
            description,
            audio_url,
        });

        res.status(201).json({
            message: "Distraction created successfully",
            data: { id: createdDistractionId, title, description, image, audio_url },
        });
    } catch (error) {
        res.status(500).json({ message: "Error creating distraction", error: error.message });
    }
};


// Create a sub-distraction
exports.createSubDistraction = async (req, res) => {
    try {
        const { title, distraction_id, description = "", type = "sub_category" } = req.body;

        if (!title || !distraction_id) {
            return res.status(400).json({ message: "Title and distraction ID are required." });
        }

        if (!req.files?.audio_url?.[0]) {
            return res.status(400).json({ message: "Audio file is required." });
        }

        const uploadedFile = req.files.audio_url[0];
        const inputFilePath = uploadedFile.path;

        // Generate a unique filename for the converted .webm file
        const outputFileName = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}.webm`;
        const outputFilePath = path.join(__dirname, '../public/uploads/distractions', outputFileName);

        // Convert audio to .webm
        await new Promise((resolve, reject) => {
            ffmpeg(inputFilePath)
                .output(outputFilePath)
                .toFormat('webm')
                .on('progress', (progress) => {
                })
                .on('end', () => {
                    resolve();
                })
                .on('error', (err) => {
                    reject(err);
                })
                .run();
        });

        // Remove the original uploaded file
        fs.unlinkSync(inputFilePath);

        // Use the converted .webm file path
        const audio_url = `/uploads/distractions/${outputFileName}`;

        // Ensure description is not null
        const validDescription = description;

        // Save sub-distraction details in the database
        const createdSubDistractionId = await distractionModel.createSubDistraction({
            title,
            audio_url,
            distraction_id,
            description: validDescription,
            type
        });

        res.status(201).json({
            message: "Sub-distraction created successfully",
            data: { id: createdSubDistractionId, title, audio_url, distraction_id, description: validDescription, type },
        });
    } catch (error) {
        res.status(500).json({ message: "Error creating sub-distraction", error: error.message });
    }
};

exports.getAll = async (req, res) => {
    try {
        const distractions = await distractionModel.getAll();
        res.status(200).json({ data: distractions });
    } catch (error) {
        console.error("Error retrieving distractions:", error);
        res.status(500).json({ message: "Error retrieving distractions", error: error.message });
    }

};


// Get all distractions
exports.getAllDistraction = async (req, res) => {
    try {
        const distractions = await distractionModel.getAllDistraction();
        res.status(200).json({ data: distractions });
    } catch (error) {
        console.error("Error retrieving distractions:", error);
        res.status(500).json({ message: "Error retrieving distractions", error: error.message });
    }
};
// Get all sub-distractions
exports.getAllSubDistraction = async (req, res) => {
    try {
        const { id } = req.params;
        const subDistractions = await distractionModel.getAllSubDistraction(id);
        res.status(200).json({ data: subDistractions });
    } catch (error) {
        console.error("Error retrieving sub-distractions:", error);
        res.status(500).json({ message: "Error retrieving sub-distractions", error: error.message });
    }
};



// Get a distraction by ID
exports.getDistractionById = async (req, res) => {
    try {
        const { id } = req.params;

        if (!id) {
            return res.status(400).json({ message: "Distraction ID is required." });
        }

        const distraction = await distractionModel.getById(id);

        if (!distraction) {
            return res.status(404).json({ message: "Distraction not found." });
        }

        res.status(200).json({ data: distraction });
    } catch (error) {
        console.error("Error retrieving distraction by ID:", error);
        res.status(500).json({ message: "Error retrieving distraction", error: error.message });
    }
};

// Update a distraction by ID
exports.updateDistractionById = async (req, res) => {
    try {
        const { id } = req.params;
        const { title, description } = req.body;

        if (!id) {
            return res.status(400).json({ message: "Distraction ID is required." });
        }

        // Fetch the existing distraction
        const existingDistraction = await distractionModel.getById(id);

        if (!existingDistraction) {
            return res.status(404).json({ message: "Distraction not found." });
        }

        let image = existingDistraction.image; // Default to existing image

        // Check if a new file is uploaded
        if (req.files?.image?.[0]) {
            const uploadedFile = req.files.image[0];
            const inputFilePath = uploadedFile.path;

            // Generate a unique filename for the .webp file
            const outputFileName = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}.webp`;
            const outputFilePath = path.join(__dirname, '../public/uploads/distractions', outputFileName);

            // Convert the image to .webp
            await sharp(inputFilePath)
                .toFormat('webp')
                .toFile(outputFilePath);

            // Delete the uploaded original file
            fs.unlinkSync(inputFilePath);

            // Update the image path
            image = `/uploads/distractions/${outputFileName}`;

            // Remove the old image file if it exists
            if (existingDistraction.image) {
                const oldImagePath = path.join(__dirname, '../public', existingDistraction.image);
                if (fs.existsSync(oldImagePath)) {
                    fs.unlinkSync(oldImagePath);
                }
            }
        }

        // Update the database
        const updatedDistraction = await distractionModel.update(id, {
            title,
            image,
            description,
        });

        res.status(200).json({
            message: "Distraction updated successfully",
            data: updatedDistraction,
        });
    } catch (error) {
        console.error("Error updating distraction:", error);
        res.status(500).json({ message: "Error updating distraction", error: error.message });
    }
};

// Update a sub-distraction by ID
// ✅ updateSubDistractionById in distractionController.js
exports.updateSubDistractionById = async (req, res) => {
    try {
        const { id } = req.params;
        const { title, type = "sub_category" } = req.body;

        if (!id) {
            return res.status(400).json({ message: "Sub-distraction ID is required." });
        }

        // Fetch existing record
        const existingSubDistraction = await distractionModel.getAudioById(id);

        if (!existingSubDistraction) {
            return res.status(404).json({ message: "Sub-distraction not found." });
        }

        let audio_url = existingSubDistraction.audio_url;

        // ✅ Handle single audio file upload properly
        const uploadedFile =
            Array.isArray(req.files?.audio_url) && req.files.audio_url.length > 0
                ? req.files.audio_url[0]
                : null;

        if (uploadedFile) {
            const inputFilePath = uploadedFile.path;
            const outputFileName = `${Date.now()}-${Math.random().toString(36).substring(2, 9)}.webm`;
            const outputFilePath = path.join(__dirname, '../public/uploads/distractions', outputFileName);

            // Convert audio to .webm
            await new Promise((resolve, reject) => {
                ffmpeg(inputFilePath)
                    .output(outputFilePath)
                    .toFormat('webm')
                    .on('end', () => resolve())
                    .on('error', (err) => reject(err))
                    .run();
            });

            // Delete the original uploaded file
            fs.unlinkSync(inputFilePath);

            // Delete old audio if it exists
            if (existingSubDistraction.audio_url) {
                const oldAudioPath = path.join(__dirname, '../public', existingSubDistraction.audio_url);
                if (fs.existsSync(oldAudioPath)) {
                    fs.unlinkSync(oldAudioPath);
                }
            }

            audio_url = `/uploads/distractions/${outputFileName}`;
        }

        // Update database
        const updated = await distractionModel.subUpdate(id, {
            title,
            audio_url,
            type,
        });

        res.status(200).json({
            message: "Sub-distraction updated successfully",
            data: updated,
        });
    } catch (error) {
        console.error("Error updating sub-distraction:", error);
        res.status(500).json({ message: "Error updating sub-distraction", error: error.message });
    }
};


// Delete a distraction by ID
exports.deleteDistractionById = async (req, res) => {
    try {
        const { id } = req.params;

        if (!id) {
            return res.status(400).json({ message: "Distraction ID is required." });
        }

        const deletedDistraction = await distractionModel.delete(id);

        if (!deletedDistraction) {
            return res.status(404).json({ message: "Distraction not found." });
        }

        res.status(200).json({ message: "Distraction deleted successfully" });
    } catch (error) {
        console.error("Error deleting distraction:", error);
        res.status(500).json({ message: "Error deleting distraction", error: error.message });
    }
};

exports.deleteSubDistractionById = async (req, res) => {
    try {
        const { id } = req.params;

        if (!id) {
            return res.status(400).json({ message: "Sub-distraction ID is required." });
        }

        const deletedSubDistraction = await distractionModel.deleteSubDistractionById(id);

        if (!deletedSubDistraction) {
            return res.status(404).json({ message: "Sub-distraction not found." });
        }

        res.status(200).json({ message: "Sub-distraction deleted successfully" });
    } catch (error) {
        console.error("Error deleting sub-distraction:", error);
        res.status(500).json({ message: "Error deleting sub-distraction", error: error.message });
    }
};
