const UserMentalState = require('../models/UserMentalStateModel');

// Controller method to create a mental state record before treatment
exports.createBeforeTreatment = async (req, res) => {
    const { user_id, thought_id, before_treatment, notes } = req.body;

    if (!user_id || !thought_id || before_treatment === undefined) {
        return res.status(400).json({ success: false, message: "User ID, Thought ID, and before treatment score are required." });
    }

    try {
        const result = await UserMentalState.createBeforeTreatment(user_id, thought_id, before_treatment, notes);
        res.status(201).json({ success: true, message: "Mental state before treatment created successfully.", data: result });
    } catch (err) {
        console.error('Error creating mental state monitoring:', err.message);
        res.status(500).json({ success: false, message: "Failed to create mental state monitoring", error: err.message });
    }
};

// Controller method to update the mental state record after treatment
exports.updateAfterTreatment = async (req, res) => {
    const { id, after_treatment, notes } = req.body;

    if (after_treatment === undefined) {
        return res.status(400).json({ success: false, message: "After treatment score is required." });
    }

    try {
        const result = await UserMentalState.updateAfterTreatment(id, after_treatment, notes);
        res.status(200).json({ success: true, message: "Mental state after treatment updated successfully.", data: result });
    } catch (err) {
        console.error('Error updating mental state after treatment:', err.message);
        res.status(500).json({ success: false, message: "Failed to update mental state monitoring", error: err.message });
    }
};

// Controller method to get mental states for a user
exports.getUserMentalStates = async (req, res) => {
    const { user_id } = req.params;

    if (!user_id) {
        return res.status(400).json({ success: false, message: "User ID is required." });
    }

    try {
        const mentalStates = await UserMentalState.getUserMentalStates(user_id);
        if (mentalStates.length === 0) {
            return res.status(404).json({ success: false, message: "No mental state records found." });
        }
        res.status(200).json({ success: true, data: mentalStates });
    } catch (err) {
        console.error('Error fetching mental state records:', err.message);
        res.status(500).json({ success: false, message: "Failed to fetch mental state records", error: err.message });
    }
};

// Controller method to get mental state by ID
exports.getMentalStateById = async (req, res) => {
    const { id } = req.params;

    if (!id) {
        return res.status(400).json({ success: false, message: "ID is required." });
    }

    try {
        const mentalState = await UserMentalState.getMentalStateById(id);
        if (!mentalState) {
            return res.status(404).json({ success: false, message: "Mental state not found." });
        }
        res.status(200).json({ success: true, data: mentalState });
    } catch (err) {
        console.error('Error fetching mental state by ID:', err.message);
        res.status(500).json({ success: false, message: "Failed to fetch mental state by ID", error: err.message });
    }
};

// Controller method to delete a mental state record by ID
exports.deleteMentalState = async (req, res) => {
    const { id } = req.params;

    if (!id) {
        return res.status(400).json({ success: false, message: "ID is required." });
    }

    try {
        const result = await UserMentalState.deleteMentalState(id);
        res.status(200).json({ success: true, message: "Mental state record deleted successfully." });
    } catch (err) {
        console.error('Error deleting mental state:', err.message);
        res.status(500).json({ success: false, message: "Failed to delete mental state", error: err.message });
    }
};
