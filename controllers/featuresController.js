const FeatureModel = require("../models/featureModel");

exports.getAllFeatures = async (req, res) => {
  try {
    const features = await FeatureModel.getAll();
    res.json({ success: true, features });
  } catch (error) {
    console.error("Error fetching features:", error.message);
    res.status(500).json({ error: "Failed to fetch features" });
  }
};

exports.createFeature = async (req, res) => {
  const { name, discount_percentage = null, type = null } = req.body;

  if (!name) {
    return res.status(400).json({ error: "Feature name is required" });
  }

  try {
    await FeatureModel.create(name, discount_percentage, type);
    res.json({ success: true, message: "Feature created successfully" });
  } catch (error) {
    console.error("Error creating feature:", error.message);
    res.status(500).json({ error: "Failed to create feature" });
  }
};

exports.updateFeature = async (req, res) => {
  const { id } = req.params;
  const { name, discount_percentage = null, type = null } = req.body;

  if (!name) {
    return res.status(400).json({ error: "Feature name is required" });
  }

  try {
    await FeatureModel.update(id, name, discount_percentage, type);
    res.json({ success: true, message: "Feature updated successfully" });
  } catch (error) {
    console.error("Error updating feature:", error.message);
    res.status(500).json({ error: "Failed to update feature" });
  }
};

exports.toggleFeatureStatus = async (req, res) => {
  const { id } = req.params;
  const { is_active } = req.body;

  try {
    await FeatureModel.toggleStatus(id, is_active);
    res.json({ success: true, message: "Feature status updated successfully" });
  } catch (error) {
    console.error("Error toggling feature status:", error.message);
    res.status(500).json({ error: "Failed to update feature status" });
  }
};

exports.deleteFeature = async (req, res) => {
  const { id } = req.params;

  try {
    await FeatureModel.delete(id);
    res.json({ success: true, message: "Feature deleted successfully" });
  } catch (error) {
    console.error("Error deleting feature:", error.message);
    res.status(500).json({ error: "Failed to delete feature" });
  }
};