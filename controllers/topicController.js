// Controller to update a topic
const fs = require('fs');
const path = require('path');
const sharp = require('sharp');
const TopicModel = require('../models/topicModel');
const NotiTopicModel = require('../models/NotiTopicModel');

// Controller to create a new topic
exports.createTopic = async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({ error: 'Image file is required' });
        }

        const { name, type, parent_id } = req.body;

        const webpFilename = `${Date.now()}-${Math.round(Math.random() * 1e9)}.webp`;
        const webpPath = path.join(__dirname, '../public/uploads/topics', webpFilename);

        await sharp(req.file.path).webp({ quality: 80 }).toFile(webpPath);
        fs.unlinkSync(req.file.path);

        const imagePath = `/uploads/topics/${webpFilename}`;

        await TopicModel.createTopic({
            image: imagePath,
            name,
            type,
            parent_id: parent_id || null,
        });

        res.status(201).json({ message: 'Topic created successfully' });
    } catch (err) {
        console.error('Error creating topic:', err.message);
        res.status(500).json({ error: 'Failed to create topic', details: err.message });
    }
};

exports.userChooseTopics = async (req, res) => {
    const userId = req.params.id;
    const { topicIds } = req.body;

    try {
        const existingTopics = await TopicModel.getExistingTopicsByIds(topicIds);
        const validTopicIds = existingTopics.map(topic => topic.id);

        const invalidTopicIds = topicIds.filter(id => !validTopicIds.includes(id));
        if (invalidTopicIds.length > 0) {
            return res.status(400).json({
                error: 'Some topicIds do not exist in the topics table',
                invalidTopicIds
            });
        }

        await TopicModel.userChooseTopics(userId, validTopicIds);
        res.status(200).json({ message: 'Topics chosen successfully' });
    } catch (err) {
        console.error('Error choosing topics:', err.message);
        res.status(500).json({ error: 'Failed to choose topics' });
    }
};

exports.getUserTopics = async (req, res) => {
    const userId = req.params.id;

    try {
        const topics = await TopicModel.getUserTopics(userId);
        res.status(200).json({ data: { id: userId, topics } });
    } catch (err) {
        console.error('Error fetching user topics:', err.message);
        res.status(500).json({ error: 'Failed to fetch user topics' });
    }
};

exports.createTopicNotification = async (req, res) => {
    const { id } = req.params;
    const { text } = req.body;

    if (!text || !Array.isArray(text) || text.length === 0) {
        return res.status(400).json({ error: "Notification text cannot be empty" });
    }

    try {
        const notifications = text.map(async (message) => {
            return await NotiTopicModel.createNotification(id, message);
        });

        await Promise.all(notifications);
        res.status(201).json({ message: "Notifications created successfully" });
    } catch (error) {
        console.error("Error creating notifications:", error.message);
        res.status(500).json({ error: "Failed to create notifications", details: error.message });
    }
};

exports.getAllTopics = async (req, res) => {
    try {
        const topics = await TopicModel.getAllTopics();
        res.status(200).json({ data: topics });
    } catch (err) {
        console.error('Error fetching topics:', err.message);
        res.status(500).json({ error: 'Failed to fetch topics' });
    }
};

// Get notifications for a specific topic
exports.getTopicNotifications = async (req, res) => {
    const { id } = req.params;
  
    try {
      const notifications = await NotiTopicModel.getNotificationsByTopicId(id);
  
      const formatted = notifications.map(n => ({
        ...n,
        text: typeof n.text === "string" ? n.text : JSON.stringify(n.text),
        create_at: n.create_at ? new Date(n.create_at).toISOString() : null
      }));
  
      res.status(200).json({ success: true, data: formatted });
    } catch (error) {
      console.error("Error fetching notifications:", error.message);
      res.status(500).json({ error: "Failed to fetch notifications", details: error.message });
    }
  };
  

exports.getTopicById = async (req, res) => {
    const userId = req.params.id;
    try {
        const topic = await TopicModel.getTopicById(userId);
        if (topic) {
            res.status(200).json({ data: topic });
        } else {
            res.status(404).json({ message: 'Topic not found' });
        }
    } catch (err) {
        console.error('Error fetching topic by ID:', err.message);
        res.status(500).json({ error: 'Failed to fetch topic by ID' });
    }
};

exports.updateTopic = async (req, res) => {
    const { id } = req.params;
    const { name, type, parent_id } = req.body;

    try {
        const existingTopic = await TopicModel.getTopicById(id);
        if (!existingTopic) {
            return res.status(404).json({ message: 'Topic not found' });
        }

        let imagePath = existingTopic.image;
        if (req.file) {
            const oldImagePath = path.join(__dirname, '../public', existingTopic.image);
            if (fs.existsSync(oldImagePath)) fs.unlinkSync(oldImagePath);

            const webpFilename = `${Date.now()}-${Math.round(Math.random() * 1e9)}.webp`;
            const webpPath = path.join(__dirname, '../public/uploads/topics', webpFilename);

            await sharp(req.file.path).webp({ quality: 80 }).toFile(webpPath);
            fs.unlinkSync(req.file.path);

            imagePath = `/uploads/topics/${webpFilename}`;
        }

        await TopicModel.updateTopic(id, { image: imagePath, name, type, parent_id });
        res.status(200).json({ message: 'Topic updated successfully' });
    } catch (err) {
        console.error('Error updating topic:', err.message);
        res.status(500).json({ error: 'Failed to update topic' });
    }
};

exports.updateUserTopicById = async (req, res) => {
    const { id } = req.params;
    const { topicIds } = req.body;

    try {
        await TopicModel.updateUserTopicById(id, topicIds);
        res.status(200).json({ message: 'User topics updated successfully' });
    } catch (err) {
        console.error('Error updating user topics:', err.message);
        res.status(500).json({ error: 'Failed to update user topics' });
    }
};

exports.editTopicNotification = async (req, res) => {
    const { id } = req.params;
    const { title } = req.body;
  
    if (!title) {
      return res.status(400).json({ error: "Title is required" });
    }
  
    try {
      await NotiTopicModel.updateNotificationById(id, title);
      res.status(200).json({ message: "Notification updated successfully" });
    } catch (error) {
      console.error("Error updating notification:", error.message);
      res.status(500).json({ error: "Failed to update notification" });
    }
  };
  

exports.activateTopic = async (req, res) => {
    const { id } = req.params;
    try {
        await TopicModel.activateTopic(id);
        res.status(200).json({ message: 'Topic activated successfully' });
    } catch (err) {
        console.error('Error activating topic:', err.message);
        res.status(500).json({ error: 'Failed to activate topic' });
    }
};

exports.toggleTopicStatus = async (req, res) => {
    const { id } = req.params;
    const { is_active } = req.body;

    if (!id || is_active === undefined) {
        return res.status(400).json({ message: "ID and is_active are required." });
    }

    try {
        const updateResult = await TopicModel.updateTopicStatus(id, is_active);
        res.status(200).json(updateResult);
    } catch (error) {
        console.error("Error toggling topic status:", error.message);
        res.status(500).json({ message: error.message || "Failed to toggle topic status." });
    }
};

exports.deleteTopic = async (req, res) => {
    const { id } = req.params;
    try {
        await TopicModel.deleteTopic(id);
        res.status(200).json({ message: 'Topic deleted successfully' });
    } catch (err) {
        console.error('Error deleting topic:', err.message);
        res.status(500).json({ error: 'Failed to delete topic' });
    }
};
