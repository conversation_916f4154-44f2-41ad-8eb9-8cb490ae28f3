const BooksModel = require('../models/booksModel');
const upload = require('../middleware/uploadMiddleware');
const fs = require('fs');
const path = require('path');
const sharp = require('sharp');



// Add new book with cover image
exports.addBook = async (req, res) => {
    upload.fields([
        { name: "coverImage", maxCount: 1 },
        { name: "secondaryImage", maxCount: 1 }
    ])(req, res, async (uploadError) => {
        if (uploadError) {
            console.error('File upload error:', uploadError);
            return res.status(500).json({ error: 'File upload error', details: uploadError.message });
        }

        const { name, price } = req.body;
        let coverImagePath = null;
        let secondaryImagePath = null;

        if (req.files['coverImage']) {
            const originalPath = req.files['coverImage'][0].path; // Original file path
            const webpFilename = `${Date.now()}-${Math.round(Math.random() * 1E9)}.webp`; // Unique filename for .webp
            const webpPath = path.join(__dirname, '../public/uploads/books', webpFilename);

            try {
                // Convert the image to .webp format
                await sharp(originalPath)
                    .webp({ quality: 80 }) // 80% quality
                    .toFile(webpPath);

                // Optional: Remove the original file after conversion
                fs.unlinkSync(originalPath);

                // Set the .webp path for database storage
                coverImagePath = `/uploads/books/${webpFilename}`;
            } catch (conversionError) {
                console.error('Error converting image to .webp:', conversionError.message);
                return res.status(500).json({ error: 'Failed to process image', details: conversionError.message });
            }
        }

        // Validate fields
        if (!name || !price || !coverImagePath || !secondaryImagePath) {
            return res.status(400).json({
                error: 'All fields are required',
                missingFields: {
                    name: !name,
                    price: !price,
                    coverImage: !coverImagePath,
                },
            });
        }

        try {
            // Save book data to the database
            const newBook = await BooksModel.addBook({ name, price, image: coverImagePath });
            res.status(201).json({ data: newBook });
        } catch (error) {
            console.error('Error in addBook controller:', error.message);
            res.status(500).json({ error: 'Failed to add book', details: error.message });
        }
    });
};

exports.addPages = async (req, res) => {
    const { id: bookId } = req.params;

    try {
        const uploadedFiles = req.files || [];
        const convertedPages = [];

        for (const file of uploadedFiles) {
            const originalPath = file.path;
            const webpFilename = `${Date.now()}-${Math.round(Math.random() * 1e9)}.webp`; // Generate unique .webp filename
            const webpPath = path.join(__dirname, "../public/uploads/books", webpFilename);

            try {
                // Convert image to .webp
                await sharp(originalPath)
                    .webp({ quality: 80 })
                    .toFile(webpPath);

                // Remove the original file
                fs.unlinkSync(originalPath);

                // Add the converted file path to the pages array
                convertedPages.push({ page_image: `/uploads/books/${webpFilename}` });
            } catch (conversionError) {
                console.error(`Error converting image ${originalPath} to .webp:`, conversionError.message);
                return res.status(500).json({
                    error: "Failed to process one or more images",
                    details: conversionError.message,
                });
            }
        }

        // Check the current number of pages for the book
        const existingPages = await BooksModel.getPagesByBookId(bookId);
        const startIndex = existingPages.length === 0 ? 1 : existingPages.length + 1;

        // Add indices to the pages
        const pagesWithIndices = convertedPages.map((page, index) => ({
            page_image: page.page_image,
            index: startIndex + index,
        }));

        // Add new pages to the database
        await BooksModel.addPages(bookId, pagesWithIndices);

        res.status(201).json({ message: "Pages added successfully" });
    } catch (error) {
        console.error("Error adding pages:", error.message);
        res.status(500).json({ error: "Failed to add pages", details: error.message });
    }
};


// Function to get a book by ID
exports.getBookById = async (req, res) => {
    const { id } = req.params; // Extract the ID from the request parameters
    try {
        const book = await BooksModel.getBookById(id);
        if (!book) {
            return res.status(404).json({ error: 'Book not found' });
        }

        res.status(200).json({ data: book });
    } catch (err) {
        console.error(`Error fetching book with ID ${id}:`, err.message);
        res.status(500).json({ error: 'Failed to fetch book by ID', details: err.message });
    }
};

// Function to get all pages by book ID
exports.getBookPages = async (req, res) => {
    const { book_id } = req.params; // Extract book_id from route parameters
    try {
        const pages = await BooksModel.getPagesByBookId(book_id); // Fetch pages
        if (!pages || pages.length === 0) {
            console.warn(`No pages found for book_id: ${book_id}`);
            return res.status(404).json({ success: false, error: 'No pages found for this book' });
        }
        res.status(200).json({ success: true, pages });
    } catch (error) {
        console.error('Error fetching pages:', error.message);
        res.status(500).json({ success: false, error: 'Failed to fetch pages' });
    }
};



exports.getPagesById = async (req, res) => {
    const { id } = req.params;
    try {
        const pages = await BooksModel.getPagesById(id);
        if (!pages || pages.length === 0) {
            return res.status(404).json({ success: false, error: 'No pages found for this book' });
        }
        res.status(200).json({ success: true, pages });
    } catch (error) {
        console.error(`Error fetching pages for bookId ${id}:`, error.message);
        res.status(500).json({ success: false, error: 'Failed to fetch pages', details: error.message });
    }
}

// Function to get all books
exports.getAllBooks = async (req, res) => {
    try {
        const books = await BooksModel.getAllBooks();

        res.status(200).json({ data: books });
    } catch (err) {
        console.error('Error fetching books:', err.message);
        res.status(500).json({ error: 'Failed to fetch books', details: err.message });
    }
};

exports.getBookPages = async (req, res) => {
    const { book_id } = req.params; // Matches the route parameter
    try {
        const pages = await BooksModel.getPagesByBookId(book_id);
        if (!pages || pages.length === 0) {
            return res.status(404).json({ success: false, error: 'No pages found for this book' });
        }
        res.status(200).json({ success: true, pages });
    } catch (error) {
        console.error('Error fetching pages:', error.message);
        res.status(500).json({ success: false, error: 'Failed to fetch pages' });
    }
};



exports.updateBook = async (req, res) => {
    const { id } = req.params;
    const { name, price } = req.body;

    try {
        // Fetch the existing book details
        const existingBook = await BooksModel.getBookById(id);
        if (!existingBook) {
            return res.status(404).json({ error: "Book not found" });
        }

        let image = existingBook.image; // Keep the old cover image if no new one is provided
        let imageSecondary = existingBook.image_secondary; // Keep old secondary image

        // ✅ Use `req.files.coverImage` and `req.files.secondaryImage` properly
        if (req.files) {
            if (req.files.coverImage && req.files.coverImage.length > 0) {
                const originalPath = req.files.coverImage[0].path;
                const webpFilename = `${Date.now()}-${Math.round(Math.random() * 1e9)}.webp`;
                const webpPath = path.join(__dirname, "..", "public", "uploads", "books", webpFilename);

                try {
                    await sharp(originalPath).webp({ quality: 80 }).toFile(webpPath);
                    fs.unlinkSync(originalPath); // Remove the original image after conversion
                    image = `/uploads/books/${webpFilename}`;

                    // Delete the old cover image if it exists
                    if (existingBook.image && fs.existsSync(path.join(__dirname, "..", "public", existingBook.image))) {
                        fs.unlinkSync(path.join(__dirname, "..", "public", existingBook.image));
                    }
                } catch (conversionError) {
                    console.error(`Error converting cover image: ${conversionError.message}`);
                    return res.status(500).json({ error: "Failed to process cover image", details: conversionError.message });
                }
            }

            if (req.files.secondaryImage && req.files.secondaryImage.length > 0) {
                const originalPath = req.files.secondaryImage[0].path;
                const webpFilename = `${Date.now()}-${Math.round(Math.random() * 1e9)}.webp`;
                const webpPath = path.join(__dirname, "..", "public", "uploads", "books", webpFilename);

                try {
                    await sharp(originalPath).webp({ quality: 80 }).toFile(webpPath);
                    fs.unlinkSync(originalPath); // Remove the original image after conversion
                    imageSecondary = `/uploads/books/${webpFilename}`;

                    // Delete the old secondary image if it exists
                    if (existingBook.image_secondary && fs.existsSync(path.join(__dirname, "..", "public", existingBook.image_secondary))) {
                        fs.unlinkSync(path.join(__dirname, "..", "public", existingBook.image_secondary));
                    }
                } catch (conversionError) {
                    console.error(`Error converting secondary image: ${conversionError.message}`);
                    return res.status(500).json({ error: "Failed to process secondary image", details: conversionError.message });
                }
            }
        }

        // ✅ Ensure image is never NULL
        if (!image) {
            return res.status(400).json({ error: "Book cover image is required and cannot be null" });
        }

        // ✅ Update book details in the database
        const updatedBookData = {
            name: name || existingBook.name,
            price: price || existingBook.price,
        };

        await BooksModel.updateBookWithImage(id, updatedBookData, image, imageSecondary);
        res.status(200).json({ message: "Book updated successfully", data: updatedBookData });

    } catch (err) {
        console.error(`Error updating book with ID ${id}:`, err.message);
        res.status(500).json({ error: "Failed to update book", details: err.message });
    }
};



// update page by id 
exports.updatePage = async (req, res) => {
    const { id } = req.params;

    if (!id) {
        return res.status(400).json({ error: "Missing required parameters." });
    }

    try {
        const currentPage = await BooksModel.getPageById(id);

        if (!currentPage) {
            return res.status(404).json({ error: "Page not found." });
        }

        let updatedImagePath = currentPage.page_image;

        // If a new image is uploaded, process it
        if (req.file) {
            const originalPath = req.file.path;
            const uploadDir = path.join(__dirname, "../public/uploads/books");

            // Ensure the upload directory exists
            if (!fs.existsSync(uploadDir)) {
                fs.mkdirSync(uploadDir, { recursive: true });
            }

            const webpFilename = `${Date.now()}-${Math.round(Math.random() * 1e9)}.webp`;
            const webpPath = path.join(uploadDir, webpFilename);

            await sharp(originalPath)
                .webp({ quality: 80 })
                .toFile(webpPath);

            fs.unlinkSync(originalPath);

            const oldImagePath = path.join(__dirname, "../public", currentPage.page_image);
            if (fs.existsSync(oldImagePath)) {
                try {
                    fs.unlinkSync(oldImagePath);
                } catch (deleteError) {
                    console.error("Error deleting old image:", deleteError.message);
                }
            }

            updatedImagePath = `/uploads/books/${webpFilename}`;
        }

        // Update the page in the database
        await BooksModel.updatePageById(id, {
            page_image: updatedImagePath,
        });

        res.status(200).json({ message: "Page updated successfully." });
    } catch (error) {
        console.error(`Error updating page with ID ${id}:`, error.message);
        res.status(500).json({ error: "Failed to update page.", details: error.message });
    }
};




// Function update status of a book
exports.updateBookStatus = async (req, res) => {
    try {
        const { id } = req.params;

        if (!id || isNaN(id)) {
            return res.status(400).json({ error: 'Invalid book ID' });
        }

        const { is_exists } = req.body;

        if (typeof is_exists === 'undefined') {
            return res.status(400).json({ error: '`is_exists` field is required in the request body' });
        }

        const isExistsBoolean = is_exists === true || is_exists === 1 || is_exists === '1';

        const result = await BooksModel.updateBookStatus(id, isExistsBoolean);

        if (result.affectedRows === 0) {
            return res.status(404).json({ error: 'Book not found or no change applied' });
        }

        return res.status(200).json({
            message: 'Book status updated successfully',
            result
        });

    } catch (err) {
        console.error(`Error updating book status for book ID ${req.params.id}:`, err);
        return res.status(500).json({
            error: 'Failed to update book status',
            details: err.message
        });
    }
};

// Function to delete a page by ID
exports.deletePageById = async (req, res) => {
    const { id } = req.params;

    try {
        // Fetch the page details to get the file path
        const page = await BooksModel.getPageById(id);

        if (!page) {
            return res.status(404).json({ error: 'Page not found' });
        }

        const filePath = path.join(__dirname, '../public', page.page_image);

        // Attempt to delete the file
        if (fs.existsSync(filePath)) {
            try {
                fs.unlinkSync(filePath);
            } catch (err) {
                console.error(`Error deleting file: ${filePath}`, err.message);
                return res.status(500).json({ error: 'Failed to delete file', details: err.message });
            }
        } else {
            console.warn(`File not found: ${filePath}`);
        }

        // Delete the page record from the database
        const result = await BooksModel.deletePageById(id);

        res.status(200).json({ message: 'Page and file deleted successfully', result });
    } catch (err) {
        console.error(`Error deleting page with ID ${id}:`, err.message);
        res.status(500).json({ error: 'Failed to delete page', details: err.message });
    }
};


