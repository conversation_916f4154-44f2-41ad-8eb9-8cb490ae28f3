const ClinicModel = require('../models/clinicsModel');
const UserModel = require('../models/userModel');

// Admin Registration
exports.createClinic = async (req, res) => {
    try {
        const {
            clinic_name,
            address_clinic,
            user_id,
            phone_clinic,
            link_whatsapp,
            link_telegram,
            link_instagram,
            link_youtube,
            link_facebook,
            link_twitter,
            link_tiktok
        } = req.body;

        // Validate required fields
        if (!clinic_name || !address_clinic || !phone_clinic || !user_id) {
            return res.status(400).json({ message: 'Clinic name, address, phone number, and user ID are required' });
        }

        // Fetch the user based on user_id and role_id = 1
        let user;
        try {
            user = await UserModel.getUserByIdAndRole(user_id, 1);
            if (!user) {
                return res.status(404).json({ message: 'User with specified ID and role not found' });
            }
        } catch (fetchError) {
            console.error("Error fetching user by ID and role:", fetchError.message);
            return res.status(500).json({ message: "Failed to fetch user by ID and role", details: fetchError.message });
        }

        const email_clinic = user.email;

        // Proceed to create the clinic entry
        const result = await ClinicModel.createClinic(
            user_id,
            clinic_name,
            address_clinic,
            email_clinic,
            phone_clinic,
            link_whatsapp || '',
            link_telegram || '',
            link_instagram || '',
            link_youtube || '',
            link_facebook || '',
            link_twitter || '',
            link_tiktok || ''
        );

        res.status(201).json({
            data: { id: result.insertId, clinic_name, address_clinic, email_clinic, phone_clinic },
            message: 'Clinic created successfully'
        });
    } catch (err) {
        console.error('Error creating clinic:', err.message);
        res.status(500).json({ message: 'Error creating clinic', details: err.message });
    }
};






// Get all admins
exports.getAllClinics = async (req, res) => {
    try {
        const result = await ClinicModel.getAllClinics();
        res.status(200).json({ data: result });
    } catch (err) {
        res.status(500).json({ message: 'Error fetching admins' });
    }
};

// Get admin by ID
exports.getClinicById = async (req, res) => {
    const { id } = req.params;
    try {
        const result = await ClinicModel.getClinicById(id);
        if (result) {
            res.status(200).json(result);
        } else {
            res.status(404).json({ message: 'Admin not found' });
        }
    } catch (err) {
        res.status(500).json({ message: 'Error fetching admin by id' });
    }
};

// Update admin
exports.updateClinic = async (req, res) => {
    const { id } = req.params;
    const { clinic_name, email_clinic, phone_clinic, address_clinic, link_whatsapp, link_telegram, link_instagram, link_youtube, link_facebook, link_twitter, link_tiktok } = req.body;
    
    try {
        const result = await ClinicModel.updateClinic(id, clinic_name, email_clinic, phone_clinic, address_clinic, link_whatsapp, link_telegram, link_instagram, link_youtube, link_facebook, link_twitter, link_tiktok);
        res.status(200).json({ data: result });
    } catch (err) {
        console.error('Error updating admin:', err);
        res.status(500).json({ message: 'Error updating admin' });
    }
};



// Delete admin (mark as inactive)
exports.deleteClinic = async (req, res) => {
    const { id } = req.params;
    try {
        const result = await ClinicModel.deactivateClinic(id); // Method to update is_active to false
        if (result.affectedRows === 0) {
            return res.status(404).json({ message: 'Admin not found' });
        }
        res.status(200).json({ message: 'Admin deactivated successfully' });
    } catch (err) {
        console.error('Error deactivating admin:', err);
        res.status(500).json({ message: 'Error deactivating admin' });
    }
};
