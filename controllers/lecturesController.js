const fs = require('fs');
const path = require('path');
const ffmpeg = require('fluent-ffmpeg');
const sharp = require('sharp');
const LecturesModel = require('../models/lecturesModel');


// --- Where we store transcoded lecture videos ---
const LECTURE_UPLOAD_DIR = path.join(__dirname, "../public/uploads/lectures");

// Ensure upload dir exists on module load (non-fatal)
if (!fs.existsSync(LECTURE_UPLOAD_DIR)) {
    fs.mkdirSync(LECTURE_UPLOAD_DIR, { recursive: true });
}

/**
 * Get Multer video file regardless of whether single() or fields() was used.
 * Returns: { path, originalname, mimetype, size } | null
 */
function getUploadedVideoFile(req) {
    if (req.file) return req.file;
    if (req.files?.video?.[0]) return req.files.video[0];
    return null;
}

/**
 * Safe unlink wrapper (synchronous; tiny ops; logs but doesn't throw).
 */
function safeUnlink(filePath, label = "file") {
    try {
        if (filePath && fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
            console.log(`✓ Deleted ${label}: ${filePath}`);
        }
    } catch (err) {
        console.warn(`⚠ Failed to delete ${label} ${filePath}: ${err.message}`);
    }
}

/**
 * Transcode + compress to MP4/H.264/AAC (720p, streaming optimized).
 * Returns: Promise<string> absoluteOutputPath
 */
function transcodeToMp4720p(inputPath, outputAbsPath) {
    return new Promise((resolve, reject) => {
        ffmpeg(inputPath)
            .output(outputAbsPath)
            .videoCodec("libx264")
            .audioCodec("aac")
            .outputOptions([
                "-crf 28",              // quality/size tradeoff (23 better / larger; 30 smaller)
                "-preset slow",         // change to 'medium' / 'faster' if server CPU is weak
                "-movflags +faststart", // web playback w/o full download
                "-vf scale=-2:720"      // maintain aspect; max height 720
            ])
            .on("end", () => resolve(outputAbsPath))
            .on("error", (err) => reject(err))
            .run();
    });
}


// ✅ Get all lectures (categories and sub-categories)
exports.getAllLectures = async (req, res) => {
    try {
        const lectures = await LecturesModel.getAllLectures();
        res.status(200).json({ data: lectures });
    } catch (err) {
        console.error('Error fetching lectures:', err.message);
        res.status(500).json({ error: 'Failed to fetch lectures' });
    }
};

// ✅ Create a new main lecture (category)
exports.createLecture = async (req, res) => {
    try {
        const { text, description, type } = req.body;

        if (!type || !['category', 'sub_category'].includes(type)) {
            return res.status(400).json({ error: 'Invalid type. Must be "category" or "sub_category".' });
        }

        if (!req.file) {
            return res.status(400).json({ error: 'File is required' });
        }

        const inputPath = req.file.path;
        const outputFilename = `${Date.now()}-${Math.random().toString(36).substring(2)}.webp`;
        const outputPath = path.join(__dirname, '../public/uploads/lectures', outputFilename);

        // Ensure directory exists
        if (!fs.existsSync(path.dirname(outputPath))) {
            fs.mkdirSync(path.dirname(outputPath), { recursive: true });
        }

        // Convert to WebP using Sharp
        await sharp(inputPath)
            .webp({ quality: 80 })
            .toFile(outputPath);

        // Delete original uploaded file
        fs.unlinkSync(inputPath);

        const imagePath = `/uploads/lectures/${outputFilename}`;
        const lectureId = await LecturesModel.createLecture(
            text.trim(),
            description?.trim(),
            type,
            imagePath
        );

        res.status(201).json({
            message: 'Lecture created successfully',
            lectureId,
            imagePath,
        });
    } catch (err) {
        console.error('Error creating lecture:', err.message);
        res.status(500).json({
            error: 'Failed to create lecture',
            details: err.message,
        });
    }
};

// ✅ Create sub-lecture
exports.createSubLecture = async (req, res) => {
    let outputAbsPath = null; // track to clean up if error

    try {
        // --------- Validate ID ---------
        const lectureId = Number(req.params.id);
        if (!lectureId || isNaN(lectureId)) {
            return res.status(400).json({ error: "Invalid lecture ID." });
        }

        // --------- Extract & sanitize fields ---------
        const text = (req.body.text || "").trim();
        const description = (req.body.description || "").trim();
        const type = (req.body.type || "sub_category").trim();

        if (!text) {
            return res.status(400).json({ error: "Text is required." });
        }
        if (!["category", "sub_category"].includes(type)) {
            return res.status(400).json({ error: 'Invalid type. Must be "category" or "sub_category".' });
        }

        // --------- Uploaded video file? ---------
        const uploaded = getUploadedVideoFile(req);
        if (!uploaded) {
            return res.status(400).json({ error: "Missing video file." });
        }
        const inputPath = uploaded.path;

        // --------- Build output path ---------
        const outputFileName = `${Date.now()}-${Math.random().toString(36).slice(2)}.webm`;
        outputAbsPath = path.join(LECTURE_UPLOAD_DIR, outputFileName);

        // --------- Transcode ---------
        await transcodeToMp4720p(inputPath, outputAbsPath);

        // Remove temp upload
        safeUnlink(inputPath, "temp upload");

        const videoRelPath = `/uploads/lectures/${outputFileName}`;

        // --------- Save DB ---------
        const result = await LecturesModel.createSubLecture({
            lectureId,
            text,
            description: description || null,
            type,
            video: videoRelPath,
        });

        return res.status(201).json({
            message: "Sub-lecture created successfully.",
            data: result,
            video: videoRelPath,
        });

    } catch (err) {
        console.error("❌ Error creating sub-lecture:", err);
        // Remove transcoded output if it exists but DB failed
        if (outputAbsPath) safeUnlink(outputAbsPath, "output video");
        return res.status(500).json({
            error: "Failed to create sub-lecture",
            details: err.message,
        });
    }
};


// ✅ Get single lecture by ID
exports.getLectureById = async (req, res) => {
    try {
        const lecture = await LecturesModel.getLectureById(req.params.id);
        if (!lecture) return res.status(404).json({ error: 'Lecture not found' });
        res.status(200).json({ data: lecture });
    } catch (err) {
        res.status(500).json({ error: 'Failed to fetch lecture', details: err.message });
    }
};

exports.getSubLecturesByLectureText = async (req, res) => {
    let { text } = req.params;

    try {
        text = decodeURIComponent(text);

        if (!text) {
            return res.status(400).json({ success: false, error: 'Text parameter is required' });
        }

        const subLectures = await LecturesModel.getSubLecturesByLectureText(text);

        if (subLectures.length === 0) {
            return res.status(404).json({ success: false, message: 'No sub-lectures found for the provided text' });
        }

        res.status(200).json({ success: true, data: subLectures });
    } catch (err) {
        res.status(500).json({
            success: false,
            error: 'Failed to fetch sub-lectures',
            details: err.message,
        });
    }
};


// ✅ Get sub-lectures by lecture ID
exports.getSubLecturesByLectureId = async (req, res) => {
    try {
        const subLectures = await LecturesModel.getSubLecturesByLectureId(req.params.id);
        res.status(200).json({ data: subLectures || [] });
    } catch (err) {
        res.status(500).json({ error: 'Failed to fetch sub-lectures', details: err.message });
    }
};

// ✅ Update lecture
exports.updateLecture = async (req, res) => {
    const { id } = req.params;
    const { text, description } = req.body;

    try {
        const lecture = await LecturesModel.getLectureById(id);
        if (!lecture) return res.status(404).json({ error: 'Lecture not found' });

        let lecturePath = lecture.image;

        if (req.file) {
            // Remove old file if it exists
            const oldPath = path.join(__dirname, '../public', lecture.image);
            if (fs.existsSync(oldPath)) fs.unlinkSync(oldPath);

            // Create new file name and path
            const newFileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.webp`;
            const outputPath = path.join(__dirname, '../public/uploads/lectures', newFileName);

            // Ensure the directory exists
            if (!fs.existsSync(path.dirname(outputPath))) {
                fs.mkdirSync(path.dirname(outputPath), { recursive: true });
            }

            // Convert uploaded file to WebP
            await sharp(req.file.path)
                .webp({ quality: 80 })
                .toFile(outputPath);

            // Remove the original uploaded file
            fs.unlinkSync(req.file.path);

            lecturePath = `/uploads/lectures/${newFileName}`;
        }

        // Update lecture in database
        await LecturesModel.updateLecture(id, text, description, lecturePath);

        res.status(200).json({
            message: 'Lecture updated successfully',
            path: lecturePath
        });
    } catch (err) {
        console.error('Error updating lecture:', err.message);
        res.status(500).json({ error: 'Failed to update lecture', details: err.message });
    }
};

// ✅ Update sub-lecture
exports.updateSubLecture = async (req, res) => {
    const subLectureId = Number(req.params.id || req.body.id);
    if (!subLectureId || isNaN(subLectureId)) {
        return res.status(400).json({ error: "Sub-lecture ID is required." });
    }

    try {
        // --------- Load existing ---------
        const existing = await LecturesModel.getLectureById(subLectureId);
        if (!existing) {
            return res.status(404).json({ error: "Sub-lecture not found." });
        }

        // --------- Sanitize & fallback to existing values (if not sent) ---------
        const text = (req.body.text ?? existing.text ?? "").trim();
        const description = (req.body.description ?? existing.description ?? "").trim();
        const type = (req.body.type ?? existing.type ?? "sub_category").trim();

        if (!text) {
            return res.status(400).json({ error: "Text is required." });
        }
        if (!["category", "sub_category"].includes(type)) {
            return res.status(400).json({ error: 'Invalid type. Must be "category" or "sub_category".' });
        }

        let videoRelPath = existing.image; // keep current
        let newOutputAbsPath = null;       // track for cleanup

        // --------- If new video uploaded ---------
        const uploaded = getUploadedVideoFile(req);
        if (uploaded) {
            const inputPath = uploaded.path;

            // Build new output path
            const outputFileName = `${Date.now()}-${Math.random().toString(36).slice(2)}.mp4`;
            newOutputAbsPath = path.join(LECTURE_UPLOAD_DIR, outputFileName);

            // Transcode new file
            await transcodeToMp4720p(inputPath, newOutputAbsPath);

            // Remove temp upload
            safeUnlink(inputPath, "temp upload");

            // Only now (after success) delete old stored video
            if (existing.image) {
                const oldAbsPath = path.join(__dirname, "../public", existing.image);
                safeUnlink(oldAbsPath, "old video");
            }

            videoRelPath = `/uploads/lectures/${outputFileName}`;
        }

        // --------- Update DB ---------
        const result = await LecturesModel.updateSubLecture(
            subLectureId,
            text,
            description,
            type,
            videoRelPath
        );

        return res.status(200).json({
            message: "Sub-lecture updated successfully.",
            data: result,
            video: videoRelPath,
        });

    } catch (err) {
        console.error("❌ Error updating sub-lecture:", err);
        // If transcode succeeded but DB failed, clean up the newly created file to avoid orphan.
        if (newOutputAbsPath) safeUnlink(newOutputAbsPath, "new output video (rollback)");
        return res.status(500).json({
            error: "Failed to update sub-lecture",
            details: err.message,
        });
    }
};


// ✅ Update status (active/inactive)
exports.updateLectureStatus = async (req, res) => {
    const { id } = req.params;
    const { is_active } = req.body;

    try {
        await LecturesModel.updateLectureStatus(id, is_active);
        res.status(200).json({ success: true });
    } catch (err) {
        console.error('Error in updateLectureStatus controller:', err.message);
        res.status(500).json({ error: 'Update failed' });
    }
};


// ✅ Delete lecture
exports.deleteLecture = async (req, res) => {
    try {
        const lecture = await LecturesModel.getLectureById(req.params.id);
        if (!lecture) return res.status(404).json({ error: 'Lecture not found' });

        if (lecture.image) {
            const filePath = path.join(__dirname, '../public', lecture.image);
            if (fs.existsSync(filePath)) fs.unlinkSync(filePath);
        }

        await LecturesModel.deleteLecture(req.params.id);
        res.status(200).json({ message: 'Lecture deleted successfully' });
    } catch (err) {
        res.status(500).json({ error: 'Failed to delete lecture', details: err.message });
    }
};

// ✅ Delete sub-lecture
exports.deleteSubLecture = async (req, res) => {
    try {
        const subLecture = await LecturesModel.getLectureById(req.params.id);
        if (!subLecture) return res.status(404).json({ error: 'Sub-lecture not found' });

        if (subLecture.image) {
            const filePath = path.join(__dirname, '../public', subLecture.image);
            if (fs.existsSync(filePath)) fs.unlinkSync(filePath);
        }

        await LecturesModel.deleteSubLecture(req.params.id);
        res.status(200).json({ message: 'Sub-lecture deleted successfully' });
    } catch (err) {
        res.status(500).json({ error: 'Failed to delete sub-lecture', details: err.message });
    }
};
