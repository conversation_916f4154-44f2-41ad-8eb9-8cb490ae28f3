const Thought = require('../models/thoughtModel'); // Assumes Thought is your Thought model
const ffmpeg = require('fluent-ffmpeg');
const path = require('path');
const fs = require('fs');

// Create a new thought
exports.createThought = async (req, res) => {
    const { content, concern_id, belongs_to } = req.body;

    // Validate required fields
    if (!content || !concern_id) {
        console.error("Validation Error: Missing required fields.");
        return res.status(400).json({ error: 'Both content and concern_id are required.' });
    }

    try {
        let audioUrl = null;
        let duration = null;

        if (req.file) {
            const uploadDir = path.join(__dirname, '../public/uploads/thoughts');

            // Ensure the upload directory exists
            if (!fs.existsSync(uploadDir)) {
                fs.mkdirSync(uploadDir, { recursive: true });
            }

            const originalPath = req.file.path; // Full path provided by <PERSON>lter
            const webmFilename = `${Date.now()}-${Math.round(Math.random() * 1e9)}.webm`;
            const webmPath = path.join(uploadDir, webmFilename);

            // Convert audio file to .webm and calculate duration
            await new Promise((resolve, reject) => {
                ffmpeg(originalPath)
                    .toFormat('webm')
                    .on('codecData', (data) => {
                        duration = data.duration;
                    })
                    .on('end', () => {
                        // Remove the original uploaded file
                        fs.unlinkSync(originalPath);

                        // Set the .webm file path
                        audioUrl = `/uploads/thoughts/${webmFilename}`;
                        resolve();
                    })
                    .on('error', (err) => {
                        reject(err);
                    })
                    .save(webmPath);
            });
        }

        // Create the thought in the database
        const thoughtId = await Thought.create({
            content,
            concern_id,
            audio_url: audioUrl,
            duration,
            belongs_to,
        });

        res.status(201).json({ message: 'Thought created successfully.', thoughtId });
    } catch (error) {
        console.error('Error creating thought:', error.message);
        res.status(500).json({ error: 'Failed to create thought.', details: error.message });
    }
};



// Get a thought by concern ID
exports.getById = async (req, res) => {
    const { id } = req.params;

    if (!id) {
        return res.status(400).json({ error: 'Concern ID is required.' });
    }

    try {
        const thoughts = await Thought.getByConcernId(id);

        if (!thoughts || thoughts.length === 0) {
            return res.status(404).json({ error: `No thoughts found for concern ID ${id}.` });
        }

        res.status(200).json(thoughts);
    } catch (error) {
        console.error(`Error fetching thoughts for concern ID ${id}:`, error.message);
        res.status(500).json({ error: 'Failed to retrieve thoughts.', details: error.message });
    }
};

// Get all thoughts
exports.getAllThoughts = async (req, res) => {
    try {
        const thoughts = await Thought.getAll();
        res.status(200).json(thoughts);
    } catch (error) {
        console.error('Error fetching all thoughts:', error.message);
        res.status(500).json({ error: 'Failed to retrieve thoughts.', details: error.message });
    }
};

exports.getAudioByThoughtId = async (req, res) => {
    const { id } = req.params;

    if (!id) {
        return res.status(400).json({ error: 'Thought ID is required.' });
    }

    try {
        const audio = await Thought.getAudioByThoughtId(id);

        if (!audio) {
            return res.status(404).json({ error: `No audio found for thought ID ${id}.` });
        }

        res.status(200).json({ id: id, audio: audio });
    } catch (error) {
        console.error(`Error fetching audio for thought ID ${id}:`, error.message);
        res.status(500).json({ error: 'Failed to retrieve audio.', details: error.message });

    }
}

// controllers/thought.controller.js
exports.getTermentByBelongsTo = async (req, res) => {
    try {
        // Pull and normalize the param
        let { belongs_to } = req.params;
        belongs_to = decodeURIComponent((belongs_to || '').trim());

        if (!belongs_to) {
            return res.status(400).json({ error: "belongs_to is required." });
        }

        // Query
        const videos = await Thought.getByBelongsTo(belongs_to);

        if (!videos || videos.length === 0) {
            return res.status(404).json({
                message: `No videos found for belongs_to "${belongs_to}".`
            });
        }

        return res.status(200).json(videos);
    } catch (error) {
        console.error(`Error fetching videos for belongs_to "${req.params.belongs_to}":`, error);
        return res.status(500).json({
            error: "Failed to retrieve videos.",
            details: error.message
        });
    }
};

// Update a thought by ID
exports.updateThought = async (req, res) => {
    const { id } = req.params;
    const { content, belongs_to } = req.body;

    if (!content) {
        return res.status(400).json({ error: 'Content is required for update.' });
    }

    try {
        const existingThought = await Thought.getById(id);

        if (!existingThought) {
            return res.status(404).json({ error: 'Thought not found.' });
        }

        let audioUrl = existingThought.audio_url || null;
        let duration = existingThought.duration || null;


        if (req.file) {
            const originalPath = req.file.path; // Full path provided by Multer

            if (!fs.existsSync(originalPath)) {
                return res.status(404).json({ error: 'Uploaded file not found.' });
            }

            const uploadDir = path.join(__dirname, '../public/uploads/thoughts');
            if (!fs.existsSync(uploadDir)) {
                fs.mkdirSync(uploadDir, { recursive: true });
            }

            const webmFilename = `${Date.now()}-${Math.round(Math.random() * 1e9)}.webm`;
            const webmPath = path.join(uploadDir, webmFilename);
            await new Promise((resolve, reject) => {
                ffmpeg(originalPath)
                    .toFormat('webm')
                    .on('start', () => {
                        console.log('Conversion started');
                    })
                    .on('codecData', (data) => {
                        duration = data.duration;
                    })
                    .on('end', () => {
                        fs.unlinkSync(originalPath); // Delete the original file after successful conversion
                        resolve();
                    })
                    .on('error', (err) => {
                        console.error('FFmpeg Error:', err.message);
                        reject(err);
                    })
                    .save(webmPath);
            });

            audioUrl = `/uploads/thoughts/${webmFilename}`;

            if (existingThought.audio_url) {
                const oldAudioPath = path.join(__dirname, '../public', existingThought.audio_url);

                if (fs.existsSync(oldAudioPath)) {
                    fs.unlinkSync(oldAudioPath);
                }
            }
        }



        const updated = await Thought.updateById(id, { content, audio_url: audioUrl, duration, belongs_to });

        if (!updated) {
            return res.status(404).json({ error: 'Thought not found or not updated.' });
        }

        res.status(200).json({
            message: 'Thought updated successfully.',
            updatedThought: { content, audio_url: audioUrl, duration, belongs_to },
        });
    } catch (error) {
        res.status(500).json({
            error: 'Failed to update thought.',
            details: error.message,
        });
    }
};



// Delete a thought by ID
exports.deleteThought = async (req, res) => {
    const { id } = req.params;

    try {
        const deleted = await Thought.delete(id);

        if (!deleted) {
            return res.status(404).json({ error: 'Thought not found.' });
        }

        res.status(200).json({ message: 'Thought deleted successfully.' });
    } catch (error) {
        console.error(`Error deleting thought with ID ${id}:`, error.message);
        res.status(500).json({ error: 'Failed to delete thought.', details: error.message });
    }
};
