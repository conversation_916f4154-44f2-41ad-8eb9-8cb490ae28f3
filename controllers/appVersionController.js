const AppVersionModel = require('../models/appVersionModel');

exports.createVersion = async (req, res) => {
  try {
    const { platform, version, change_log, is_force_update } = req.body;

    if (!platform || !version) {
      return res.status(400).json({ error: 'Platform and version are required' });
    }

    const response= await AppVersionModel.create(platform, version, change_log, is_force_update);
    res.status(201).json({ message: 'App version created', data: response });
  } catch (error) {
    console.error('Create version error:', error.message);
    res.status(500).json({ error: 'Failed to create app version' });
  }
};

exports.updateVersion = async (req, res) => {
  try {
    const { id } = req.params;
    const { platform, version, change_log, is_force_update } = req.body;

    if (!id || !platform || !version) {
      return res.status(400).json({ error: 'ID, platform, and version are required' });
    }

    const result = await AppVersionModel.update(id, platform, version, change_log, is_force_update);
    if (result === 0) {
      return res.status(404).json({ error: 'Version not found or not updated' });
    }

    // get updated version
    const updatedVersion = await AppVersionModel.getById(id);
    if (!updatedVersion) {
      return res.status(404).json({ error: 'Version not found' });
    }

    res.status(200).json({ data: updatedVersion, message: 'App version updated' });
  } catch (error) {
    console.error('Update version error:', error.message);
    res.status(500).json({ error: 'Failed to update app version' });
  }
};

exports.deleteVersion = async (req, res) => {
  try {
    const { id } = req.params;
    const result = await AppVersionModel.deleteById(id);

    if (result === 0) {
      return res.status(404).json({ error: 'Version not found' });
    }

    res.status(200).json({ message: 'Version deleted successfully' });
  } catch (error) {
    console.error('Delete version error:', error.message);
    res.status(500).json({ error: 'Failed to delete version' });
  }
};

exports.getAllVersions = async (req, res) => {
  try {
    const versions = await AppVersionModel.getAll();
    res.status(200).json({ data: versions });
  } catch (error) {
    console.error('Fetch all versions error:', error.message);
    res.status(500).json({ error: 'Failed to fetch app versions' });
  }
};
