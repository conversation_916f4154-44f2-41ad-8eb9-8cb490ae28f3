const PaymentMethodModel = require("../models/paymentModel");

exports.getAllPaymentMethods = async (req, res) => {
    try {
        const paymentMethods = await PaymentMethodModel.getAll();
        res.status(200).json(paymentMethods);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
};

exports.getPaymentMethodById = async (req, res) => {
    const { id } = req.params;
    try {
        const paymentMethod = await PaymentMethodModel.getById(id);
        res.status(200).json(paymentMethod);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
};

exports.createPaymentMethod = async (req, res) => {
    const { code,
            display_name,
            is_active } = req.body;
    try {
        const paymentMethod = await PaymentMethodModel.create({ code, display_name, is_active });
        res.status(201).json(paymentMethod);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
};

exports.updatePaymentMethod = async (req, res) => {
    const { id } = req.params;
    const { code,
            display_name } = req.body;
    try {
        const paymentMethod = await PaymentMethodModel.update(id, code, display_name);
        res.status(200).json(paymentMethod);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
};

exports.deletePaymentMethod = async (req, res) => {
    const { id } = req.params;
    try {
        const paymentMethod = await PaymentMethodModel.delete(id);
        res.status(200).json(paymentMethod);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
};