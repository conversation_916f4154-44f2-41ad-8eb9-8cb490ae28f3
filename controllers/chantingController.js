const ChantingModel = require('../models/chantingModel');

// Create a new chanting entry
// Create a new chanting entry with chant count
exports.createChanting = async (req, res) => {
    const { user_id, question_id } = req.body;

    if (!user_id || !question_id) {
        return res.status(400).json({ success: false, message: "User ID and Question ID are required." });
    }

    try {
        // Check if the question exists
        const isQuestionExist = await ChantingModel.checkIfQuestionExists(question_id);
        if (!isQuestionExist) {
            return res.status(400).json({ success: false, message: "Invalid Question ID, question does not exist." });
        }

        // Create new chanting and get the result
        const result = await ChantingModel.createUserChanting(user_id, question_id);

        res.status(201).json({
            data: result,
            message: "Chanting created successfully."
        });
    } catch (error) {
        console.error("❌ Error creating chanting:", error.message);
        res.status(500).json({ success: false, message: "Failed to create chanting", error: error.message });
    }
};




// Get all active chantings for a user
exports.getUserChantings = async (req, res) => {
    const { user_id } = req.params;

    if (!user_id) {
        return res.status(400).json({ success: false, message: "User ID is required." });
    }

    try {
        const chantings = await ChantingModel.getUserChantings(user_id);  // Get chantings from the model

        if (!chantings || chantings.length === 0) {
            return res.status(404).json({ success: false, message: "No active chanting records found." });
        }

        res.status(200).json({ data: chantings });
    } catch (error) {
        console.error("❌ Error fetching chantings:", error.message);
        res.status(500).json({ success: false, message: "Failed to retrieve chantings", error: error.message });
    }
};



// Delete chanting (hard delete)
exports.deleteChanting = async (req, res) => {
    const { id } = req.params;

    try {
        const result = await ChantingModel.deleteChanting(id);
        if (!result.success) {
            return res.status(404).json({ success: false, message: "Chanting not found or already deleted." });
        }
        res.status(200).json({ success: true, message: "Chanting deleted successfully." });
    } catch (error) {
        console.error(`❌ Error deleting chanting with ID ${id}:`, error.message);
        res.status(500).json({ success: false, message: "Failed to delete chanting", error: error.message });
    }
};
