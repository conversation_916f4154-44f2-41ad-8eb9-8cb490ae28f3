// notiCatController.js
const path = require("path");
const fs = require("fs");
const sharp = require("sharp");
const ffmpeg = require("fluent-ffmpeg");
const VideosModel = require("../models/videosModel");



// Controller to get all notification categories
exports.getAllVideos = async (req, res) => {
    try {
        const videos = await VideosModel.getAllVideos();
        res.status(200).json(videos);
    } catch (err) {
        console.error('Error fetching notification categories:', err.message);
        res.status(500).json({ error: 'Failed to fetch notification categories' });
    }
};

// Controller to create a new notification category

exports.createVideo = async (req, res) => {
    try {
        const { text = "", description = "", price = 0 } = req.body;

        // Validate input
        if (!text.trim()) {
            return res.status(400).json({ error: 'Text is required' });
        }

        if (!price.trim()) {
            return res.status(400).json({ error: 'Price is required' });
        }

        if (!req.file) {
            return res.status(400).json({ error: 'File is required' });
        }

        // Ensure upload directory exists
        const uploadDir = path.join(__dirname, "../public/uploads/treatment");
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir, { recursive: true });
        }

        // Convert the uploaded image to webp format
        const webpFilename = `${Date.now()}-${Math.round(Math.random() * 1e9)}.webp`;
        const webpPath = path.join(uploadDir, webpFilename);

        await sharp(req.file.path)
            .toFormat("webp")
            .toFile(webpPath);

        // Remove original file
        fs.unlinkSync(req.file.path);

        const imagePath = `/uploads/treatment/${webpFilename}`;

        // Save as a category (not a video item)
        const categoryId = await VideosModel.createCategory(
            text.trim(),
            description?.trim() || null,
            imagePath,
            price
        );

        res.status(201).json({
            message: "Category created successfully",
            id: categoryId,
            imagePath
        });
    } catch (err) {
        console.error("Error creating category:", err);
        res.status(500).json({
            error: "Failed to create category",
            details: err.message
        });
    }
};


// Controller to get a notification category by ID
exports.getVideoById = async (req, res) => {
    const { id } = req.params;
    try {
        const video = await VideosModel.getVideoById(id);
        if (!video) {
            return res.status(404).json({ message: 'Video not found' });
        }
        res.status(200).json({ data: video });
    } catch (err) {
        console.error(`Error fetching video with ID ${id}:`, err.message);
        res.status(500).json({ error: 'Failed to fetch video by ID', details: err.message });
    }
};


// Controller to update 
exports.updateVideo = async (req, res) => {
    const { id } = req.params;
    const { text, description, price } = req.body;

    try {
        const existingVideo = await VideosModel.getVideoById(id);
        if (!existingVideo) {
            return res.status(404).json({ message: 'Video not found' });
        }

        let newImagePath = existingVideo.image;

        if (req.file) {
            const oldImagePath = path.join(__dirname, '../public', existingVideo.image);
            if (fs.existsSync(oldImagePath)) {
                fs.unlinkSync(oldImagePath);
            }

            const uploadDir = path.join(__dirname, '../public/uploads/treatment');
            if (!fs.existsSync(uploadDir)) {
                fs.mkdirSync(uploadDir, { recursive: true });
            }

            const newFilename = `${Date.now()}-${Math.round(Math.random() * 1e9)}.webp`;
            const outputPath = path.join(uploadDir, newFilename);

            await sharp(req.file.path)
                .toFormat('webp')
                .toFile(outputPath);

            fs.unlinkSync(req.file.path);

            newImagePath = `/uploads/treatment/${newFilename}`;
        }

        await VideosModel.updateCategory(id, text, description, price, newImagePath);

        res.status(200).json({ message: 'Video updated successfully', videoPath: newImagePath });
    } catch (err) {
        console.error('Error updating video:', err.message);
        res.status(500).json({ error: 'Failed to update video', details: err.message });
    }
};


// controller to update the is active status of a video
exports.updateVideoStatus = async (req, res) => {
    const { id } = req.params;
    const { isActive } = req.body;

    if (typeof isActive !== 'boolean') {
        return res.status(400).json({ error: 'Invalid isActive value' });
    }

    try {
        const result = await VideosModel.activateVideo(id, isActive);
        res.status(200).json({ message: 'Video status updated successfully', result });
    } catch (err) {
        console.error(`Error updating video status for video ID ${id}:`, err.message);
        res.status(500).json({ error: 'Failed to update video status', details: err.message });
    }
};


exports.getTreatmentVideo = async (req, res) => {
    const { id } = req.params;
    try {
        const video = await VideosModel.getVideoById(id);
        if (!video) {
            return res.status(404).json({ message: 'Video not found' });
        }
        res.status(200).json({ data: video });
    } catch (err) {
        console.error(`Error fetching video with ID ${id}:`, err.message);
        res.status(500).json({ error: 'Failed to fetch video by ID', details: err.message });
    }
};

// Create SubVideo
exports.createSubVideo = async (req, res) => {
  const { id } = req.params; // video_id
  const { title } = req.body;

  if (!id || !title) {
    return res.status(400).json({ error: 'Video ID and title are required.' });
  }

  try {
    const insertedId = await VideosModel.createSubVideo(title, id);
    res.status(201).json({ message: 'Sub-video created successfully', id: insertedId });
  } catch (err) {
    console.error("Error creating sub-video:", err.message);
    res.status(500).json({ error: "Failed to create sub-video", details: err.message });
  }
};


exports.getSubVideosByVideoId = async (req, res) => {
    const { id } = req.params;

    if (!id) {
        return res.status(400).json({ error: "Video ID is required." });
    }

    try {
        // Fetch subcategories for the given video
        const subVideos = await VideosModel.getSubVideosByVideoId(id);
        // Fetch items under this subcategory (if needed)
        const itemVideos = await VideosModel.getItemsBySubCategoryId(id);
        // Merge both arrays
        const allVideos = [...subVideos, ...itemVideos];

        if (!allVideos || allVideos.length === 0) {
            return res.status(200).json({ data: [] }); // empty array if no videos
        }

        res.status(200).json({ data: allVideos });
    } catch (err) {
        res.status(500).json({ error: "Failed to fetch videos", details: err.message });
    }
};



exports.getSubVideosByVideoText = async (req, res) => {
    let { text } = req.params;

    // Decode the encoded text parameter
    try {
        text = decodeURIComponent(text);

        if (!text) {
            return res.status(400).json({ success: false, error: 'Text parameter is required' });
        }

        const subVideos = await VideosModel.getSubCategoryByText(text);

        if (subVideos.length === 0) {
            return res.status(404).json({ success: false, message: 'No sub-videos found for the provided text' });
        }

        res.status(200).json({ success: true, data: subVideos });
    } catch (err) {
        res.status(500).json({ success: false, error: 'Failed to fetch sub-videos', details: err.message });
    }
};


// Update SubVideo
exports.updateSubVideo = async (req, res) => {
  const { id } = req.params;
  const { text } = req.body; // use "text" instead of "title"

  if (!id) {
    return res.status(400).json({ error: "Sub-video ID is required." });
  }
  if (!text) {
    return res.status(400).json({ error: "Text (title) is required." });
  }

  try {
    const existingSubVideo = await VideosModel.getSubVideoById(id);
    if (!existingSubVideo) {
      return res.status(404).json({ error: "Sub-video not found." });
    }

    await VideosModel.updateSubVideo(id, text);

    res.status(200).json({
      message: "Sub-video title updated successfully.",
      id,
      text
    });
  } catch (err) {
    console.error("Error updating sub-video:", err.message);
    res.status(500).json({
      error: "Failed to update sub-video",
      details: err.message
    });
  }
};


exports.deleteSubCategory = async (req, res) => {
    const { id } = req.params;

    if (!id) {
        return res.status(400).json({ error: 'Missing ID.' });
    }

    try {
        const video = await VideosModel.getVideoById(id);
        if (!video || video.type !== 'sub_category') {
            return res.status(404).json({ error: 'sub_category not found.' });
        }

        const deleted = await VideosModel.deleteSubCategoryById(id);
        if (!deleted) {
            return res.status(500).json({ error: 'Failed to delete sub_category from database.' });
        }

        res.status(200).json({ message: 'sub_category deleted successfully.' });
    } catch (err) {
        console.error('Error deleting sub_category:', err.message);
        res.status(500).json({ error: 'Internal server error', details: err.message });
    }
};

//   ==================== item treatment by vedeo id ==================

// Get all items by video id where type is item
exports.getItemsBySubCategoryId = async (req, res) => {
    const { id } = req.params;
    if (!id) return res.status(400).json({ error: "Video ID is required" });

    try {
        const items = await VideosModel.getItemsBySubCategoryId(id);
        res.status(200).json({ data: items });
    } catch (err) {
        console.error("Error fetching items:", err.message);
        res.status(500).json({ error: "Failed to fetch items", details: err.message });
    }
};

const safeUnlink = (file) => {
    try {
        if (fs.existsSync(file)) fs.unlinkSync(file);
    } catch (err) {
        console.warn("Failed to delete:", file, err.message);
    }
};

exports.createItem = async (req, res) => {
    try {
        const { text, type } = req.body;
        const video_id = parseInt(req.body.video_id, 10);

        if (!text?.trim()) return res.status(400).json({ error: "Text is required" });
        if (!type || typeof type !== "string") return res.status(400).json({ error: "Type is required" });
        if (isNaN(video_id)) return res.status(400).json({ error: "Invalid video_id" });

        const uploadDir = path.join(__dirname, "../public/uploads/treatment");
        if (!fs.existsSync(uploadDir)) fs.mkdirSync(uploadDir, { recursive: true });

        let imagePath = null;
        let filePath = null;

        // 🎥 Video conversion task
        const videoTask = async () => {
            const video = req.files?.image?.[0];
            if (video) {
                const webmFilename = `${Date.now()}-${Math.random().toString(36).slice(2, 8)}.webm`;
                const webmPath = path.join(uploadDir, webmFilename);

                await new Promise((resolve, reject) => {
                    ffmpeg(video.path)
                        .output(webmPath)
                        .videoCodec("libvpx")
                        .audioCodec("libvorbis")
                        .outputOptions([
                            "-crf", "35",
                            "-cpu-used", "8",
                            "-deadline", "realtime",
                            "-threads", "2"
                        ])
                        .on("end", resolve)
                        .on("error", (err, stdout, stderr) => {
                            console.error("FFmpeg error:", err.message, stderr);
                            reject(err);
                        })
                        .run();
                });

                imagePath = `/uploads/treatment/${webmFilename}`;
                safeUnlink(video.path);
            }
        };

        // 📁 File saving task
        const fileTask = async () => {
            const file = req.files?.file?.[0];
            if (file) {
                const filename = `${Date.now()}-${Math.random().toString(36).slice(2, 8)}${path.extname(file.originalname)}`;
                const destPath = path.join(uploadDir, filename);
                fs.renameSync(file.path, destPath);
                filePath = `/uploads/treatment/${filename}`;
            }
        };

        // ⏱️ Run tasks in parallel if needed
        const tasks = [];
        if (req.files?.image?.[0]) tasks.push(videoTask());
        if (req.files?.file?.[0]) tasks.push(fileTask());
        await Promise.all(tasks);

        // Save to DB
        const itemId = await VideosModel.createItem({
            text: text.trim(),
            type: type.trim(),
            video_id,
            image: imagePath,
            file: filePath,
        });

        return res.status(201).json({
            message: "Item created successfully",
            id: itemId,
            image: imagePath,
            file: filePath,
        });

    } catch (err) {
        console.error("Error creating item:", err);
        return res.status(500).json({ error: "Failed to create item", details: err.message });
    }
};

exports.updateItem = async (req, res) => {
    const { id } = req.params;
    const { text } = req.body;

    if (!id || !text?.trim()) {
        return res.status(400).json({ error: "ID and valid text are required" });
    }

    const uploadDir = path.join(__dirname, "../public/uploads/treatment");
    if (!fs.existsSync(uploadDir)) fs.mkdirSync(uploadDir, { recursive: true });

    let newImagePath = null;
    let newFilePath = null;

    // ✅ Get existing item
    let oldItem;
    try {
        oldItem = await VideosModel.getItemById(id);
        if (!oldItem) return res.status(404).json({ error: "Item not found" });
    } catch (err) {
        console.error("DB error:", err);
        return res.status(500).json({ error: "Database error", details: err.message });
    }

    // 🎥 Handle video upload + conversion
    if (req.files?.image?.[0]) {
        const inputVideo = req.files.image[0].path;
        const webmFilename = `${Date.now()}-${Math.random().toString(36).slice(2, 8)}.webm`;
        const webmPath = path.join(uploadDir, webmFilename);

        try {
            await new Promise((resolve, reject) => {
                ffmpeg(inputVideo)
                    .output(webmPath)
                    .videoCodec("libvpx")
                    .audioCodec("libvorbis")
                    .outputOptions(["-crf", "35", "-cpu-used", "8", "-deadline", "realtime", "-threads", "2"])
                    .on("end", () => {
                        safeUnlink(inputVideo);
                        resolve();
                    })
                    .on("error", (err) => {
                        console.error("FFmpeg error:", err.message);
                        reject(err);
                    })
                    .run();
            });

            newImagePath = `/uploads/treatment/${webmFilename}`;

            // ❌ Delete old video if exists
            if (oldItem.image) safeUnlink(path.join(__dirname, "../public", oldItem.image));

        } catch (err) {
            console.error("Video conversion failed:", err.message);
            return res.status(500).json({ error: "Video conversion failed", details: err.message });
        }
    }

    // 📁 Handle file upload
    if (req.files?.file?.[0]) {
        const file = req.files.file[0];
        const filename = `${Date.now()}-${Math.random().toString(36).slice(2, 8)}${path.extname(file.originalname)}`;
        const destPath = path.join(uploadDir, filename);
        fs.renameSync(file.path, destPath);
        newFilePath = `/uploads/treatment/${filename}`;

        // ❌ Delete old file if exists
        if (oldItem.file) safeUnlink(path.join(__dirname, "../public", oldItem.file));
    }

    // ❌ Remove old image if cleared and no new image
    if (!req.files?.image?.[0] && oldItem.image && req.body.clearImage === 'true') {
        safeUnlink(path.join(__dirname, "../public", oldItem.image));
        newImagePath = null;
    }

    // ❌ Remove old file if cleared and no new file
    if (!req.files?.file?.[0] && oldItem.file && req.body.clearFile === 'true') {
        safeUnlink(path.join(__dirname, "../public", oldItem.file));
        newFilePath = null;
    }

    // ✅ Update DB
    try {
        await VideosModel.updateItem(id, {
            text: text.trim(),
            image: newImagePath !== null ? newImagePath : oldItem.image,
            file: newFilePath !== null ? newFilePath : oldItem.file,
        });

        return res.status(200).json({ message: "Item updated successfully" });
    } catch (err) {
        console.error("Update DB failed:", err.message);
        return res.status(500).json({ error: "Failed to update item", details: err.message });
    }
};

// Delete item
exports.deleteItem = async (req, res) => {
    const { id } = req.params;
    if (!id) return res.status(400).json({ error: "ID is required" });

    try {
        const deleted = await VideosModel.deleteItem(id);
        if (!deleted) {
            return res.status(404).json({ error: "Item not found or already deleted" });
        }
        res.status(200).json({ message: "Item deleted successfully" });
    } catch (err) {
        console.error("Error deleting item:", err.message);
        res.status(500).json({ error: "Failed to delete item", details: err.message });
    }
};

// Controller to delete (deactivate) a notification category
exports.deleteTreatmentVideo = async (req, res) => {
    const { id } = req.params;

    if (!id) {
        return res.status(400).json({ error: 'Missing video ID.' });
    }

    try {
        const video = await VideosModel.getVideoById(id);
        if (!video) {
            return res.status(404).json({ error: 'Video not found.' });
        }

        // מחק את הקובץ מהשרת
        const filePath = path.join(__dirname, '../public', video.image);
        if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
        }

        // מחק מהדאטאבייס
        await VideosModel.deleteVideo(id);

        return res.status(200).json({ message: 'Video deleted successfully.' });
    } catch (err) {
        console.error('Error deleting video:', err.message);
        return res.status(500).json({ error: 'Failed to delete video.' });
    }
};


