
-- --------------------------------------------------------

--
-- Table structure for table `chanting`
--

DROP TABLE IF EXISTS `chanting`;
CREATE TABLE `chanting` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `question_id` int NOT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

--
-- Dumping data for table `chanting`
--

INSERT INTO `chanting` (`id`, `user_id`, `question_id`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 1, 8, 1, '2025-03-18 09:35:46', '2025-03-18 09:35:46'),
(2, 1, 18, 1, '2025-03-18 09:36:03', '2025-03-18 09:36:03');
