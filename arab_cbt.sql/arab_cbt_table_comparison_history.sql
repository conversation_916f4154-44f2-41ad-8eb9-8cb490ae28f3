
-- --------------------------------------------------------

--
-- Table structure for table `comparison_history`
--

DROP TABLE IF EXISTS `comparison_history`;
CREATE TABLE `comparison_history` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `thought_id` int NOT NULL,
  `before_score` float NOT NULL,
  `after_score` float DEFAULT NULL,
  `after_score_audio` float DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `comparison_history`
--

INSERT INTO `comparison_history` (`id`, `user_id`, `thought_id`, `before_score`, `after_score`, `after_score_audio`, `created_at`, `updated_at`) VALUES
(1, 1, 19, 2, 5, 4, '2025-07-30 11:26:29', '2025-07-30 15:21:17'),
(2, 1, 19, 2, 5, 4, '2025-07-30 13:26:14', '2025-07-30 15:21:17'),
(3, 1, 19, 6, 5, 4, '2025-07-30 13:26:36', '2025-07-30 15:21:17'),
(4, 1, 19, 6, 5, 4, '2025-07-30 13:27:47', '2025-07-30 15:21:17'),
(5, 1, 19, 6, 5, 4, '2025-07-30 13:27:49', '2025-07-30 15:21:17'),
(6, 1, 19, 8, 5, 4, '2025-07-30 15:19:01', '2025-07-30 15:21:17'),
(7, 1, 19, 7, NULL, NULL, '2025-07-30 15:25:20', '2025-07-30 15:25:20'),
(8, 1, 19, 7, NULL, NULL, '2025-07-30 15:26:01', '2025-07-30 15:26:01'),
(9, 1, 19, 9, 5, 4, '2025-07-30 15:27:40', '2025-07-30 15:38:19'),
(10, 1, 21, 6, NULL, 1, '2025-07-30 15:50:45', '2025-07-30 15:54:51'),
(11, 1, 21, 4, NULL, NULL, '2025-07-30 16:06:56', '2025-07-30 16:06:56'),
(12, 1, 19, 6, 1, 5, '2025-07-30 16:09:04', '2025-07-30 16:10:25'),
(13, 1, 22, 4, NULL, NULL, '2025-07-30 16:11:08', '2025-07-30 16:11:08'),
(14, 1, 21, 5, 6, NULL, '2025-07-30 16:40:32', '2025-07-30 16:41:22'),
(15, 1, 21, 6, 6, NULL, '2025-07-30 16:42:20', '2025-07-30 16:42:47'),
(16, 1, 19, 7, NULL, NULL, '2025-08-06 22:04:43', '2025-08-06 22:04:43'),
(17, 1, 20, 1, NULL, NULL, '2025-09-08 13:19:34', '2025-09-08 13:19:34');
