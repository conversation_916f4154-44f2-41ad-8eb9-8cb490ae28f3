
-- --------------------------------------------------------

--
-- Table structure for table `user_subscriptions`
--

DROP TABLE IF EXISTS `user_subscriptions`;
CREATE TABLE `user_subscriptions` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `subscription_type` enum('package','item') COLLATE utf8mb4_general_ci NOT NULL,
  `reference_id` int NOT NULL,
  `plan_type` enum('monthly','yearly') COLLATE utf8mb4_general_ci NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `price_paid` decimal(10,2) NOT NULL,
  `currency` varchar(10) COLLATE utf8mb4_general_ci DEFAULT 'USD',
  `payment_method_id` int DEFAULT NULL,
  `payment_reference` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `user_subscriptions`
--

INSERT INTO `user_subscriptions` (`id`, `user_id`, `subscription_type`, `reference_id`, `plan_type`, `start_date`, `end_date`, `price_paid`, `currency`, `payment_method_id`, `payment_reference`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 1, 'package', 3, 'monthly', '2025-08-03', '2025-09-03', 25.00, 'USD', 1, 'PAYPAL-TRANS-ID-12345', 1, '2025-08-03 23:10:40', '2025-08-03 23:10:40');
