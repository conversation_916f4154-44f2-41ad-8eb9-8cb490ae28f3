
-- --------------------------------------------------------

--
-- Table structure for table `features`
--

DROP TABLE IF EXISTS `features`;
CREATE TABLE `features` (
  `id` int NOT NULL,
  `name` varchar(255) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

--
-- Dumping data for table `features`
--

INSERT INTO `features` (`id`, `name`, `created_at`, `updated_at`) VALUES
(1, 'علاج خوف واحد ومحدد', '2025-04-21 18:56:40', '2025-04-21 23:56:04'),
(2, 'علاج جميع المخاوف والأفكار', '2025-04-21 18:58:49', '2025-04-21 23:56:14'),
(3, 'رسائل دعم 3 مرات في اليوم', '2025-04-21 18:59:37', '2025-04-21 23:56:25'),
(4, '33% تخفيض على العلاج النفسي  المسجل ', '2025-04-21 22:34:23', '2025-04-21 23:56:38'),
(5, '33% تخفيض على الكتب', '2025-04-21 22:34:37', '2025-04-21 23:58:18'),
(6, '50% تخفيض على العلاج النفسي  المسجل ', '2025-04-21 22:34:47', '2025-04-21 23:57:34'),
(7, '50% تخفيض على الكتب', '2025-04-21 22:34:55', '2025-04-21 23:58:38'),
(8, 'جلسات الاسترخاء العميق المتقدم ', '2025-04-21 22:35:06', '2025-04-21 23:59:54'),
(9, 'أداة تشتيت الأفكار', '2025-04-21 22:35:14', '2025-04-21 23:59:15'),
(10, 'دورات ومحاضرات مجانية', '2025-04-21 22:35:24', '2025-04-21 23:59:33'),
(11, 'أدوات جديدة ومتجددة باستمرار', '2025-04-21 22:35:55', '2025-04-21 23:59:09'),
(12, 'تخفيض على جميع المنتجات', '2025-04-21 22:36:10', '2025-04-21 23:59:46');
