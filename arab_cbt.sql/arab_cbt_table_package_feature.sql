
-- --------------------------------------------------------

--
-- Table structure for table `package_feature`
--

DROP TABLE IF EXISTS `package_feature`;
CREATE TABLE `package_feature` (
  `package_id` int NOT NULL,
  `feature_id` int NOT NULL,
  `discount_id` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
