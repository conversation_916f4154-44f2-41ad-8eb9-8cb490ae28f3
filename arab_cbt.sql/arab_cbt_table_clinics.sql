
-- --------------------------------------------------------

--
-- Table structure for table `clinics`
--

DROP TABLE IF EXISTS `clinics`;
CREATE TABLE `clinics` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `clinic_name` varchar(255) NOT NULL,
  `address_clinic` varchar(255) NOT NULL,
  `email_clinic` varchar(255) NOT NULL,
  `phone_clinic` varchar(20) NOT NULL,
  `link_whatsapp` varchar(255) DEFAULT NULL,
  `link_telegram` varchar(255) DEFAULT NULL,
  `link_instagram` varchar(255) DEFAULT NULL,
  `link_youtube` varchar(255) DEFAULT NULL,
  `link_facebook` varchar(255) DEFAULT NULL,
  `link_twitter` varchar(255) DEFAULT NULL,
  `link_tiktok` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

--
-- Dumping data for table `clinics`
--

INSERT INTO `clinics` (`id`, `user_id`, `clinic_name`, `address_clinic`, `email_clinic`, `phone_clinic`, `link_whatsapp`, `link_telegram`, `link_instagram`, `link_youtube`, `link_facebook`, `link_twitter`, `link_tiktok`, `created_at`, `updated_at`) VALUES
(3, 1, 'سليم كيال', 'يركا', '<EMAIL>', '0523196012', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2024-11-07 11:07:01', '2025-01-18 02:35:15');
