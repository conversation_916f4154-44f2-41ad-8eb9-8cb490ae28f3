
-- --------------------------------------------------------

--
-- Table structure for table `subscription_packages`
--

DROP TABLE IF EXISTS `subscription_packages`;
CREATE TABLE `subscription_packages` (
  `id` int NOT NULL,
  `name` varchar(100) COLLATE utf8mb4_general_ci NOT NULL,
  `monthly_price` decimal(10,2) NOT NULL,
  `yearly_price` decimal(10,2) NOT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `subscription_packages`
--

INSERT INTO `subscription_packages` (`id`, `name`, `monthly_price`, `yearly_price`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'المحدد', 10.00, 7.99, 1, '2025-04-19 09:58:54', '2025-05-09 10:52:40'),
(2, 'الشامل', 20.00, 15.99, 1, '2025-04-19 10:04:25', '2025-04-19 12:39:52'),
(3, 'متقدم +', 25.00, 19.99, 1, '2025-04-19 10:05:09', '2025-04-19 12:39:53');
