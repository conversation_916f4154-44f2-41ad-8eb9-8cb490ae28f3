
-- --------------------------------------------------------

--
-- Table structure for table `discount_options`
--

DROP TABLE IF EXISTS `discount_options`;
CREATE TABLE `discount_options` (
  `id` int NOT NULL,
  `subscription_type` enum('none','basic','advanced','advanced_plus') NOT NULL,
  `discount_percentage` int NOT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ;

--
-- Dumping data for table `discount_options`
--

INSERT INTO `discount_options` (`id`, `subscription_type`, `discount_percentage`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'basic', 33, 1, '2025-03-12 09:22:02', '2025-03-12 09:22:02'),
(2, 'advanced', 33, 1, '2025-03-12 09:22:11', '2025-03-12 09:22:11'),
(3, 'advanced_plus', 50, 1, '2025-03-12 09:22:22', '2025-03-12 09:22:22');
