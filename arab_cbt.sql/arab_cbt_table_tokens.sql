
-- --------------------------------------------------------

--
-- Table structure for table `tokens`
--

DROP TABLE IF EXISTS `tokens`;
CREATE TABLE `tokens` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `token` varchar(255) NOT NULL,
  `expires_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3;

--
-- Dumping data for table `tokens`
--

INSERT INTO `tokens` (`id`, `user_id`, `token`, `expires_at`, `created_at`, `updated_at`) VALUES
(74, 2, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MiwiaWF0IjoxNzM3MTIzNzg1LCJleHAiOjE3Mzk3MTU3ODV9.41WUz6RMLydsIqJdZZMHhCtjA9Xtx80xy_kry0SrK-k', '2025-01-18 14:23:05', '2025-01-17 14:23:05', '2025-01-17 14:23:05'),
(112, 3, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MywiaWF0IjoxNzM4NDY2MDUzLCJleHAiOjE3NDEwNTgwNTN9.vLkbQf5KF32Tg7_VR8G2x81Iof3g8KC8XLeeLqpsLH8', '2025-02-03 03:14:14', '2025-02-02 03:14:13', '2025-02-02 03:14:13'),
(270, 14, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MTQsImlhdCI6MTc1NDc5MTc0OX0.opzqhnLAngjfWX7MzZy_onykw2puAYkQKsVMK3HMnZw', '2025-08-10 02:09:09', '2025-08-10 02:09:09', '2025-08-10 02:09:09'),
(281, 1, 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MSwicm9sZSI6MSwiaWF0IjoxNzU3NjY5NzkwLCJleHAiOjE3NTc2NzMzOTB9.SxCFQo4dAtdDSfTs6CQ_-B9lf5_CtxMFln7zpX6y-go', '2025-09-12 09:36:30', '2025-09-12 09:36:30', '2025-09-12 09:36:30');
