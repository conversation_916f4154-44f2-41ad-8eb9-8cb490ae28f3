
-- --------------------------------------------------------

--
-- Table structure for table `app_versions`
--

DROP TABLE IF EXISTS `app_versions`;
CREATE TABLE `app_versions` (
  `id` int NOT NULL,
  `platform` enum('android','ios') COLLATE utf8mb4_general_ci NOT NULL,
  `version` varchar(20) COLLATE utf8mb4_general_ci NOT NULL,
  `change_log` text COLLATE utf8mb4_general_ci,
  `is_force_update` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `app_versions`
--

INSERT INTO `app_versions` (`id`, `platform`, `version`, `change_log`, `is_force_update`, `created_at`, `updated_at`) VALUES
(1, 'android', '1.0.0', 'Initial release', 0, '2025-03-27 18:11:31', '2025-03-27 18:11:31'),
(2, 'ios', '1.0.0', 'Initial release', 0, '2025-03-27 18:43:17', '2025-03-27 18:43:17');
