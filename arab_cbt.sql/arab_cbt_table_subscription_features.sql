
-- --------------------------------------------------------

--
-- Table structure for table `subscription_features`
--

DROP TABLE IF EXISTS `subscription_features`;
CREATE TABLE `subscription_features` (
  `id` int NOT NULL,
  `package_id` int NOT NULL,
  `feature_id` int NOT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `subscription_features`
--

INSERT INTO `subscription_features` (`id`, `package_id`, `feature_id`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 1, 1, 1, '2025-04-21 21:39:02', '2025-05-09 10:53:30'),
(3, 1, 3, 1, '2025-04-21 21:43:40', '2025-04-21 22:34:08'),
(4, 1, 4, 1, '2025-04-21 22:36:46', '2025-05-09 10:53:31'),
(5, 1, 5, 1, '2025-04-21 22:36:46', '2025-05-09 10:53:32'),
(6, 2, 2, 1, '2025-04-21 22:37:15', '2025-04-21 23:42:49'),
(7, 2, 4, 1, '2025-04-21 22:37:15', '2025-04-21 22:37:15'),
(8, 2, 5, 1, '2025-04-21 22:37:15', '2025-04-21 22:37:15'),
(9, 2, 8, 1, '2025-04-21 22:37:15', '2025-04-21 23:42:16'),
(10, 2, 12, 1, '2025-04-21 22:37:15', '2025-04-21 23:42:28'),
(11, 3, 2, 1, '2025-04-21 22:38:02', '2025-04-21 22:38:02'),
(12, 3, 3, 1, '2025-04-21 22:38:02', '2025-04-21 22:38:02'),
(13, 3, 6, 1, '2025-04-21 22:38:02', '2025-04-21 22:38:02'),
(14, 3, 7, 1, '2025-04-21 22:38:02', '2025-04-21 22:38:02'),
(15, 3, 8, 1, '2025-04-21 22:38:02', '2025-04-21 22:38:02'),
(16, 3, 10, 1, '2025-04-21 22:38:02', '2025-04-21 22:38:02'),
(17, 3, 11, 1, '2025-04-21 22:38:02', '2025-04-21 22:38:02'),
(18, 3, 12, 1, '2025-04-21 22:38:02', '2025-04-21 22:38:02'),
(19, 3, 9, 1, '2025-04-21 22:38:02', '2025-04-21 22:38:02');
