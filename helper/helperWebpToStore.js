const path = require("path");
const fs = require("fs");
const sharp = require("sharp");

// Create folder synchronously (no callback needed)
function ensureDirSync(dir) {
  try {
    fs.mkdirSync(dir, { recursive: true });
  } catch (e) {
    if (e.code !== "EEXIST") throw e;
  }
}

/**
 * Convert an uploaded file (from Multer) to .webp and store it under /public/uploads/<folder>.
 * @param {object|null} file - Multer file object (e.g., req.files.image[0])
 * @param {string} folder  - Subfolder under /public/uploads
 * @returns {Promise<string|null>} Relative web path like "/uploads/meditation/<name>.webp" or null if no file.
 */
async function toWebpAndStore(file, folder = "meditation") {
  if (!file) return null;

  const uploadDir = path.join(__dirname, "..", "public", "uploads", folder);
  ensureDirSync(uploadDir); // ✅ no callback

  const webpName = `${Date.now()}-${Math.round(Math.random() * 1e9)}.webp`;
  const dest = path.join(uploadDir, webpName);

  // Convert to webp
  await sharp(file.path).webp({ quality: 80 }).toFile(dest);

  // Remove Multer temp file (if any)
  try {
    fs.unlinkSync(file.path);
  } catch (_) {}

  // Return a relative URL that your app serves statically
  return `/uploads/${folder}/${webpName}`;
}

/**
 * Safely delete a previously stored file (given a relative URL like "/uploads/meditation/x.webp")
 */
async function safeUnlink(relPath) {
  try {
    if (!relPath) return;
    // normalize relative URL -> filesystem path
    const normalized = relPath.replace(/^\/+/, ""); // strip leading slash
    const full = path.join(__dirname, "..", "public", normalized);
    await fs.promises.unlink(full);
  } catch (e) {
    if (e.code !== "ENOENT") {
      console.error("safeUnlink error:", e.message);
    }
  }
}

module.exports.ensureDirSync = ensureDirSync;
module.exports.toWebpAndStore = toWebpAndStore;
module.exports.safeUnlink = safeUnlink;