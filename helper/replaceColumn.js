// utils/ddl.js
const pool = require('../config/db');

/**
 * Adds, renames, modifies, or drops a column in a MySQL table.
 * Now supports foreign keys.
 *
 * @param {Object} options
 * @param {string} options.tableName
 * @param {string} [options.oldColumn]
 * @param {string} [options.newColumn]
 * @param {string} [options.newDefinition]  // e.g. "BIGINT UNSIGNED NOT NULL"
 * @param {string} [options.afterColumn]
 * @param {boolean} [options.dropOnly=false]
 * @param {Object}  [options.foreignKey]    // NEW
 * @param {string}  options.foreignKey.referencedTable
 * @param {string}  [options.foreignKey.referencedColumn="id"]
 * @param {string}  [options.foreignKey.constraintName]  // default auto
 * @param {string}  [options.foreignKey.onDelete="RESTRICT"] // CASCADE|SET NULL|RESTRICT|NO ACTION
 * @param {string}  [options.foreignKey.onUpdate="CASCADE"]
 * @param {boolean} [options.foreignKey.index=true] // create index on FK column
 * @param {boolean} [options.foreignKey.dropConstraint=false] // drop FK (when used with dropOnly or modify)
 * @param {boolean} [options.foreignKey.makeNullableFirst=false] // add as NULL then tighten (safer migrations)
 * @param {string}  [options.preTightenUpdateSql] // optional data backfill SQL before NOT NULL tighten
 */
async function replaceOrUpdateColumn({
  tableName,
  oldColumn = '',
  newColumn = '',
  newDefinition = '',
  afterColumn = '',
  dropOnly = false,
  foreignKey,              // NEW
  preTightenUpdateSql,     // NEW
}) {
  const conn = pool; // alias

  const q = async (sql, params=[]) => (await conn.query(sql, params))[0];

  const dbName = (await q('SELECT DATABASE() AS db'))[0].db;

  const colExists = async (t, c) =>
    (await q('SHOW COLUMNS FROM ?? LIKE ?', [t, c])).length > 0;

  const idxExists = async (t, c) =>
    (await q('SHOW INDEX FROM ?? WHERE Column_name = ?', [t, c])).length > 0;

  const fkInfo = async (t, c) =>
    await q(`
      SELECT CONSTRAINT_NAME, REFERENCED_TABLE_NAME, REFERENCED_COLUMN_NAME
      FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ? AND COLUMN_NAME = ? AND REFERENCED_TABLE_NAME IS NOT NULL
    `, [dbName, t, c]);

  const constraintExists = async (t, name) =>
    (await q(`
      SELECT CONSTRAINT_NAME
      FROM INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS
      WHERE CONSTRAINT_SCHEMA = ? AND TABLE_NAME = ? AND CONSTRAINT_NAME = ?
    `, [dbName, t, name])).length > 0;

  try {
    if (!tableName) throw new Error("Missing required 'tableName'");

    const hasAfter = afterColumn ? ` AFTER \`${afterColumn}\`` : '';
    const hasValidDefinition =
      newDefinition &&
      /\b(INT|BIGINT|VARCHAR|CHAR|TEXT|FLOAT|DOUBLE|DECIMAL|BOOLEAN|TINYINT|DATE|DATETIME|TIMESTAMP|ENUM|JSON)/i.test(newDefinition);

    const oldExists = oldColumn ? await colExists(tableName, oldColumn) : false;
    const newExists = newColumn ? await colExists(tableName, newColumn) : false;

    // ---------- Drop-only path ----------
    if (dropOnly) {
      // Drop FK first if requested
      if (foreignKey?.dropConstraint && newColumn) {
        const fks = await fkInfo(tableName, newColumn);
        for (const fk of fks) {
          await q(`ALTER TABLE \`${tableName}\` DROP FOREIGN KEY \`${fk.CONSTRAINT_NAME}\``);
          console.log(`🔗 Dropped FK '${fk.CONSTRAINT_NAME}' on ${tableName}.${newColumn}`);
        }
      }
      if (oldColumn && oldExists) {
        await q(`ALTER TABLE \`${tableName}\` DROP COLUMN \`${oldColumn}\``);
        console.log(`🗑️ Dropped column '${oldColumn}' from '${tableName}'`);
      } else if (oldColumn) {
        console.log(`ℹ️ Column '${oldColumn}' does not exist in '${tableName}', nothing to drop.`);
      }
      return;
    }

    // ---------- Rename ----------
    if (oldExists && !newExists && oldColumn && newColumn && oldColumn !== newColumn && hasValidDefinition) {
      await q(`ALTER TABLE \`${tableName}\` CHANGE \`${oldColumn}\` \`${newColumn}\` ${newDefinition}${hasAfter}`);
      console.log(`✏️ Renamed '${oldColumn}' -> '${newColumn}' on '${tableName}'`);
      // If there is an FK request, handle below after ensuring column exists
    }

    // ---------- Modify ----------
    if (newExists && oldColumn === newColumn && hasValidDefinition) {
      await q(`ALTER TABLE \`${tableName}\` MODIFY COLUMN \`${newColumn}\` ${newDefinition}${hasAfter}`);
      console.log(`🛠️ Modified column '${newColumn}' on '${tableName}'`);
    }

    // ---------- Drop duplicate ----------
    if (oldExists && newExists && oldColumn !== newColumn) {
      await q(`ALTER TABLE \`${tableName}\` DROP COLUMN \`${oldColumn}\``);
      console.log(`🗑️ Dropped duplicate '${oldColumn}' from '${tableName}'`);
    }

    // ---------- Add ----------
    if (!oldColumn && newColumn && !newExists && hasValidDefinition) {
      // Optional: make it nullable first for safer deploys
      if (foreignKey?.makeNullableFirst && !/null/i.test(newDefinition)) {
        const looseDef = newDefinition.replace(/NOT\s+NULL/ig, '').trim() + ' NULL';
        await q(`ALTER TABLE \`${tableName}\` ADD COLUMN \`${newColumn}\` ${looseDef}${hasAfter}`);
        console.log(`➕ Added column '${newColumn}' (temporarily NULLable) to '${tableName}'`);
      } else {
        await q(`ALTER TABLE \`${tableName}\` ADD COLUMN \`${newColumn}\` ${newDefinition}${hasAfter}`);
        console.log(`➕ Added column '${newColumn}' to '${tableName}'`);
      }
    }

    // ---------- Foreign Key handling (optional) ----------
    if (foreignKey && newColumn) {
      const {
        referencedTable,
        referencedColumn = 'id',
        constraintName = `fk_${tableName}_${newColumn}_${referencedTable}_${referencedColumn}`,
        onDelete = 'RESTRICT',
        onUpdate = 'CASCADE',
        index = true,
        dropConstraint = false,
        makeNullableFirst = false,
      } = foreignKey;

      if (!referencedTable) {
        throw new Error("foreignKey.referencedTable is required when foreignKey is provided");
      }

      // Ensure target column exists
      const newNowExists = await colExists(tableName, newColumn);
      if (!newNowExists) throw new Error(`Column '${newColumn}' must exist before creating FK`);

      // Optionally drop existing FK on this column (idempotent-ish)
      if (dropConstraint) {
        const fks = await fkInfo(tableName, newColumn);
        for (const fk of fks) {
          await q(`ALTER TABLE \`${tableName}\` DROP FOREIGN KEY \`${fk.CONSTRAINT_NAME}\``);
          console.log(`🔗 Dropped FK '${fk.CONSTRAINT_NAME}' on ${tableName}.${newColumn}`);
        }
      }

      // Create index (recommended for FK)
      if (index && !(await idxExists(tableName, newColumn))) {
        await q(`ALTER TABLE \`${tableName}\` ADD INDEX \`idx_${tableName}_${newColumn}\` (\`${newColumn}\`)`);
        console.log(`📇 Created index on ${tableName}.${newColumn}`);
      }

      // If constraint not present, add it
      if (!(await constraintExists(tableName, constraintName))) {
        await q(`
          ALTER TABLE \`${tableName}\`
          ADD CONSTRAINT \`${constraintName}\`
          FOREIGN KEY (\`${newColumn}\`)
          REFERENCES \`${referencedTable}\`(\`${referencedColumn}\`)
          ON DELETE ${onDelete}
          ON UPDATE ${onUpdate}
        `);
        console.log(`🔗 Added FK '${constraintName}' (${tableName}.${newColumn} → ${referencedTable}.${referencedColumn})`);
      } else {
        console.log(`✅ FK '${constraintName}' already exists on '${tableName}'`);
      }

      // Tighten NULLability after backfill (safe migrations)
      if (makeNullableFirst && /NOT\s+NULL/i.test(newDefinition)) {
        if (preTightenUpdateSql) {
          await q(preTightenUpdateSql);
          console.log('🧹 Ran pre-tighten backfill SQL');
        }
        await q(`ALTER TABLE \`${tableName}\` MODIFY COLUMN \`${newColumn}\` ${newDefinition}`);
        console.log(`🔒 Tightened '${newColumn}' to '${newDefinition}'`);
      }
    }

    // ---------- No-op message ----------
    if (!hasValidDefinition && !foreignKey) {
      console.warn(`⚠️ Skipped: no valid definition and no foreignKey work for '${tableName}'`);
    }
  } catch (err) {
    console.error('❌ Error in replaceOrUpdateColumn:', err.message);
    throw err;
  }
}

module.exports = { replaceOrUpdateColumn };