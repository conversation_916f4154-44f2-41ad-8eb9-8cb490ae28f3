{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "nodemon index.js", "start": "pm2 start index.js --name ArabCBT"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@paypal/checkout-server-sdk": "^1.0.3", "bcryptjs": "^2.4.3", "child_process": "^1.0.2", "compression": "^1.7.5", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.21.0", "express-session": "^1.18.0", "express-validator": "^7.2.0", "firebase-admin": "^13.2.0", "fluent-ffmpeg": "^2.1.3", "helmet": "^8.0.0", "http": "^0.0.1-security", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "mysql2": "^3.11.3", "node-cron": "^3.0.3", "nodemailer": "^6.9.15", "nodemon": "^3.1.4", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "path": "^0.12.7", "sequelize": "^6.37.5", "sharp": "^0.33.5", "uuid": "^11.0.5", "winston": "^3.17.0"}}